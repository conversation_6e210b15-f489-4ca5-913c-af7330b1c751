/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author: liufeng Date: 2018/6/6 17:47 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.core.constants;

import java.io.File;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ArrayBlockingQueue;

/**
 * Description:  算法常量类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class Constants {

    public final static String SEPARATOR = File.separator;

    public static  final  String PROVINCE_DEFAULT_INDEX = "province_default_index";

    public static  final  String CITY_DEFAULT_INDEX = "city_default_index";

    /**
     * 时间范围分隔符
     */
    public static final String DATE_SEPARATOR_PUNCTUATION = "~";

    /**
     * 文件编码格式 稳定度算法用到  其他算法用的是GB2312
     */
    public final static String ENCODING = "UTF-8";

    /**
     * 授权文件的路径（所有的都用同一个授权文件）
     */
    public final static String LICENCE_PATH = "LICENCE.PATH";


    /**
     * 稳定度算法名称
     */
    public final static String PRECISION_NAME = "precision";

    /**
     * 稳定度分析控制参数写入文件
     */
    public final static String PRECISION_IN_FILE_NAME = "precision.ctr";

    /**
     * 存放历史数据的文件
     */
    public final static String PRECISION_IN_DATA_NAME = "load.txt";

    /**
     * 算法输出结果（分量）存放文件
     */
    public final static String PRECISION_OUT_LOAD_DATA_NAME = "loadDivision.out";

    /**
     * 算法输出结果(上下限) 存放文件名称
     */
    public final static String PRECISION_OUT_DATA_NAME = "precision.out";

    /**
     * 调试模式
     */
    public final static String IsDebug = "ALGORITHM.DEBUG";

    public final static String EMPTY = " ";


    //======================================台风预测算法===============
    public final static String TYPHOON_IN = "TYPHOON.IN.PATH";

    public final static String TYPHOON_RUN = "TYPHOON.RUN.PATH";

    public final static String TYPHOON_OUT = "TYPHOON.OUT.PATH";


    //预测算法的freemarker的模板名称
    public final static String FORECAST_TEMPLATE_NAME = "forecast";

    //模型融合算法模板freemarker名称
    public final static String MF_TEMPLATE_NAME = "modelfusion";


    //超短期预测算法模板名称96
    public final static String SHORT_FORECAST_TEMPLATE_96_NAME = "shortForecast96";

    //超短期预测算法模板名称288
    public final static String SHORT_FORECAST_TEMPLATE_288_NAME = "shortForecast288";

    //LGB算法模板freemarker名称
    public final static String LGB_TEMPLATE_NAME = "replenish";

    //预测算法的freemarker的模板名称
    public final static String SENSITIVITY_TEMPLATE_NAME = "sensitivity";


    public final static String TYPHOON_TEMPLATE_NAME = "typhoon";

    // 基准日

    public final static String BASEDAY = "BaseDay";

    public final static String CITYNAME = "CityName";

    //预测起始日
    public final static String FORECAST_START_DAY = "ForecastStarDay";

    //是否使用多线程
    public final static String IS_MULTI_THREAD = "IsMultiThread";

    //节假日算法0619版本新增预测模式
    public final static String MODE = "Mode";

    //预测结束日
    public final static String FORECAST_END_DAY = "ForecasetEndDay";

    //采集的历史负荷
    public final static String HIS_LOAD_CLCT = "LoadClctHisList";

    //历史负荷
    public final static String HIS_LOAD = "LoadHisList";

    //SVM算法的预测结果
    public final static String SVM_FC ="SvmFcList";

    //Xg算法的预测结果
    public final static String XG_FC ="XgFcList";

    //ANN算法的预测结果
    public final static String ANN_FC ="AnnFcList";

    //综合气象算法的预测结果
    public final static String C_ANN_FC  ="CAnnFcList";


    //综合气象算法的预测结果
    public final static String GRU_FC  ="GRUFcList";


    //综合气象算法的预测结果
    public final static String MXRH_FC  ="MXRHFcList";


    //标典算法的预测结果
    public final static String SVM_LARGE_FC ="BdFcList";

    //LightGBM算法的预测结果
    public final static String LGB_FC ="LightFcList";

    public final static String START_WITH_ZERO = "startWithZero";


    //湿度
    public final static String HUMIDITY_DATA = "HumidityList";

    //温度
    public final static String TEMPERATURE_DATA = "TemperatureList";

    //实感温度 xg补充算法专用
    public final static String EFFECTIVE_TEMPERATURE = "ApparentTemperatureList";

    // 焓值  xg补充算法专用
    public final static String ENTHALPY_VALUE = "EnthalpyList";


    //降水
    public final static String PRECIPITATION_DATA = "PrecipitationList";

    //风速
    public final static String WIND_DATA = "WindList";

    //调休日期
    public final static String OFFDATES = "OffDates";

    //节假日信息
    public final static String HOLIDAY_INFO = "HolidayInfo";

    //近两年强台风日期
    public final static String TYPHOON_DATAS = "TyphoonDatas";

    //每日最高温度
    public final static String MAX_TEMPERATURE = "MaxTemperatureList";

    //算法类型
    public final static String ALGORITHM_TYPE = "AlgorithmType";

    public final static String NEW_POINT = "Point";

    /**
     * 超短期 预测起始日期
     */
    public final static String FORECAST_BEGIN_DAY = "ForecastBeginDay";

    /**
     * 超短期 时间间隔
     */
    public final static String TIME_SPAN = "TimeSpan";

    /**
     * 超短期 预测起始点数
     */
    public final static String FORECAST_BEGIN_POINT = "ForecastBeginPoint";

    /**
     * 超短期 预测点数
     */
    public final static String FORECAST_POINT = "ForecastPoint";

    /**
     * 算法输入文件的名称
     */
    public final static String MULTIMETHOD_IN_FILE_NAME = "FILE_IN.e";

    /**
     * 算法输出文件的名称
     */
    public final static String MULTIMETHOD_OUT_NAME = "FILE_OUT.e";

    /**
     * message文件
     */
    public final static String MESSAGE_NAME = "message.e";


    /**
     * e文件名称
     */
    public final static String ForecastEtableName = "ForecastLoad";

    public final static String ModifyLoadEtableName = "ModifiedLoad";

    public final static String SimilarEtableName = "SimilarDay";

    public final static String SensitivityEtableName = "Sensitivityalgorithm";


    /**
     * ----------------正常日预测算法相关路径---------------------------------
     */

    public final static String FORECAST_IN_PATH = "FORECAST.IN.PATH";

    public final static String FORECAST_OUT_PATH = "FORECAST.OUT.PATH";

    public final static String FORECAST_RUN_PATH = "FORECAST.RUN.PATH";

    /**
     * ----------------超短期预测算法相关路径---------------------------------
     */
    public final static String SHORT_IN_PATH = "SHORT.IN.PATH";

    public final static String SHORT_OUT_PATH = "SHORT.OUT.PATH";

    public final static String SHORT_RUN_PATH = "SHORT.RUN.PATH";

    /**
     * ----------------LGB算法相关路径---------------------------------
     */

    public final static String LGB_IN_PATH = "LGB.IN.PATH";

    public final static String LGB_OUT_PATH = "LGB.OUT.PATH";

    public final static String LGB_RUN_PATH = "LGB.RUN.PATH";

    /**
     * ----------------节假日预测算法相关路径---------------------------------
     */
    public final static String HOLIDAY_IN = "HOLIDAY.IN.PATH";

    public final static String HOLIDAY_RUN = "HOLIDAY.RUN.PATH";

    public final static String HOLIDAY_OUT = "HOLIDAY.OUT.PATH";


    /*=============================数据清洗========================*/

    public final static String PREPROCESS_IN_PATH = "PREPROCESS.IN.PATH";

    public final static String PREPROCESS_RUN_PATH = "PREPROCESS.RUN.PATH";

    public final static String PREPROCESS_OUT_PATH = "PREPROCESS.OUT.PATH";


    /*=============================稳定度算法========================*/
    public final static String PRECISION_IN = "PRECISION.IN.PATH";

    public final static String PRECISION_RUN = "PRECISION.RUN.PATH";

    public final static String PRECISION_OUT = "PRECISION.OUT.PATH";

    /*=============================相似性日算法========================*/
    public final static String SIMILARITY_IN_PATH = "SIMSILARITY.IN.PATH";

    public final static String SIMILARITY_OUT_PATH = "SIMSILARITY.OUT.PATH";

    public final static String SIMILARITY_RUN_PATH = "SIMSILARITY.RUN.PATH";

    /*=============================灵敏度算法========================*/
    public final static String SENSITIVITY_IN_PATH = "SENSITIVITY.IN.PATH";

    public final static String SENSITIVITY_RUN_PATH = "SENSITIVITY.RUN.PATH";

    public final static String SENSITIVITY_OUT_PATH = "SENSITIVITY.OUT.PATH";

    /*=============================分布式光伏算法========================*/
    public final static String SMALL_POWER_IN_PATH = "SMALL_POWER.IN.PATH";

    public final static String SMALL_POWER_RUN_PATH = "SMALL_POWER.RUN.PATH";

    public final static String SMALL_POWER_OUT_PATH = "SMALL_POWER.OUT.PATH";


    /*=============================光伏辐照度占比算法========================*/
    public final static String PV_RATIO_IN_PATH = "PV_RATIO.IN.PATH";

    public final static String PV_RATIO_RUN_PATH = "PV_RATIO.RUN.PATH";

    public final static String PV_RATIO_OUT_PATH = "PV_RATIO.OUT.PATH";



    /*
     *
     * 省会城市Type
     * */
    public static final Integer PROVINCE_TYPE=1;

    /*
     * 地市城市Type
     * */
    public static final  Integer CITY_TYPE = 2;




    /**
     * 省份id为1
     */
    public static final String PROVINCE_ID = "1";

    public static final String GUANGZHOU_ID = "2";


    /**
     * 系统负荷口径：统调负荷+分布式光伏
     */
    public static final String SYSTEM_LOAD_CALIBER ="10";


    /**
     * 省份名称
     */
    public static final String PROVINCE_NAME = "广东";

    /**
     * 省会id
     */
    public static final String CAPITAL_ID = "2";

    /**
     * 默认口径id
     */
    public static final String DEFAULT_CALIBER_ID = "1";

    /**
     * session中的系统时间,保存的类型是Date
     */
    public static final String SYSTEM_DATE = "SYSTEM_DATE";

    public static final String FORECAST_DATE = "forecast_date";

    public static final String FORECAST_COUNT = "forecast_count";


    /**
     * session中的用户ID,保存的类型是String
     */
    public static final String USER_ID = "USER_ID";

    /**
     * session中的城市ID,保存的类型是String
     */
    public static final String CITY_ID = "CITY_ID";

    /**
     * session中的自动预测算法ID,保存的类型是String
     */
    public static final String AUTO_FORECAST_ALGORITHM_ID = "AUTO_FORECAST_ALGORITHM_ID";

    /**
     * session中的默认口径id
     */
    public static final String CALIBER_ID = "CALIBER_ID";

    // token密匙
    public final static String tokenKey = "base64EncodedSecretKey";

    // token标识
    public final static String TOKEN = "load-token";

    /**
     * 年月日标识
     */
    public final static Integer DAY = 1;

    public final static Integer MONTH = 2;

    public final static Integer YEAR = 3;

    /**
     * 系统默认小数位数
     */
    public final static Integer DEFAULT_DIGITS = 0;

    /**
     * 负荷小数位数
     */
    public final static Integer LOAD_DIGITS = 2;

    /**
     * 气象小数位数
     */
    public final static Integer WEATHER_DIGITS = 2;

    /**
     * 百分数小数位
     */
    public final static Integer PERCENT_DIGITS = 1;


    private static BigDecimal QUALIFIED = BigDecimal.valueOf(0.97);

    public static Queue<Map<String, Object>> queue = new ArrayBlockingQueue<Map<String, Object>>(1024);

    public final static String PROVINCE = "1";

    public final static String GUANGDONG = "2";

    /**
     * 排序
     */
    public static final Integer DAY_ASC = 1;

    public static final Integer DAY_DESC = -1;

    public static final Integer TEMP_ASC = 2;

    public static final Integer TEMP_DESC = -2;

    public static final Integer RAIN_ASC = 3;

    public static final Integer RAIN_DESC = -3;


    /**
     * 城市前缀,具体key的方式为“dict.city.belongId.cityId”
     */
    public static final String CACHE_ID_CITY_PREFIX = "lf.city.";

    /**
     * 算法前缀
     */
    public static final String CACHE_ID_ALGORITHM_PREFIX = "lf.algorithm.";

    /**
     * 算法参数前缀
     */
    public static final String CACHE_ID_ALGORITHM_PARAM_PREFIX = "lf.algorithmParam.";

    /**
     * 行业前缀
     */
    public static final String CACHE_ID_INDUSTRY_PREFIX = "lf.industry.";

    /**
     * 气象前缀
     */
    public static final String CACHE_ID_WEATHER_PREFIX = "lf.weather.";

    /**
     * 系统设置前缀
     */
    public static final String CACHE_ID_SETTING_SYSTEM_PREFIX = "lf.settingSystem.";

    /**
     * 数据字典分类前缀
     */
    public static final String CACHE_ID_DICTIONARY_CATEGORY_PREFIX = "lf.dictionaryCategory.";

    /**
     * 数据字典数据项前缀
     */
    public static final String CACHE_ID_DICTIONARY_ITEM_PREFIX = "lf.dictionaryItem.";

    /**
     * 口径前缀
     */
    public static final String CACHE_ID_CALIBER_PREFIX = "lf.caliber.";


    /**
     * 负荷曲线是否从0点开始,true是从00:00到23:45；false是从00:15到24:00；null表示从00:00到24:00，共97个点
     */
    public static final Boolean LOAD_CURVE_START_WITH_ZERO = true;

    /**
     * 日负荷曲线中的负荷点数
     */
    public static final int LOAD_CURVE_POINT_NUM = 96;

    /**
     * 气象曲线是否从0点开始,true是从00:00到23:45；false是从00:15到24:00；null表示从00:00到24:00，共97个点
     */
    public static final Boolean WEATHER_CURVE_START_WITH_ZERO = true;

    /**
     * 气象曲线中的负荷点数
     */
    public static final int WEATHER_CURVE_POINT_NUM = 96;

    /**
     * 负荷曲线时段列表
     */
    public static final List<String> DAY_LOAD_CURVE_COLUMNS = ColumnUtil
        .getColumns(LOAD_CURVE_POINT_NUM, LOAD_CURVE_START_WITH_ZERO, true);

    /**
     * 负荷预测表的表名
     */
    public static final String LOAD_TABLE = "load_city_his_basic";

    /**
     * 人工决策算法ID
     */
    public static final String MD_ALGORITHM_ID = "0";


    public static final String TYPHOON_ALGORITHM_ID = "11";

    /**
     * 计算中Bigdecimal保留的小数位
     */
    public static final int SCALE = 4;


    /**
     * 免考申请的状态是已经审批
     */
    public static final Integer CHECK_STATUS = 1;
    /**
     * 免考申请的状态是未审批
     */
    public static final Integer NOT_CHECK = 0;

    /**
     * 免考申请的状态是审批未通过
     */
    public static final Integer CHECK_NOT_PASS = 2;

    /**
     * 免考申请的状态是过期
     */
    public static final Integer CHECK_OVERDUE = 3;
    /**
     * 免考申请的状态是删除
     */
    public static final Integer CHECK_DELETE = 4;
    /**
     * 免考申请的状态是删除
     */
    public static final Integer CHECK_WITH_DRAW = 5;
    //免考附件存储目录
    public static final String CHECK_FILE_PATH = "/home/<USER>/Public/run/service/service-job/fujian";
    public static final String CHECK_FILE_Suffix_DOC = "doc";

    public static final String CHECK_FILE_Suffix_DOCX = "docx";

    //节假日算法执行预测model
    public static final Integer FORECAST_MODEL = 1;

    public static final Integer TRAIN_MODEL = 0;

    /**
     * 正序
     */
    public final static Integer ASC = 0;

    /**
     * 倒叙
     */
    public final static Integer DESC = 1;
    /**
     * 手动预测
     */
    public final static Integer FORECAST_TYPE = 1;

    /**
     * 上报设置 日期类型 正常日
     */
    public final static Integer NORMAL_DAY = 0;

    /**
     * 节假日
     */
    public final static Integer HOLIDAY = 1;

    /**
     * 超短期
     */
    public final static Integer SHORT_DAY = 2;


    /**
     * 综合相似 近几日气象条件开关
     */
    public static final Integer CompositeOn = 1;

    /**
     * 通用条件开关
     */
    public static final String STRING_COMPOSITE_ON = "1";

    /**
     * 综合模型算法比例key值
     */
    public static final String SCALE_COMPREHENSIVE_MODEL = "scale_comprehensive_model";


    /**
     * 首页
     */
    public static final String INDEX_PARENT = "Index";


    /**
     * 人工修正
     */
    public static final String MANUAL_PARENT = "RevisedReport";

    /**
     * 预测分析
     */
    public static final String FORECAST_ANALYSIS_PARENT = "ForeCastAnalysis";

    /**
     * 负荷分析
     */
    public static final String LOAD_ANALYSIS_PARENT = "LoadAnalysis";

    /**
     * 气象灵敏度分析
     */
    public static final String METEOROLOGY_SENSITIVITY_ANALYSIS_PARENT = "SensitivityAnalysis";

    /**
     * 稳定度分析
     */
    public static final String STABILITY_ANALYSIS_PARENT = "StabilityAnalysis";

    /**
     * 模型管理
     */
    // public static final String MODEL_MANAGEMENT_PARENT = "VirtualPrediction";
    public static final String MODEL_MANAGEMENT_PARENT = "SystemSettingPage";

    /**
     * 高级预测
     */
    public static final String ADVANCED_FORECAST_PARENT = "AdvancedForecast";

    /**
     * 正常日预测
     */
    public static final String VIRTUAL_PREDICTION_PARENT = "AutoForecast";

    /**
     * 节假日预测
     */
    public static final String HOLIDAY_PREDICTION_PARENT = "HolidayForecast";

    /**
     * 预测后评估
     */
    public static final String PREDICTION_EVALUATION_PARENT = "PredictionEvaluation";

    /**
     * 考核管理
     */
    public static final String CHECK_MANAGEMENT_PARENT = "CheckManagement";

    /**
     * 准确率统计
     */
    public static final String ACCURATE_STATISTICS_PARENT = "AccurateStatistics";

    /**
     * 免考审批
     */
    public static final String CHECK_EXEMPT_APPROVE_PARENT = "CheckApproval";

    /**
     * 免考申请
     */
    public static final String CHECK_EXEMPT_APPLICATION_PARENT = "CheckApply";

    /**
     * 数据管理
     */
    public static final String DATA_MANAGEMENT_PARENT = "DataManagement";

    /**
     * 数据清洗
     */
    public static final String DATA_REFRESH_PARENT = "DataRefresh";

    /**
     * 数据编辑
     */

    // public static final String DATA_EDIT_PARENT = "DataEdit";
    public static final String DATA_EDIT_PARENT = "DataManagement";

    /**
     * 数据查询
     */
    //public static final String DATA_QUERY_PARENT = "DataQuery";
    public static final String DATA_QUERY_PARENT = "DataManagement";

    /**
     * 误差分析
     */
    // public static final String DEVIATION_ANALYSIS_PARENT = "Analysis";
    public static final String DEVIATION_ANALYSIS_PARENT = "DataManagement";

    /**
     * 极端天气汇总
     */
    //public static final String TYPHOON_WEATHER_PARENT = "ExtremeWeather";
    public static final String TYPHOON_WEATHER_PARENT = "SystemSettingPage";

    /**
     * 极端天气分析
     */
    //public static final String EXTREME_WEATHER_ANALYSIS = "ExtremeWeatherAnalysis";
    public static final String EXTREME_WEATHER_ANALYSIS = "SystemSettingPage";

    /**
     * 台风分析
     */
    public static final String EXTREME_WEATHER_TYPHON = "ExtremeWeatherTyphoon";

    /**
     * 实施页面
     */
    public static final String IMPLEMENT_PAGE = "Implementation";


    /**
     * 日负荷曲线中的负荷点数
     */
    public static final int LOAD_CURVE_POINT_NUM_24 = 24;

    /**
     * 气象曲线中的负荷点数
     */
    public static final int WEATHER_CURVE_POINT_NUM_24 = 24;

    //综合相似模板参数
    public static final String ModelType = "ModelType";

    //相似日天数
    public static final String SimilarDayNum = "SimilarDayNum";

    public static final String HistoryLoad = "HistoryLoad";

    public static final String Temperature = "Temperature";

    public static final String Precipitation = "Precipitation";

    public static final String Humidity = "Humidity";

    /**
     * 系统通用分隔符
     */
    public static final String SEPARATOR_PUNCTUATION = ",";

    /**
     * 系统通用分隔符
     */
    public static final String PUNCTUATION = "-";

    public static final String TONGDIAO = "1";


    public static final String ZHONGDIAO = "2";


    public  final  static  String  isAlgo109="isAlgo109";

    public final  static  String  isAlgo106="isAlgo106";

    public  final  static  String  isAlgo105="isAlgo105";

    public final  static  String  isAlgo807="isAlgo807";

    public final  static  String  isAlgo119="isAlgo119";

    public final  static  String  isAlgo808="isAlgo808";

    public  final  static  String  threshold="threshold";

    /**
     * 亮度设置
     */
    public  static  final  String  colorStr ="color_str";

    /**
     * 网状设置
     */
    public  static  final  String isOpenReseau="open_reseau";


    public  static  final  String OpenReseau="1";


    public static final String ZYT = "1";

    public static final String GDT = "2";

    public static final String ZH = "3";

    public static final String JT = "4";

    public static final String HIS = "5";




    /**
     *
     * 特殊算法type
     */
    public static  final  String SPECIAL_ALGORITHM_TYPE="3";

    /**
     *
     * 节假日算法type
     */
    public static  final  String HOLIDAY_ALGORITHM_TYPE="2";

    /**
     *
     * 正常日算法type
     */
    public static  final  String NORMAL_ALGORITHM_TYPE="1";


    /**
     *
     * 通用算法type
     */
    public static  final  String COMMON_ALGORITHM_TYPE="0";

    /**
     * 发送预测数据类型 节调
     */
    public static final String SEND_FC_LOAD_TYPE_JD = "1";


    public static final String DOWNLOAD_FC_LOAD_TYPE_JD = "2";



    /**
     * 粤西包含城市id
     */
    public static final Integer[] WEST_LIST = {2,4,6,7,8,9,10,11,16,17,19,22,23};

    /**
     * 粤东包含城市id
     */
    public static final Integer[] EAST_LIST = {3,5,12,13,14,15,18,20,21};

    /**
     * 粤东城市id
     */
    public static final String EAST_ID = "99";

    /**
     * 粤西城市id
     */
    public static final String WEST_ID = "100";

    /**
     * 考虑分布式光伏系统预测算法id
     */
    public static final String ALGORITHM_DP_ID = "25";

    public static final String MONTH_WAIVE = "month_waive";
}
