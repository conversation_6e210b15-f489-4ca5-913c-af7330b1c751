package com.tsintergy.lf.serviceapi.base.report.api;

import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAccuracyDayBatchDO;

import java.util.Date;
import java.util.List;

/** @Description
 * <AUTHOR>
 * @Date 2025/2/18 14:26
 **/
public interface ReportAccuracyDayBatchService {

    /**
     * 统计日负荷填报准确率
     *
     * @param cityId
     * @param startDate
     * @param endDate
     * @return
     * @throws Exception
     */
    List<ReportAccuracyDayBatchDO> doStatSaveReportAccuracy(String cityId, String caliberId, Date startDate, Date endDate) throws Exception;

    void doSaveOrUpdate(List<ReportAccuracyDayBatchDO> reportAccuracyDayVOS) throws Exception;

    List<ReportAccuracyDayBatchDO> getBatchReportAccuracy(Integer batchId, String cityId, String caliberId, String algorithmId, Date startDate, Date endDate, String startTime, String endTime) throws Exception;

    List<ReportAccuracyDayBatchDO> getBatchReportAccuracyByAlgorithm(Integer batchId, String cityId, String caliberId, String algorithmId, Date startDate, Date endDate, String startTime, String endTime) throws Exception;
}
