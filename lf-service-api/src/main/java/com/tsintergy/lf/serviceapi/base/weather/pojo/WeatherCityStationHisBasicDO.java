package com.tsintergy.lf.serviceapi.base.weather.pojo;

import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "weather_city_station_his_basic")
public class WeatherCityStationHisBasicDO extends BaseWeatherDO {

    public WeatherCityStationHisBasicDO(){
        super();
    }

    public WeatherCityStationHisBasicDO(String cityId, Integer type, java.util.Date date){
        super( cityId,  type, date);
    }
}