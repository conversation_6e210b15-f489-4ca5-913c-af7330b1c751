package com.tsintergy.lf.serviceapi.base.weather.pojo;

import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Load;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Weather;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.base.pojo.Base96DO;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;

@Data
@Entity
@Table(name ="weather_station_his_clct")
@EntityUniqueIndex({"date","stationId","type"})
public class WeatherStationHisClctDO extends Base96DO implements Weather, Load {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    /**
     * 日期
     */
    @Column(name = "date")
    private Date date;

    @Column(name = "station_Id")
    private String stationId;

    @Column(name = "type")
    private Integer type;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;


    @Override
    public List<BigDecimal> getloadList() {
        return BasePeriodUtils.toList(this, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
    }

    @Override
    public List<BigDecimal> getWeatherList() {
        return BasePeriodUtils.toList(this, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
    }
}
