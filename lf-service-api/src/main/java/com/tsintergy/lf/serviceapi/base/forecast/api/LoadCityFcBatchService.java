package com.tsintergy.lf.serviceapi.base.forecast.api;

import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcBatchDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/2/3 14:29
 */
public interface LoadCityFcBatchService {

    void save(LoadCityFcDO loadCityFcDO);

    List<LoadCityFcBatchDO> selectByCondition(String cityId, Date date, String caliberId, String algorithmId);

    public List<LoadCityFcBatchDO> findBatchFc(String cityId, String caliberId, Date startDate, Date endDate, String algorithmId);

    LoadCityFcBatchDO findOneByConditionByBatchId(String cityId, Date date, String caliberId,
        String algorithmId, String batchId, Integer day) throws Exception;

}
