package com.tsintergy.lf.serviceapi.base.evalucation.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2025-05-07
 * @since 1.0.0
 */
@Data
@ApiModel("准确率统计结果对象；")
public class AccuracyAssessUnitDTO {


    /**
     * 考核点名称
     */
    private String assessName;

    /**
     * 考核点准确率
     */
    @ApiModelProperty("日准确率")
    private BigDecimal assessAccuracy;

    /**
     * 考核点准确率
     */
    @ApiModelProperty("预测回溯准确率")
    private BigDecimal assessAccuracyRecall;

    @ApiModelProperty("偏差")
    private BigDecimal deviation;

    @BigdecimalJsonFormat(percentConvert = 100, scale = 2)
    @ApiModelProperty(value = "回溯提升", example = "0.96")
    private BigDecimal recallDeviation;

    /**
     * 日预测极值负荷
     */
    @ApiModelProperty("日预测极值负荷")
    private BigDecimal dailyExtremumFc;

    /**
     * 日预测极值负荷
     */
    @ApiModelProperty("日实际极值负荷")
    private BigDecimal dailyExtremumHis;

    /**
     * 考核点极值类型；1最大 2最小 3平均
     */
    private Integer type;

    /**
     * 排序使用
     */
    private Date date;

    private Integer sortNo;

    public AccuracyAssessUnitDTO(String assessName, BigDecimal assessAccuracy, BigDecimal assessAccuracyRecall,
        BigDecimal recallDeviation, Date date) {
        this.assessName = assessName;
        this.assessAccuracy = assessAccuracy;
        this.assessAccuracyRecall = assessAccuracyRecall;
        this.recallDeviation = recallDeviation;
        this.date = date;
    }
}

