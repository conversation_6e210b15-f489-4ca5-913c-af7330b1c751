package com.tsintergy.lf.serviceapi.base.weather.api;

import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcLoadForecastDO;
import java.util.Date;
import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2025-05-08
 * @since 1.0.0
 */
public interface WeatherCityFcLoadForecastService {

    List<WeatherCityFcLoadForecastDO> findWeatherFcLoadForecastBatchByType(String cityId,
        String algorithmId,String caliberId, String batchId, Integer type,  Date startDate, Date endDate) throws Exception;

    void insertOrUpdateWeatherInfo(String cityId, Date date, String systemAlgorithmId, String caliberId);

    List<WeatherCityFcLoadForecastDO> findWeatherInfoFcLoadForecast(String cityId, Integer type, String algorithmId,
        Date date, String caliberId) throws Exception;

    void doCreateAndFlush(WeatherCityFcLoadForecastDO weatherCityFcLoadForecastDO);

}
