
package com.tsintergy.lf.serviceapi.base.weather.api;


import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherDeatilDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherNameDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.BaseWeatherDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $Id: WeatherCityFcService.java, v 0.1 2018-01-31 10:59:49 tao Exp $$
 */

public interface WeatherCityFcService extends BaseFcWeatherService{
     /**
     *  search entity list
     * @param param
     * @return
     * @throws BusinessException
     */
    DataPackage queryWeatherCityFcDO(DBQueryParam param) throws Exception;

    /**
     * 功能描述:<br>
     * @param cityId
     * @param type
     * @param startDate
     * @param endDate
     * @Return: {@link List< WeatherDTO>}
     * @Version: 1.0.0
     * @Author:<EMAIL>
     * @Date: 2021/10/20 9:43
     */
    List<WeatherDTO> findWeatherCityFcDTOs(String cityId, Integer type, Date startDate, Date endDate) throws Exception;

    /**
     * create entity
     * @param vo
     * @return
     * @throws Exception
     */
     WeatherCityFcDO doCreate(WeatherCityFcDO vo) throws Exception;

    /**
     *  delete entity by object
     * @param vo
     * @throws Exception
     */
     void doRemoveWeatherCityFcDO(WeatherCityFcDO vo) throws Exception;

    /**
     * delete entity by PK
     * @param pk
     * @throws Exception
     */
     void doRemoveWeatherCityFcDOByPK(Serializable pk) throws Exception;

    /**
     * update entity object
     * @param vo
     * @return
     * @throws Exception
     */
     WeatherCityFcDO doUpdateWeatherCityFcDO(WeatherCityFcDO vo) throws Exception;

    /**
     * find entity by PK
     * @param pk
     * @return
     * @throws Exception
     */
     WeatherCityFcDO findWeatherCityFcDOByPk(Serializable pk) throws Exception;

    /**
     * 查询气象预测数据
     * @param cityId 城市ID
     * @param type 气象类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return
     */
    List<BaseWeatherDO> findWeatherCityFcDOs(String cityId, Integer type, Date startDate, Date endDate) throws Exception;

    /**
     * 根据气象源查询气象预测数据
     * @param cityId 城市ID
     * @param type 气象类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param weatherSource 气象源
     * @return
     */
    List<BaseWeatherDO> findWeatherCityFcDOs(String cityId, Integer type, Date startDate, Date endDate,String weatherSource) throws Exception;

    <T extends BaseWeatherDO> List<T> getWeatherDataFromWeatherTypes(String cityId, Integer type, Date startDate, Date endDate,String weatherType) throws Exception;

    DataPackage queryWeatherCityFcDO(DBQueryParam param,String weatherType) throws Exception;

    BaseWeatherDO doCreate(BaseWeatherDO vo,String weatherType) throws Exception;

    BaseWeatherDO doUpdateBaseWeatherDO(BaseWeatherDO vo,String weatherType) throws Exception;



    public <T extends BaseWeatherDO> List<T> getWeatherDataFromSettings(String cityId, Integer type, Date startDate, Date endDate) throws Exception;
    /**
     * 查询該城市目标日的所有基础气象数据
     * @param cityId
     * @param targetDate
     * @return
     * @throws Exception
     */
    List<WeatherNameDTO> findAllByDateAndCityId(String cityId, Date targetDate) throws Exception;



    void doInsertOrUpdate(WeatherCityFcDO weatherCityFcVO) throws Exception;

    void doInsertOrUpdate(BaseWeatherDO baseWeatherDO,String weatherType) throws Exception;


    List<BigDecimal> find96WeatherCityFcValue(Date date, String cityId, Integer type) throws Exception;


    WeatherDeatilDTO findWeatherDeatilDTO(Date date, String cityId) throws Exception;

    List<WeatherCityFcDO> findWeatherCityFcDOs(String cityId, Date date, Date date1) throws Exception;

    List<WeatherCityFcDO> findWeatherCityHisDOs(List<String> cityIds, Integer type, Date hisStartDate, Date endDate);
}