package com.tsintergy.lf.serviceapi.base.weather.api;

import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationFcClctJTDO;

import java.util.Date;
import java.util.List;

public interface WeatherStationFcClctJTService {

    List<WeatherStationFcClctJTDO> findByDateAndstationId(Date startDate, Date endDate, String stationId, Integer type)
            throws Exception;

    List<WeatherStationFcClctJTDO> findByDateAndstationIds(Date startDate, Date endDate, List<String> stationIds, Integer type)
            throws Exception;
}
