package com.tsintergy.lf.serviceapi.base.report.pojo;

import com.tsieframework.core.base.dao.BaseVO;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2025-05-07
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "report_accuracy_day_stat_algorithm_recall")
public class ReportAlgorithmAccuracyRecallDayDO extends BaseVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    @Column(name = "city_id")
    private String cityId;

    @Column(name = "caliber_id")
    private String caliberId;

    @Column(name = "algorithm_id")
    private String algorithmId;


    @Column(name = "date")
    private Date date;


    /**
     * 96时刻点准确率
     */
    @Column(name = "point_accuracy")
    private BigDecimal pointAccuracy;

    /**
     * 最大负荷预测准确率
     */
    @Column(name = "max_accuracy")
    private BigDecimal maxAccuracy;

    /**
     * 最小负荷准确率
     */
    @Column(name = "min_accuracy")
    private BigDecimal minAccuracy;

    /**
     * 合格率
     */
    @Column(name = "pass")
    private BigDecimal pass;

    /**
     * 目标准确率
     */
    @Column(name = "standard_accuracy")
    private BigDecimal standardAccuracy;


    @Column(name = "createtime")
    @CreationTimestamp
    private Date createTime;

    @Column(name = "updatetime")
    @UpdateTimestamp
    private Date updateTime;

    /**
     * 电量准确率
     */
    @Column(name = "enery_accuracy")
    private BigDecimal eneryAccuracy;

    /**
     * 综合准确率
     */
    @Column(name = "comprehensive_accuracy")
    private BigDecimal comprehensiveAccuracy;

    @Column(name = "report")
    private Boolean report;
}
