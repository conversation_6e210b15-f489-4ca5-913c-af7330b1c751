/**
 * Copyright(C),2015‐2020,北京清能互联科技有限公司
 * Author:yangjin
 * Date:2020/5/1218:32
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.weather.api;


import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationFcClctGDTDO;
import java.util.Date;
import java.util.List;

/**
 *Description: <br>
 *
 *<AUTHOR>
 *@create 2020/7/21
 *@since 1.0.0
 */
public interface WeatherStationFcClctGDTService {

    List<WeatherStationFcClctGDTDO> findByDateAndstationId(Date startDate, Date endDate,String stationId, Integer type)
        throws Exception;

    List<WeatherStationFcClctGDTDO> findByDateAndstationIds(Date startDate, Date endDate,List<String> stationIds, Integer type)
            throws Exception;
}