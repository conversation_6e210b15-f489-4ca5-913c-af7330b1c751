package com.tsintergy.lf.serviceapi.base.report.api;

import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAlgorithmAccuracyRecallDayDO;
import java.util.Date;
import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2025-05-07
 * @since 1.0.0
 */
public interface ReportAlgorithmAccuracyDayRecallService {

    List<ReportAlgorithmAccuracyRecallDayDO> doStatSaveReportAccuracy(String cityId, String caliberId, Date startDate,
        Date endDate) throws Exception;

    void doSaveOrUpdate(List<ReportAlgorithmAccuracyRecallDayDO> reportAccuracyDayVOS) throws Exception;

    List<ReportAlgorithmAccuracyRecallDayDO> getReportAccuracy(String cityId, String caliberId, String algorithmId,
        Date startDate, Date endDate, Boolean isReport) throws Exception;
}
