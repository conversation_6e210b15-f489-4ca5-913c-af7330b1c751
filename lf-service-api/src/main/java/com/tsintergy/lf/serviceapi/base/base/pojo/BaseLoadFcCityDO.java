package com.tsintergy.lf.serviceapi.base.base.pojo;

import io.swagger.annotations.ApiModelProperty;
import javax.persistence.Column;
import javax.persistence.MappedSuperclass;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2025-05-07
 * @since 1.0.0
 */
@MappedSuperclass
public class BaseLoadFcCityDO extends BaseLoadCityDO{
    /**
     * 是否上报结果
     */
    @ApiModelProperty(value = "是否上报结果")
    @Column(name = "report")
    private Boolean report;


    /**
     * 算法ID
     */
    @ApiModelProperty(value = "算法ID")
    @Column(name = "algorithm_id")
    private String algorithmId;


    public Boolean getReport() {
        return report;
    }

    public void setReport(Boolean report) {
        this.report = report;
    }

    public String getAlgorithmId() {
        return algorithmId;
    }

    public void setAlgorithmId(String algorithmId) {
        this.algorithmId = algorithmId;
    }
}
