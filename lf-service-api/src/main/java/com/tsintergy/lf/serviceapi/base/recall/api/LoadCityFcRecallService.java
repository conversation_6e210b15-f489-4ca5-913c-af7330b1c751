package com.tsintergy.lf.serviceapi.base.recall.api;

import com.tsintergy.lf.serviceapi.base.recall.dto.LoadFcQueryDTO;
import com.tsintergy.lf.serviceapi.base.recall.pojo.LoadCityFcRecallDO;
import java.util.Date;
import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2025-05-07
 * @since 1.0.0
 */
public interface LoadCityFcRecallService {

    LoadFcQueryDTO getLoadBatchFcQueryDTO(String cityId, String caliberId, Date date, String algorithmId
        , String batchId, Integer day);

    LoadCityFcRecallDO getLoadCityFcRecallDO(String cityId, String caliberId, Date date, String algorithmId) throws Exception;

    List<LoadCityFcRecallDO> getLoadAllData(String cityId, String caliberId, Date startDate, Date endDate,
        String algorithmId)throws Exception;

    LoadCityFcRecallDO doSaveOrUpdateLoadCityFcDO96(LoadCityFcRecallDO loadCityFcRecallDO) throws Exception;
}
