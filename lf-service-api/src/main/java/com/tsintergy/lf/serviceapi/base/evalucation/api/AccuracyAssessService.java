package com.tsintergy.lf.serviceapi.base.evalucation.api;

import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyAssessDTO;
import com.tsintergy.lf.serviceapi.base.recall.dto.StatisticsAccuracyDTO;
import java.util.Date;
import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2025-05-07
 * @since 1.0.0
 */
public interface AccuracyAssessService {

    List<String> findNameList(String cityId, String caliberId, String algorithmId,
        Date startDate, Date endDate);

    List<AccuracyAssessDTO> findAccuracyAccessRecallList(String cityId, String caliberId, String algorithmId,
        Date startDate, Date endDate, String batchId, Integer day) throws Exception;

    StatisticsAccuracyDTO getStatisticsAccuracyAccessRecall(String cityId, String caliberId, Date startDate,
        Date endDate, String algorithmId, String batchId, String accuracyName, Integer day) throws Exception;
}
