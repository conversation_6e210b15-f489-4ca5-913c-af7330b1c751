package com.tsintergy.lf.serviceapi.algorithm.api;

import com.tsintergy.aif.algorithm.serviceapi.base.dto.AlgorithmEnum;

import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/5/29 13:50
 **/
public interface GurNetworkAlgorithmService {

    void doGurNetworkForecast(String cityId, Date startDate, Date endDate, String caliberId, List<AlgorithmEnum> algorithmEnums);

    /**
     * 用电需求预测
     * @param cityId
     * @param startDate
     * @param endDate
     * @param caliberId
     * @param algorithmEnums
     */
    void doGurNetworkFullForecast(String cityId, Date startDate, Date endDate, String caliberId, List<AlgorithmEnum> algorithmEnums);



    void doGurNetworkRecallForecast(String cityId, Date startDate, Date endDate, String caliberId,
        List<AlgorithmEnum> algorithmEnums, Integer pointNum, Integer weatherType, Integer type, Boolean isRecall);

}
