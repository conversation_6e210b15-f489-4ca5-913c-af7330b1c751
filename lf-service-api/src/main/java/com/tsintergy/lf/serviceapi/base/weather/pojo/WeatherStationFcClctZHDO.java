/**
 * Copyright(C),2015‐2020,北京清能互联科技有限公司
 * Author:yangjin
 * Date:2020/5/1216:19
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.weather.pojo;



import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;

import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Load;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Weather;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.base.pojo.Base96DO;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;
import org.springframework.format.annotation.DateTimeFormat;

/**
 *Description: <br>
 *
 *<AUTHOR>
 *@create 20200721
 *@since 1.0.0
 */
@Data
@Entity
@Table(name="weather_station_fc_clct_zh")
@EntityUniqueIndex({"date","stationId","type"})
public class WeatherStationFcClctZHDO extends Base96DO implements Weather, Load {
    public  WeatherStationFcClctZHDO() {
        super();
    }

    public  WeatherStationFcClctZHDO(String stationId, Integer type, java.util.Date date){
        this.stationId = stationId;
        this.type=type;
        this.date=new Date(date.getTime());
        this.id= UUID.randomUUID().toString().replace("-","");
    }
    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    /**
     * 日期
     */
    @Column(name = "date")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date date;

    @Column(name = "station_Id")
    private String stationId;

    @Column(name = "type")
    private Integer type;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    @Override
    public List<BigDecimal> getWeatherList() {
        return BasePeriodUtils.toList(this, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
    }

    @Override
    public List<BigDecimal> getloadList() {
        return Collections.emptyList();
    }

    @Override
    public String getDeviceId() {
        return Load.super.getDeviceId();
    }
}