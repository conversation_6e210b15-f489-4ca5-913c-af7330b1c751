package com.tsintergy.lf.serviceapi.base.evalucation.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2025-05-07
 * @since 1.0.0
 */
@Data
@ApiModel("综合准确率结果对象；")
public class CompositeAccuracyUnitDTO {

    /**
     * 综合准确率名称
     */
    @ApiModelProperty("综合准确率名称")
    private String accuracyName;

    /**
     * 综合准确率
     */
    @ApiModelProperty("综合准确率")
    private BigDecimal accuracy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createtime;

}
