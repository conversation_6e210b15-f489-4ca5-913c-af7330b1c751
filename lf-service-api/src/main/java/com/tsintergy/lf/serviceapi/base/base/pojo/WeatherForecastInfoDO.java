package com.tsintergy.lf.serviceapi.base.base.pojo;

import com.tsieframework.core.base.vo.CacheVO;
import java.sql.Date;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * Description: 天气预报配置
 *
 * <AUTHOR>
 * @create 2023-04-26
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "weather_forecast_info")
public class WeatherForecastInfoDO extends CacheVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    /**
     * 日期
     */
    @Column(name = "date")
    private Date date;

    /**
     * 文件名称
     */
    @Column(name = "path")
    private String path;

    /**
     * 文件名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    @Override
    public String getKey() {
        return this.id;
    }

    @Override
    public String getLabel() {
        return this.name;
    }

}
