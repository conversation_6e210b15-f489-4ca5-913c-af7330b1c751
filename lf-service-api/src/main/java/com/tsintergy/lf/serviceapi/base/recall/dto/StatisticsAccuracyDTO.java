package com.tsintergy.lf.serviceapi.base.recall.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2025-05-07
 * @since 1.0.0
 */
@Data
@ApiModel
public class StatisticsAccuracyDTO {
    @BigdecimalJsonFormat(percentConvert = 100, scale = 2)
    @ApiModelProperty(value = "预测准确率")
    private BigDecimal fcAccuracy;

    @BigdecimalJsonFormat(percentConvert = 100, scale = 2)
    @ApiModelProperty(value = "回溯准确率")
    private BigDecimal recallAccuracy;

    @BigdecimalJsonFormat(percentConvert = 100, scale = 2)
    @ApiModelProperty(value = "饼状图 < 0 占比")
    private BigDecimal area1;

    @BigdecimalJsonFormat(percentConvert = 100, scale = 2)
    @ApiModelProperty(value = "饼状图 0-1 占比")
    private BigDecimal area2;

    @BigdecimalJsonFormat(percentConvert = 100, scale = 2)
    @ApiModelProperty(value = "饼状图 > 1 占比")
    private BigDecimal area3;

    @ApiModelProperty(value = "提示内容")
    private String showInfo;
}
