/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司 Author: liufeng Date: 2018/6/6 19:26 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.impl;

import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.DateUtil;
import com.tsintergy.lf.core.constants.WeatherSourceEnum;
import com.tsintergy.lf.serviceapi.algorithm.api.Forecastable;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.algorithm.dto.ForecastParam;
import com.tsintergy.lf.serviceapi.algorithm.dto.MetaData;
import com.tsintergy.lf.serviceapi.algorithm.dto.Param;
import com.tsintergy.lf.serviceapi.algorithm.dto.Result;
import com.tsintergy.lf.serviceapi.algorithm.dto.SensitivityParam;
import com.tsintergy.lf.serviceapi.algorithm.dto.ShortForecastParam;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.Base96DO;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.CityEnum;
import com.tsintergy.lf.serviceapi.base.forecast.api.ForecastDataService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherNewEnum;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHis288DO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingSystemDO;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.tsintergy.lf.serviceapi.base.weather.pojo.*;
import com.tsintergy.lf.serviceimpl.common.constants.ShortConstants;
import com.tsintergy.lf.serviceimpl.common.impl.CoreConfigInfo;
import com.tsintergy.lf.serviceimpl.common.util.ColumnUtil;
import com.tsintergy.lf.serviceimpl.executor.Executor;
import com.tsintergy.lf.serviceimpl.executor.analysis.SensitivityExecutor;
import com.tsintergy.lf.serviceimpl.factory.ForecastMethodFactory;
import com.tsintergy.lf.serviceimpl.system.constant.SystemConstant;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcClctGDTDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcClctZHTDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcClctZYTDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherFeatureCityDayHisDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherFeatureCityWeekFcDAO;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:  <br> 服务接口实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service(value = "forecastable")
public class Forecastor implements Forecastable {

    private static final Logger logger = LogManager.getLogger(Forecastor.class);

    @Autowired
    private ForecastDataService algorithmForecastDataService;

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    private WeatherCityFcModifyService weatherCityFcModifyService;


    @Autowired
    private SettingSystemService settingSystemService;

    @Autowired
    private SensitivityExecutor sensitivityExecutor;

    @Autowired
    CityService cityService;

    @Autowired
    private CoreConfigInfo coreConfigInfo;

    @Autowired
    private WeatherCityFcClctGDTDAO weatherCityFcClctGDTDAO;

    @Autowired
    private WeatherCityFcClctZHTDAO weatherCityFcClctZHTDAO;

    @Autowired
    private WeatherCityFcClctZYTDAO weatherCityFcClctZYTDAO;


    @Override
    public Result autoForecast(String cityId, String caliberId, Date startDate, Date endDate) throws Exception {
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        Result result = null;
        List<AlgorithmEnum> enums = new ArrayList<>();
        enums.add(AlgorithmEnum.FORECAST_SIMILAR);
        enums.add(AlgorithmEnum.FORECAST_SIMILAR_OFFSET);
        enums.add(AlgorithmEnum.FORECAST_SVM);
        enums.add(AlgorithmEnum.FORECAST_SVM_LARGE);
        enums.add(AlgorithmEnum.FORECAST_XGBOOST);
        enums.add(AlgorithmEnum.REPLENISH_LGB);
        enums.add(AlgorithmEnum.FORECAST_SIMILAR);
        try {
            result = doForecast(null, 1, null, cityId, caliberId, dateList, enums, null, null, null);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return result;
    }

    @Override
    public Result doForecast(ForecastParam param, Integer forecastType, String uid,
        String cityId, String caliberId, List<Date> dates,
        List<AlgorithmEnum> algorithmEnums, Integer fcstDayWeatherType, Integer type, Integer pointNum)
        throws Exception {
        return doForecast(param, forecastType, uid, cityId, caliberId, dates, algorithmEnums, fcstDayWeatherType,
            type, pointNum, null);
    }

    @Override
    public Result doForecast(ForecastParam param, Integer forecastType, String uid,
        String cityId, String caliberId, List<Date> dates,
        List<AlgorithmEnum> algorithmEnums, Integer fcstDayWeatherType, Integer type,
        Integer pointNum, String weatherCode) throws Exception{
        Collections.sort(dates);
        Date startFcstDay = dates.get(0);
        Date endFcstDay = dates.get(dates.size() - 1);
        Result result = new Result();
        Map<String, Object> datas = new HashMap();
        if (param == null) {
            param = new ForecastParam();
        }
        mergeData(datas, cityId, caliberId, startFcstDay, endFcstDay, fcstDayWeatherType, weatherCode,type,param,algorithmEnums);


        if (StringUtils.isNotBlank(uid)) {
            param.setUid(uid);
        }
        param.setCityId(cityId);
        param.setCaliberId(caliberId);
        param.setPointNum(pointNum);
        if (forecastType == null) {
            //如果没有设置，则默认是算法内部做滚动预测
            forecastType = 1;
        }
        if (forecastType == 1) {//算法内部做了滚动预测，预测多天，只调用一次算法
            for (AlgorithmEnum algorithmEnum : algorithmEnums) {
                //复制神经网络算法 只跑city_id =1
                if (AlgorithmEnum.COPY_FORECAST_INNOVATION.equals(algorithmEnum) && Integer.valueOf(cityId) != 1) {
                    continue;
                }
                try {
                    param.setForecastDate(startFcstDay);
                    param.setForecastEndDate(endFcstDay);
                    param.setAlgorithmEnum(algorithmEnum);
                    param.setCityName(algorithmForecastDataService.findCityDOByCityId(cityId).getCity());
                    logger.info("开始预测，startDate:{}, endDate:{}, cityId:{}, caliberId:{}, algorithmId:{}",
                        startFcstDay,endFcstDay,cityId,caliberId, algorithmEnum.getType());
                    Result executeResult = execute(param, datas);
                    logger.info("预测完成，startDate:" + startFcstDay + ", endDate:" + endFcstDay + ", cityId:" + cityId
                        + ", caliberId:" + caliberId + ", algorithmId:" + algorithmEnum.getType());
                    result = result.add(executeResult);

                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                    continue;
                }
            }
        } else {//程序循环一天一天调用
            for (AlgorithmEnum algorithmEnum : algorithmEnums) {
                for (Date date : dates) {
                    try {
                        param.setForecastDate(date);
                        param.setForecastEndDate(date);
                        param.setAlgorithmEnum(algorithmEnum);
                        param.setCityName(algorithmForecastDataService.findCityDOByCityId(cityId).getCity());
                        String enumType = algorithmEnum.getType();
                        logger.info("开始预测，startDate:" + startFcstDay + ", endDate:" + endFcstDay + ", cityId:" + cityId
                            + ", caliberId:" + caliberId + ", algorithmId:" + enumType);
                        Result executeResult = execute(param, datas);
                        logger.info("预测完成，startDate:" + startFcstDay + ", endDate:" + endFcstDay + ", cityId:" + cityId
                            + ", caliberId:" + caliberId + ", algorithmId:" + enumType);
                        result = result.add(executeResult);
                        //预测结果累加到历史负荷
                        List<LoadCityHisDO> loads = (List<LoadCityHisDO>) datas.get(DATA_LOAD_KEY);
                        //如果数据够 则不需要将预测结果累加到历史负荷
                        Date baseDate = DateUtils.addDays(date, -2);
                        if (loads.get(loads.size() - 1).getDate().before(DateUtils.addDays(baseDate, 1))) {
                            if (null != executeResult) {
                                List<MetaData<BigDecimal>> metaDatas = executeResult.getData();
                                for (MetaData<BigDecimal> metaData : metaDatas) {
                                    LoadCityHisDO vo = new LoadCityHisDO();
                                    vo.setCaliberId(caliberId);
                                    vo.setCityId(cityId);
                                    vo.setDate(new java.sql.Date(metaData.getDate().getTime()));
                                    BasePeriodUtils.setAllFiled(vo, ColumnUtil
                                        .listToMap(metaData.getValue(), Constants.LOAD_CURVE_START_WITH_ZERO));
                                    loads.add(vo);
                                }
                                datas.put(DATA_LOAD_KEY, loads);
                            }
                        }
                    } catch (Exception e) {
                        logger.error(e.getMessage(), e);
                        continue;
                    }
                }
            }
        }
        return result;
    }


    @Override
    public Result doSensitivity(String cityId, Date startDate, Date endDate, String max, String min, String step,
        String loadType, String weatherType, Map<String, Object> map, List<String> dateNotIncluded) throws Exception {
        SensitivityParam sensitivityParam = new SensitivityParam();
        sensitivityParam.setBeginDate(startDate);
        sensitivityParam.setEndDate(endDate);
        sensitivityParam.setMax(max);
        sensitivityParam.setMin(min);
        sensitivityParam.setStep(step);
        sensitivityParam.setLoadType(loadType);
        sensitivityParam.setWeatherType(weatherType);
        sensitivityParam.setCityId(cityId);
        sensitivityParam.setDateNotIncluded(dateNotIncluded);
        //节假日数据
        mergeHolidayData(map);
        sensitivityExecutor.preprocess(sensitivityParam, map);
        sensitivityExecutor.execute(sensitivityParam);
        return sensitivityExecutor.postprocess(sensitivityParam);
    }

    public Result execute(Param param, Map<String, Object> datas) throws Exception {
        try {
            ForecastParam forecastParam = (ForecastParam) param;
            Executor executor = ForecastMethodFactory.create(forecastParam);
            executor.preprocess(forecastParam, datas);
            executor.execute(forecastParam);
            Result result = executor.postprocess(forecastParam);
            return result;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw e;
        }
    }

    public void mergeData(Map<String, Object> datas, String cityId, String caliberId, Date startFcstDay,
        Date endFcstDay,ForecastParam forecastParam,List<AlgorithmEnum> algorithmEnums) throws Exception {
        //fway为null表示不是走的实施页面t+1或者t+2
        mergeData(datas, cityId, caliberId, startFcstDay, endFcstDay, null, null,null,forecastParam,algorithmEnums);
    }

    /**
     * 算法构造数据
     * weatherType 待预测日气象类型 实施页面传参
     */
    public void mergeData(Map<String, Object> datas, String cityId, String caliberId, Date startFcstDay,
        Date endFcstDay, Integer fcstDayWeatherType, String weatherCode,Integer fway,ForecastParam forecastParam,List<AlgorithmEnum> algorithmEnums) throws Exception {
        //查询三年的历史负荷
        Date lastYearStartDate = DateUtil.getMoveDay(startFcstDay, -1200);

        datas.put("caliberId",caliberId);
        mergeHisLoad(datas, cityId, caliberId, lastYearStartDate, endFcstDay);
        Date baseDate = null;
        if(forecastParam.getBaseDay() != null){
            baseDate = forecastParam.getBaseDay();
        }else {
            baseDate = getBaseDate(datas, fway, startFcstDay, forecastParam);
        }
        mergeWeatherData(cityId, datas, lastYearStartDate, endFcstDay, fcstDayWeatherType, startFcstDay, weatherCode,baseDate,algorithmEnums);
        mergeHolidayData(datas);
        //气象特性往前推一年
        // 补充气象特性数据 modify by yzm 2021-09-17 将3年改为1200天，经和算法组沟通，数据可以多，不可以少
        mergeWeatherFeature(datas, cityId, endFcstDay, lastYearStartDate, fcstDayWeatherType, weatherCode,baseDate,algorithmEnums);
        //基准日（包含）前2年天的各类算法预测数据
        Date queryStartTime = DateUtils.addYears(startFcstDay, -2);
        mergeAlgorithmFc(datas, cityId, queryStartTime, DateUtils.addDays(endFcstDay, 1), caliberId);

        if (algorithmEnums.contains(AlgorithmEnum.FORECAST_INNOVATION)) {
            // 神经网络周预测补充预测人工修正气象数据
            mergeWeatherWeekFeature(datas, cityId, startFcstDay, endFcstDay);
        }

        if (algorithmEnums.contains(AlgorithmEnum.REPLENISH_LGB)) {
            //如果是xg的补充算法--lgb算法,填充综合气象数据
            mergeSynthesizeWeatherData(cityId, datas, DateUtil.getMoveDay(startFcstDay, -1200), endFcstDay,baseDate);
        }
    }

    public Date getBaseDate(Map<String, Object> datas,Integer fway,Date startFcstDay,ForecastParam forecastParam){
        Date baseDate = null;
        List<LoadCityHisDO> hisLoadDatas = (List<LoadCityHisDO>) datas.get(DATA_LOAD_KEY);
        if(fway != null){
            forecastParam.setFway(fway);
            //预测方式 1 T+1 2 T+2  不为null，是实施页面调用
             baseDate = fway == 1 ? DateUtil.getMoveDay(startFcstDay, -1) :
                 DateUtil.getMoveDay(startFcstDay, -2);
            //如果该方式基准日没有完整数据  走动态获取到基准日的逻辑
            Map<Date , LoadCityHisDO> dateMap = hisLoadDatas.stream().collect(Collectors.toMap(e->e.getDate() , e->e ,(oldV, curV)->curV));
            LoadCityHisDO nowBaseDayLoad = dateMap.get(baseDate);
            if(nowBaseDayLoad == null || nowBaseDayLoad.getT2345() == null){
                //动态获取到基准日的逻辑
                //基准日 不定死 取有完整数据的最近一天数据
                List<LoadCityHisDO> loadCityHisVOS = hisLoadDatas.stream().filter(
                    loadCityHisVO -> loadCityHisVO.getDate().before(startFcstDay))
                    .collect(Collectors.toList());
                for (int i = 1; ; i++) {
                    if (loadCityHisVOS.get(loadCityHisVOS.size() - i).getT2345() != null) {
                        baseDate = hisLoadDatas.get(loadCityHisVOS.size() - i).getDate();
                        break;
                    }
                }
            }
        }else {
            //动态获取到基准日，取距离预测日最近的有完整的数据
            List<LoadCityHisDO> loadCityHisDOS = hisLoadDatas.stream().filter(
                    loadCityHisDO -> loadCityHisDO.getDate().before(startFcstDay))
                .collect(Collectors.toList());
            //基准日 手动预测时  是前端传的 其他情况为  取有完整数据的最近一天数据
            if (!Constants.FORECAST_TYPE.equals(forecastParam.getType())) {
                baseDate = DateUtils.addDays(startFcstDay, -2);
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(loadCityHisDOS)) {
                    //基准日 不定死 取有完整数据的最近一天数据
                    for (int i = 1; ; i++) {
                        if (loadCityHisDOS.get(loadCityHisDOS.size() - i).getT2345() != null) {
                            baseDate = loadCityHisDOS.get(loadCityHisDOS.size() - i).getDate();
                            break;
                        }
                    }
                }
            }
        }
        return baseDate;
    }



    /**
     * 查询算法历史预测负荷数据
     */
    private void mergeAlgorithmFc(Map<String, Object> datas, String cityId, Date queryStartTime,
        Date startFcstDay, String caliberId) throws Exception {
        List<LoadCityFcDO> loadCityFcVOS = loadCityFcService
            .findLoadCityFc(Arrays.asList(cityId), queryStartTime, startFcstDay, caliberId);
        if (CollectionUtils.isEmpty(loadCityFcVOS)) {
            logger.error("城市id:{}算法执行失败，算法历史预测负荷数据为空", cityId);
            throw TsieExceptionUtils.newBusinessException("算法执行失败，算法历史预测负荷数据为空");
        }
        datas.put(DATA_ALGORITHM_KEY, loadCityFcVOS);

    }


    /**
     * 查询历史负荷数据
     *
     * @param cityId
     * @param caliberId
     * @param endFcstDay
     * @param lastYearStartDate
     * @return
     * @throws Exception
     */
    private void mergeHisLoad(Map<String, Object> datas, String cityId, String caliberId, Date lastYearStartDate,
        Date endFcstDay) throws Exception {
        List<LoadCityHisDO> LoadCityHisDOS = algorithmForecastDataService.findHisLoad(cityId, caliberId,
            lastYearStartDate, endFcstDay);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(LoadCityHisDOS)) {
            logger.error("城市id:{}算法执行失败，历史负荷数据为空", cityId);
            throw TsieExceptionUtils.newBusinessException("算法执行失败，历史负荷数据为空");
        }
        datas.put(DATA_LOAD_KEY, LoadCityHisDOS);
    }


    /**
     * 匹配历史气象数据
     */
    public void mergeWeatherData(String cityId, Map<String, Object> datas, Date lastYearStartDate, Date endFcstDay,
        Integer fcstDayWeatherType, Date startFcstDate, String weatherCode,Date baseDate,List<AlgorithmEnum> algorithmEnums)
        throws Exception {
        SettingSystemDO fc_weather_type = settingSystemService.findByFieldId(SystemConstant.DEFAULT_FC_WEATHER_SOURCE);
        String fc_value_type = fc_weather_type.getValue();
        //如果是预测省 则使用省会城市的气象(人为将省会城市排序为2)
        List<WeatherCityHisDO> weatherCityHisVOS = null;
        if ("1".equals(cityId) && algorithmEnums.size() == 1 && AlgorithmEnum.COPY_FORECAST_INNOVATION.equals(algorithmEnums.get(0))){
            cityId = "1";
        }else {
            cityId = cityService.findWeatherCityId(cityId);
        }
        Integer weatherType = 0;
        String type = coreConfigInfo.getRuntimeParam("forecast.model.weather");
        if (type != null) {
            weatherType = Integer.valueOf(type);
        }
        //系统设置的使用历史气象数据
        if (weatherType == 0) {
            //从lastYearStartDate到预测日前一天使用实际气象
            weatherCityHisVOS = algorithmForecastDataService.findHisWeather(cityId, lastYearStartDate, DateUtils.addDays(startFcstDate,-1));
            //查看历史数据截止日期
            if (CollectionUtils.isNotEmpty(weatherCityHisVOS)) {
                mergeSynthesizeHisWeather(cityId, datas, endFcstDay, weatherCityHisVOS,startFcstDate,fc_value_type,baseDate,algorithmEnums);
            }
        }
        //系统设置的使用预测气象数据
        else {
            mergeSynthesizeFcWeather(cityId, datas, lastYearStartDate, endFcstDay,fc_value_type);
        }

        //获取预测日气象源数据，广东修正上报中选择气象源才会使用这个
        getForecastDayWeather(cityId, datas, endFcstDay, weatherCode);

        if (fcstDayWeatherType != null) {
            //实施页面逻辑 自动预测不走这块
            List<WeatherCityHisDO> hisWeather = (List<WeatherCityHisDO>) datas.get(DATA_WEATHER_KEY);
            if (CollectionUtils.isEmpty(hisWeather)) {
                return;
            }
            Map<Date, List<WeatherCityHisDO>> dateWeatherMap = hisWeather.stream()
                .collect(Collectors.groupingBy(WeatherCityHisDO::getDate));
            //待预测日之前的气象使用历史气象，缺的日期使用预测补充
            dateWeatherMap.remove(endFcstDay);
            Set<Date> dateSet = dateWeatherMap.keySet();
            Date maxDate = Collections.max(dateSet);
            List<BaseWeatherDO> supplementWeatherCityFcDOS = fcWeatherService
                    .findFcWeatherDataWithCityId(cityId, null, DateUtil.getMoveDay(maxDate, 1), DateUtil.getMoveDay(endFcstDay, -1), fc_value_type);
            if (!CollectionUtils.isEmpty(supplementWeatherCityFcDOS)) {
                Map<Date, List<BaseWeatherDO>> map = supplementWeatherCityFcDOS.stream()
                    .collect(Collectors.groupingBy(BaseWeatherDO::getDate));
                for (Date date : map.keySet()) {
                    List<WeatherCityHisDO> supplementWeatherCityHisDOS = new ArrayList<>();
                    map.get(date).forEach(e -> {
                        WeatherCityHisDO hisVO = new WeatherCityHisDO();
                        BeanUtils.copyProperties(e, hisVO);
                        supplementWeatherCityHisDOS.add(hisVO);
                    });
                    dateWeatherMap.put(date, supplementWeatherCityHisDOS);
                }
            }
            if (fcstDayWeatherType == 0) {
                //待预测日使用 历史气象
                List<WeatherCityHisDO> fcstDayWeatherVO = algorithmForecastDataService
                    .findHisWeather(cityId, endFcstDay, endFcstDay);
                dateWeatherMap.put(endFcstDay, CollectionUtils.isEmpty(fcstDayWeatherVO) ? null : fcstDayWeatherVO);
            } else {
                //待预测日使用 预测气象
                List<BaseWeatherDO> baseWeatherDOS = fcWeatherService
                    .findFcWeatherDataWithCityId(cityId, null,endFcstDay, endFcstDay, fc_value_type);

                if (CollectionUtils.isEmpty(baseWeatherDOS)) {
                    dateWeatherMap.put(endFcstDay, null);
                } else {
                    List<WeatherCityHisDO> weatherCityHisVOList = new ArrayList<>();
                    baseWeatherDOS.forEach(e -> {
                        WeatherCityHisDO hisVO = new WeatherCityHisDO();
                        BeanUtils.copyProperties(e, hisVO);
                        weatherCityHisVOList.add(hisVO);
                    });
                    dateWeatherMap.put(endFcstDay, weatherCityHisVOList);
                }
            }
            List<WeatherCityHisDO> resultWeather = new ArrayList<>();
            for (Date dateKey : dateWeatherMap.keySet()) {
                if (dateWeatherMap.get(dateKey) == null) {
                    continue;
                }
                resultWeather.addAll(dateWeatherMap.get(dateKey));
            }
            resultWeather.remove(null);
            resultWeather = resultWeather.stream().sorted(Comparator.comparing(WeatherCityHisDO::getDate))
                .collect(Collectors.toList());
            datas.put(DATA_WEATHER_KEY, resultWeather);
        }
    }

    private void getForecastDayWeather(String cityId, Map<String, Object> datas, Date endFcstDay, String weatherCode)
        throws Exception {
        if(WeatherSourceEnum.getEnumByCode(weatherCode) == null){
            return;
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String endDateStr = format.format(endFcstDay);
        if(WeatherSourceEnum.getEnumByCode(weatherCode) == WeatherSourceEnum.MODIFY){
            Map<Integer, WeatherCityFcModifyDO> map = weatherCityFcModifyService
                .findModifyWeatherByDateAndType(endDateStr, null, cityId).stream()
                .collect(Collectors.toMap(WeatherCityFcModifyDO::getType, Function.identity(), (v1, v2) -> v1));
            replaceFCVO(datas, map, endFcstDay);
        }
        if(WeatherSourceEnum.getEnumByCode(weatherCode) == WeatherSourceEnum.GDT){
            Map<Integer, WeatherCityFcClctGDTDO> map = weatherCityFcClctGDTDAO
                .findWeatherCityFcDO(cityId, null, endFcstDay, endFcstDay).stream()
                .collect(Collectors.toMap(WeatherCityFcClctGDTDO::getType, Function.identity(), (v1, v2) -> v1));
            replaceFCVO(datas, map, endFcstDay);
        }
        if(WeatherSourceEnum.getEnumByCode(weatherCode) == WeatherSourceEnum.JT){
            Map<Integer, WeatherCityFcDO> map = algorithmForecastDataService.findFcWeather(cityId,
                endFcstDay, endFcstDay).stream()
                .collect(Collectors.toMap(WeatherCityFcDO::getType, Function.identity(), (v1, v2) -> v1));;
            replaceFCVO(datas, map, endFcstDay);
        }
        if(WeatherSourceEnum.getEnumByCode(weatherCode) == WeatherSourceEnum.ZHT){
            Map<Integer, WeatherCityFcClctZHTDO> map = weatherCityFcClctZHTDAO
                .findWeatherCityFcDO(cityId, null, endFcstDay, endFcstDay).stream()
                .collect(Collectors.toMap(WeatherCityFcClctZHTDO::getType, Function.identity(), (v1, v2) -> v1));;
            replaceFCVO(datas, map, endFcstDay);
        }
        if(WeatherSourceEnum.getEnumByCode(weatherCode) == WeatherSourceEnum.ZYT){
            Map<Integer, WeatherCityFcClctZYTDO> map = weatherCityFcClctZYTDAO
                .findWeatherCityFcDO(cityId, null, endFcstDay, endFcstDay).stream()
                .collect(Collectors.toMap(WeatherCityFcClctZYTDO::getType, Function.identity(), (v1, v2) -> v1));;
            replaceFCVO(datas, map, endFcstDay);
        }
    }

    private <T extends Base96DO> void replaceFCVO(Map<String, Object> datas, Map<Integer, T> map, Date date) {
        if(map == null || map.size() < 0){
            return;
        }
        for (Map.Entry<Integer, T> entry : map.entrySet()) {
            Integer key = entry.getKey();
            T value = entry.getValue();
            if (value != null) {
                WeatherCityHisDO his = new WeatherCityHisDO();
                BeanUtils.copyProperties(value, his);
                List<WeatherCityHisDO> hisWeather = (List<WeatherCityHisDO>) datas.get(DATA_WEATHER_KEY);
                for (int i = 0; i < hisWeather.size(); i++) {
                    WeatherCityHisDO weatherCityHisVO = hisWeather.get(i);
                    if (weatherCityHisVO.getDate().compareTo(date) == 0 && key
                        .equals(weatherCityHisVO.getType())) {
                        hisWeather.remove(i);
                        hisWeather.add(his);
                    }
                }
            }
        }
    }


    private void mergeSynthesizeFcWeather(String cityId, Map<String, Object> datas, Date lastYearStartDate,
        Date endFcstDay,String fc_weather_type) throws Exception {
        List<WeatherCityFcDO> weatherCityFcDOS = fcWeatherService
            .findFcWeatherData(cityId, null, lastYearStartDate, endFcstDay, fc_weather_type);
        List<WeatherCityHisDO> hisVOS = new ArrayList<>();
        for (WeatherCityFcDO fcVO : weatherCityFcDOS) {
            WeatherCityHisDO hisVO = new WeatherCityHisDO();
            BeanUtils.copyProperties(fcVO, hisVO);
            hisVOS.add(hisVO);
        }
        datas.put(DATA_WEATHER_KEY, hisVOS);
    }

    /**
     * 功能描述: 从date 开始检查days天数的数据是否为空，为空补预测，预测为空补96个null<br>
     * 〈〉
     *
     * @param days  检查数据天数
     * @param date  从哪天开始检查
     * @param weatherCityHisDOS 检查的数据
     * @param cityId 城市id
     * @return:void
     * @since: 1.0.0
     * @Author:yangjin
     * @Date: 2021/8/9 14:45
     */
    public void checkHisWeatherData(int days, Date date, List<WeatherCityHisDO> weatherCityHisDOS, String cityId,String fc_weather_type)
        throws Exception {
        Map<java.sql.Date, List<WeatherCityHisDO>> collect = weatherCityHisDOS.stream()
            .collect(Collectors.groupingBy(WeatherCityHisDO::getDate));
        java.sql.Date start = new java.sql.Date(date.getTime());


        String s = DateUtils.date2String(start, DateFormatType.SIMPLE_DATE_FORMAT_STR);
        Date string2Date = DateUtils.string2Date(s, DateFormatType.SIMPLE_DATE_FORMAT_STR);


        for (int i = 0; i < days; i++) {
            List<WeatherCityHisDO> cityHisDOS = collect.get(new java.sql.Date(DateUtils.addDays(string2Date, 0-i).getTime()));
            if (CollectionUtils.isEmpty(cityHisDOS)) {
                List<BaseWeatherDO> fcWeather =   fcWeatherService.findFcWeatherDataWithCityId(cityId,null,DateUtils.addDays(start, 0-i),
                    DateUtils.addDays(start, 0-i),fc_weather_type);
                //预测数据为空
                if (CollectionUtils.isEmpty(fcWeather)) {
                    for (WeatherEnum weatherEnum : WeatherEnum.values()) {
                        weatherCityHisDOS
                            .add(new WeatherCityHisDO(cityId, weatherEnum.getType(), DateUtils.addDays(start, 0-i)));
                    }
                } else {
                    Map<Integer, List<BaseWeatherDO>> fcWeatherMap = fcWeather.stream()
                        .collect(Collectors.groupingBy(BaseWeatherDO::getType));
                    for (WeatherEnum weatherEnum : WeatherEnum.values()) {
                        Integer type = weatherEnum.getType();
                        List<BaseWeatherDO> baseWeatherDOS = fcWeatherMap.get(type);
                        if (CollectionUtils.isEmpty(baseWeatherDOS)) {
                            weatherCityHisDOS.add(new WeatherCityHisDO(cityId, type, DateUtils.addDays(start, 0-i)));
                        } else {
                            BaseWeatherDO vo = baseWeatherDOS.get(0);
                            WeatherCityHisDO hisVO = new WeatherCityHisDO();
                            hisVO.setDate(vo.getDate());
                            hisVO.setType(vo.getType());
                            hisVO.setCityId(vo.getCityId());
                            List<BigDecimal> period = BasePeriodUtils
                                .toList(vo, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
                            BasePeriodUtils
                                .setAllFiled(hisVO, ColumnUtil.listToMap(period, Constants.LOAD_CURVE_START_WITH_ZERO));
                            weatherCityHisDOS.add(hisVO);
                        }
                    }
                }
                //不是空，但有缺失(比如温度缺失)
            } else {
                Map<Integer, List<WeatherCityHisDO>> hisWeatherMap = cityHisDOS.stream()
                    .collect(Collectors.groupingBy(WeatherCityHisDO::getType));
                for (WeatherNewEnum weatherEnum : WeatherNewEnum.values()) {
                    List<WeatherCityHisDO> hisDOS = hisWeatherMap.get(weatherEnum.getType());
                    if (CollectionUtils.isEmpty(hisDOS)) {
                        List<BaseWeatherDO> fcWeather =   fcWeatherService.findFcWeatherDataWithCityId(cityId,weatherEnum.getType(),DateUtils.addDays(start, 0-i),
                            DateUtils.addDays(start, 0-i),fc_weather_type);
                       if(CollectionUtils.isEmpty(fcWeather)) {
                           weatherCityHisDOS
                               .add(new WeatherCityHisDO(cityId, weatherEnum.getType(),
                                   DateUtils.addDays(start, 0 - i)));
                       }else {
                           BaseWeatherDO vo = fcWeather.get(0);
                           WeatherCityHisDO hisVO = new WeatherCityHisDO();
                           hisVO.setDate(vo.getDate());
                           hisVO.setType(vo.getType());
                           hisVO.setCityId(vo.getCityId());
                           List<BigDecimal> period = BasePeriodUtils
                               .toList(vo, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
                           BasePeriodUtils
                               .setAllFiled(hisVO, ColumnUtil.listToMap(period, Constants.LOAD_CURVE_START_WITH_ZERO));
                           weatherCityHisDOS.add(hisVO);
                       }
                    }
                }
            }
        }
    }


    @Autowired
    FcWeatherService fcWeatherService;

    @Autowired
    private WeatherCityFcSplitService weatherCityFcSplitService;
    private void mergeSynthesizeHisWeather(String cityId, Map<String, Object> datas, Date endFcstDay,
        List<WeatherCityHisDO> weatherCityHisDOS, Date startFcDate,String fc_weather_type,Date baseDate,List<AlgorithmEnum> algorithmEnums) throws Exception {


        List<Date> updateDay = new ArrayList<>();

        //最后一天的气象
        WeatherCityHisDO weatherCityHisDO = weatherCityHisDOS.get(weatherCityHisDOS.size() - 1);


        Date endHisDate = weatherCityHisDO.getDate();
        List<BigDecimal> list = BasePeriodUtils.toList(weatherCityHisDO, Constants.LOAD_CURVE_POINT_NUM,
            Constants.LOAD_CURVE_START_WITH_ZERO);
        //去除null数据
        list.removeAll(Collections.singleton(null));
        String caliberId = datas.get("caliberId").toString();

        //正常自动预测的时候，当日的实际负荷肯定是不全的，可能存在今天的实时气象不存在的情况导致最后一条数据是全的，但是全的历史气象数据，就不考虑用预测补，
        //当日气象没有，前一天气象也不全，暂不处理，这里只处理预测当日的实时气象
        //正常自动预测当日的日期为开始日期减1
        if(list.size()<96 && DateUtils.addDays(startFcDate,-1).compareTo(endHisDate) == 0){
            //查询当日预测气象
            List<?extends BaseWeatherDO> fcVOS = getFcWeatherList(cityId,caliberId,algorithmEnums,endHisDate,endHisDate,fc_weather_type);
            //广州，系统负荷口径，bpnn、gru
            if(!CollectionUtils.isEmpty(fcVOS)){
                //查询最后一日的气象数据
                Map<Integer, List<BaseWeatherDO>> fcWeather = fcVOS.stream().collect(Collectors.groupingBy(BaseWeatherDO::getType));
                for(BaseWeatherDO baseHisWeatherDO:weatherCityHisDOS){
                    java.sql.Date dataDate = baseHisWeatherDO.getDate();
                    if(dataDate.compareTo(new java.sql.Date(endHisDate.getTime())) == 0){
                        Integer type = baseHisWeatherDO.getType();
                        List<BaseWeatherDO> baseWeatherDOS = fcWeather.get(type);
                        if(!CollectionUtils.isEmpty(baseWeatherDOS)){
                            BaseWeatherDO fcTypeWeatherDO = baseWeatherDOS.get(0);
                            List<BigDecimal> fcValues = BasePeriodUtils.toList(fcTypeWeatherDO,
                                Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
                            List<BigDecimal> hisValues = BasePeriodUtils.toList(baseHisWeatherDO,
                                Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);

                            //广东这里补预测的时候跳过2345点,从94的下标开始,后面再单独赋值2345，因为气象数据2345点可能有值(暂时没有排查出来问题).
                            for(int i=94;i>=0;i--){
                                BigDecimal poitHisWeather = hisValues.get(i);
                                if(poitHisWeather == null){
                                     hisValues.set(i,fcValues.get(i));
                                }else {
                                    //从后往前补，如果找到第一个有数据的点后，停止补数据。实际+预测，不考虑实际缺点
                                    break;
                                }
                            }
                            hisValues.set(95,fcValues.get(95));
                            BasePeriodUtils.setAllFiled(baseHisWeatherDO, ColumnUtil.listToMap(hisValues, Constants.LOAD_CURVE_START_WITH_ZERO));
                        }
                    }
                }
            }
        }

        List<? extends BaseWeatherDO> fcVOS = getFcWeatherList(cityId, caliberId, algorithmEnums, DateUtils.addDays(endHisDate, 1), endFcstDay, fc_weather_type);
        //有预测数据
        for (BaseWeatherDO vo : fcVOS) {
            //将预测的数据放入历史数据里
            WeatherCityHisDO hisVO = new WeatherCityHisDO();
            hisVO.setDate(vo.getDate());
            hisVO.setType(vo.getType());
            hisVO.setCityId(vo.getCityId());
            List<BigDecimal> period = BasePeriodUtils.toList(vo, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
            BasePeriodUtils.setAllFiled(hisVO, ColumnUtil.listToMap(period, Constants.LOAD_CURVE_START_WITH_ZERO));
            weatherCityHisDOS.add(hisVO);
            updateDay.add(vo.getDate());
            }

        //检查5天的历史气象，如果没有则补96个null
        checkHisWeatherData(5, baseDate, weatherCityHisDOS, cityId,fc_weather_type);

        //检查预测气象数据，不够时补充玖天预测气象
        checkFcWeatherData(weatherCityHisDOS,endFcstDay,cityId);

        weatherCityHisDOS =  weatherCityHisDOS.stream().sorted(Comparator.comparing(WeatherCityHisDO::getDate)).collect(Collectors.toList());
        datas.put(DATA_WEATHER_KEY, weatherCityHisDOS);
    }


    /**
     * 根据城市口径算法 区分所使用的预测气象
     * @param cityId
     * @param caliberId
     * @param algorithmEnums
     * @param startDate
     * @param endDate
     * @param fc_weather_type
     * @return
     * @throws Exception
     */
    List<?extends BaseWeatherDO>  getFcWeatherList(String cityId,String caliberId,List<AlgorithmEnum> algorithmEnums,
                                                   Date startDate,Date endDate,String fc_weather_type) throws Exception {
        List<?extends BaseWeatherDO> fcVOS;
        //广州，系统负荷口径，bpnn、gru
        if (CityEnum.guangzhou.getId().equals(cityId) && Constants.SYSTEM_LOAD_CALIBER.equals(caliberId) &&
                (algorithmEnums.contains(AlgorithmEnum.FORECAST_INNOVATION) || algorithmEnums.contains(AlgorithmEnum.GUR_NETWORK))){
            fcVOS = weatherCityFcSplitService.findWeatherList(cityId, null, startDate, endDate);
        }else{
            fcVOS = fcWeatherService
                    .findFcWeatherDataWithCityId(cityId, null, startDate, endDate, fc_weather_type);
        }
        return fcVOS;
    }

    public void checkFcWeatherData(List<WeatherCityHisDO> weatherCityHisDOS,Date endDateFc,String cityId) throws Exception{
        int pointLine = 76;
        Set<java.sql.Date> dates = weatherCityHisDOS.stream().collect(Collectors.groupingBy(WeatherCityHisDO::getDate))
            .keySet();
        java.sql.Date maxDate = Collections.max(dates);
        Date start = new Date(maxDate.getTime());

        String s = DateUtils.date2String(maxDate, DateFormatType.SIMPLE_DATE_FORMAT_STR);
        Date date = DateUtils.string2Date(s, DateFormatType.SIMPLE_DATE_FORMAT_STR);

        Map<java.sql.Date, List<WeatherCityHisDO>> collect = weatherCityHisDOS.stream()
            .collect(Collectors.groupingBy(WeatherCityHisDO::getDate));

        List<WeatherCityHisDO> maxDateWeatherCityHisDOS = collect.get(new java.sql.Date(date.getTime()));
        Map<Integer, List<WeatherCityHisDO>> maxDateTypeWeatherDatas = maxDateWeatherCityHisDOS.stream()
            .collect(Collectors.groupingBy(WeatherCityHisDO::getType));
        List<WeatherCityHisDO> temps = maxDateTypeWeatherDatas.get(WeatherEnum.TEMPERATURE.getType());
        if(CollectionUtils.isEmpty(temps) ){

        }else {
            WeatherCityHisDO temp  = temps.get(0);
            List<BigDecimal> bigDecimals = BasePeriodUtils
                .toList(temp, Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
            List<BigDecimal> tempDatas = bigDecimals.stream().filter(t -> t != null).collect(Collectors.toList());
            //如果当天有数据数据质量好不用处理当天数据
            if(tempDatas.size()>pointLine){
                start = DateUtils.addDays(start,1);
            //如果当天有数据数据质量不好,处理当天数据
            } else {
                Iterator<WeatherCityHisDO> iterator = weatherCityHisDOS.iterator();
                while (iterator.hasNext()){
                    WeatherCityHisDO next = iterator.next();
                    if(next.getDate().compareTo(start) == 0){
                        iterator.remove();
                    }
                }
            }
        }




        if(start.compareTo(endDateFc)<0){
        List<BaseWeatherDO> fcWeatherData = fcWeatherService
            .findFcWeatherDataWithCityId(cityId, null, start, endDateFc, WeatherSourceEnum.JT.getAbbreviation());
            for (BaseWeatherDO vo : fcWeatherData) {
                //将预测的数据放入历史数据里
                WeatherCityHisDO hisVO = new WeatherCityHisDO();
                hisVO.setDate(vo.getDate());
                hisVO.setType(vo.getType());
                hisVO.setCityId(vo.getCityId());
                List<BigDecimal> period = BasePeriodUtils.toList(vo, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
                BasePeriodUtils.setAllFiled(hisVO, ColumnUtil.listToMap(period, Constants.LOAD_CURVE_START_WITH_ZERO));
                weatherCityHisDOS.add(hisVO);
            }
        }
    }




    /**
     * 匹配历史综合气象数据
     */
    private void mergeSynthesizeWeatherData(String cityId, Map<String, Object> datas, Date lastYearStartDate,
        Date endFcstDay,Date baseDate) throws Exception {
        //如果是预测省 则使用省会城市的气象(人为将省会城市排序为2)
        List<StatisticsSynthesizeWeatherCityDayHisDO> WeatherCityHisDOS = null;
        List<StatisticsSynthesizeWeatherCityDayHisDO> enthalpyHisVOS = null;
        cityId = algorithmForecastDataService.findProvinceId(cityId);
        Integer weatherType = 0;
        String type = coreConfigInfo.getRuntimeParam("forecast.model.weather");
        if (type != null) {
            weatherType = Integer.valueOf(type);
        }
        //系统设置的 使用历史气象
        if (weatherType == 0) {
            WeatherCityHisDOS = algorithmForecastDataService
                .findSynthesizeHisWeather(cityId, lastYearStartDate, endFcstDay,
                    WeatherEnum.EFFECTIVE_TEMPERATURE.getType());
            enthalpyHisVOS = algorithmForecastDataService
                .findSynthesizeHisWeather(cityId, lastYearStartDate, endFcstDay,
                    WeatherEnum.ENTHALPY.getType());
            //查看历史数据截止日期
            mergeSynthesizeHis(cityId, datas, endFcstDay, WeatherCityHisDOS, SYNTHESIZE_WEATHER_KEY,baseDate);
            mergeSynthesizeHis(cityId, datas, endFcstDay, enthalpyHisVOS, Constants.ENTHALPY_VALUE,baseDate);
        }
        //系统设置的使用 预测气象
        else {
            mergeSynthesizeFc(cityId, datas, lastYearStartDate, endFcstDay, WeatherEnum.EFFECTIVE_TEMPERATURE.getType(),
                SYNTHESIZE_WEATHER_KEY);
            mergeSynthesizeFc(cityId, datas, lastYearStartDate, endFcstDay, WeatherEnum.ENTHALPY.getType(),
                Constants.ENTHALPY_VALUE);
        }
    }

    private void mergeSynthesizeFc(String cityId, Map<String, Object> datas, Date lastYearStartDate, Date endFcstDay,
        Integer type, String mapKey)
        throws Exception {
        List<StatisticsSynthesizeWeatherCityDayFcDO> weatherCityFcDOS = algorithmForecastDataService
            .findSynthesizeFcWeather(cityId,
                lastYearStartDate, endFcstDay, type);
        List<StatisticsSynthesizeWeatherCityDayHisDO> hisVOS = new ArrayList<>();
        for (StatisticsSynthesizeWeatherCityDayFcDO fcVO : weatherCityFcDOS) {
            StatisticsSynthesizeWeatherCityDayHisDO hisVO = new StatisticsSynthesizeWeatherCityDayHisDO();
            BeanUtils.copyProperties(fcVO, hisVO);
            hisVOS.add(hisVO);
        }
        datas.put(mapKey, hisVOS);
    }



    @Autowired
    StatisticsSynthesizeWeatherCityDayFcService statisticsSynthesizeWeatherCityDayFcService;

    private void mergeSynthesizeHis(String cityId, Map<String, Object> datas, Date endFcstDay,
        List<StatisticsSynthesizeWeatherCityDayHisDO> synthesizeWeatherCityDayHisDOS,
        String mapKey,Date baseDate) throws Exception {


        List<WeatherCityHisDO> weatherData = ( List<WeatherCityHisDO>)datas.get(DATA_WEATHER_KEY);

        Map<java.sql.Date, List<WeatherCityHisDO>> collect = weatherData.stream()
            .collect(Collectors.groupingBy(WeatherCityHisDO::getDate));

        java.sql.Date fiveDateBefore = new java.sql.Date(DateUtils.addDays(baseDate,-10).getTime());

        while (fiveDateBefore.before(new java.sql.Date(DateUtils.addDays(endFcstDay,1).getTime()))){
            List<WeatherCityHisDO> weatherCityHisDOS = collect.get(fiveDateBefore);
            Integer type = mapKey.equals(SYNTHESIZE_WEATHER_KEY)?WeatherEnum.EFFECTIVE_TEMPERATURE.getType()
                :WeatherEnum.ENTHALPY.getType();
            StatisticsSynthesizeWeatherCityDayFcDO statisticsSynthesizeWeatherCityDayFcDO = statisticsSynthesizeWeatherCityDayFcService
                .statisticsSynthesizeWeatherCityDayFcDOByType(weatherCityHisDOS, fiveDateBefore, cityId, type);

            if(statisticsSynthesizeWeatherCityDayFcDO != null){
                Iterator<StatisticsSynthesizeWeatherCityDayHisDO> iterator = synthesizeWeatherCityDayHisDOS.iterator();
                while (iterator.hasNext()){
                    if(iterator.next().getDate().compareTo(fiveDateBefore) == 0){
                        iterator.remove();
                    }
                }
                StatisticsSynthesizeWeatherCityDayHisDO hisVO = new StatisticsSynthesizeWeatherCityDayHisDO();
                hisVO.setDate(statisticsSynthesizeWeatherCityDayFcDO.getDate());
                hisVO.setType(statisticsSynthesizeWeatherCityDayFcDO.getType());
                hisVO.setCityId(statisticsSynthesizeWeatherCityDayFcDO.getCityId());
                List<BigDecimal> period = BasePeriodUtils.toList(statisticsSynthesizeWeatherCityDayFcDO, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
                BasePeriodUtils.setAllFiled(hisVO, ColumnUtil.listToMap(period, Constants.LOAD_CURVE_START_WITH_ZERO));
                synthesizeWeatherCityDayHisDOS.add(hisVO);
            }
            fiveDateBefore = new java.sql.Date(DateUtils.addDays(fiveDateBefore,1).getTime());
        }
        datas.put(mapKey, synthesizeWeatherCityDayHisDOS);
    }

    /**
     * 节假日数据
     *
     * @param datas
     * @throws Exception
     */
    private void mergeHolidayData(Map<String, Object> datas) throws Exception {
        List<HolidayDO> holidayVOS = algorithmForecastDataService.findAllHolidays();
        holidayVOS.sort(Comparator.comparing(HolidayDO::getDate, Comparator.nullsFirst(Comparator.naturalOrder())));
        datas.put(HOLIDAY_INFO, holidayVOS);
    }


    @Autowired
    FcWeatherFeatureService fcWeatherFeatureService;

    @Autowired
    WeatherFeatureCityDayHisDAO weatherFeatureCityDayHisDAO;

    @Autowired
    WeatherFeatureCityWeekFcDAO weatherFeatureCityWeekFcDAO;

    @Autowired
    WeatherFeatureCityWeekFcService weatherFeatureCityWeekFcService;

    /**
     * 匹配历史气象特性数据
     *
     * @param datas
     * @param cityId
     * @param endFcstDay
     * @param lastYearStartDate
     * @throws Exception
     */
    public void mergeWeatherFeature(Map<String, Object> datas, String cityId, Date endFcstDay,
        Date lastYearStartDate, Integer fcstDayWeatherType, String weatherCode,Date baseDate,List<AlgorithmEnum> algorithmEnums) throws Exception {

        SettingSystemDO fc_weather_type = settingSystemService.findByFieldId(SystemConstant.DEFAULT_FC_WEATHER_SOURCE);
        String fc_value_type = fc_weather_type.getValue();
        if ("1".equals(cityId) && algorithmEnums.size() == 1 && AlgorithmEnum.COPY_FORECAST_INNOVATION.equals(algorithmEnums.get(0))){
            cityId = "1";
        }else {
            cityId = cityService.findWeatherCityId(cityId);
            //cityId = algorithmForecastDataService.findProvinceId(cityId);
        }
        Integer weatherType = 0;
        String type = coreConfigInfo.getRuntimeParam("forecast.model.weather.feature");
        if (type != null) {
            weatherType = Integer.valueOf(type);
        }
        if (weatherType == 0) {//训练数据用历史数据做预测
            List<WeatherFeatureCityDayHisDO> weatherFeatureHisVOS =
                algorithmForecastDataService.findWeatherFeatureCityDayHisDO(cityId, lastYearStartDate, endFcstDay);
            List<WeatherCityHisDO> weatherData = ( List<WeatherCityHisDO>)datas.get(DATA_WEATHER_KEY);

            Map<java.sql.Date, List<WeatherCityHisDO>> collect = weatherData.stream()
                .collect(Collectors.groupingBy(WeatherCityHisDO::getDate));

            java.sql.Date fiveDateBefore = new java.sql.Date(DateUtils.addDays(baseDate,-10).getTime());
            while (fiveDateBefore.before(new java.sql.Date(DateUtils.addDays(endFcstDay,1).getTime()))){
                List<WeatherCityHisDO> weatherCityHisDOS = collect.get(fiveDateBefore);
                List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOS = weatherFeatureCityDayHisDAO
                    .statisticsDayFeature(weatherCityHisDOS);
                if(CollectionUtils.isNotEmpty(weatherFeatureCityDayHisDOS)){
                    WeatherFeatureCityDayHisDO weatherFeatureCityDayHisDO = weatherFeatureCityDayHisDOS.get(0);
                    Iterator<WeatherFeatureCityDayHisDO> iterator = weatherFeatureHisVOS.iterator();
                    while (iterator.hasNext()){
                        if(iterator.next().getDate().compareTo(fiveDateBefore) == 0){
                            iterator.remove();
                        }
                    }
                    weatherFeatureHisVOS.add(weatherFeatureCityDayHisDO);
                }
                fiveDateBefore = new java.sql.Date(DateUtils.addDays(fiveDateBefore,1).getTime());
            }
//            if (CollectionUtils.isNotEmpty(weatherFeatureHisVOS)) {
//                WeatherFeatureCityDayHisDO dayHisVO = weatherFeatureHisVOS.get(weatherFeatureHisVOS.size() - 1);
//                //历史数据的结束日期
//                Date hisEndDate = dayHisVO.getDate();
//                //如果历史数据结束日期在预测结束日期之前 则说明缺少历史数据 用预测数据补齐
//                while (hisEndDate.before(endFcstDay)) {
//
//                    List<? extends  BaseWeatherFeatureCityDayFcDO> featureFcVOS = fcWeatherFeatureService
//                        .findFcWeatherFeatureData(cityId, DateUtil.getMoveDay(hisEndDate, 1),
//                            DateUtil.getMoveDay(hisEndDate, 1),fc_value_type);
//
//                    if (CollectionUtils.isEmpty(featureFcVOS)) {
//                        //预测数据为空 则使用昨天的预测数据补齐
//                        WeatherFeatureCityDayHisDO yesterdayWeatherFeatureCityDayHisDO = weatherFeatureHisVOS
//                            .get(weatherFeatureHisVOS.size() - 1);
//                        WeatherFeatureCityDayHisDO weatherFeatureCityDayHisDO = new WeatherFeatureCityDayHisDO();
//                        BeanUtils.copyProperties(yesterdayWeatherFeatureCityDayHisDO,weatherFeatureCityDayHisDO);
//                        weatherFeatureCityDayHisDO.setDate(new java.sql.Date(DateUtil.getMoveDay(hisEndDate, 1).getTime()));
//                        weatherFeatureHisVOS.add(weatherFeatureCityDayHisDO);
//                    } else {
//                        //预测数据中有, 取预测中的数据
//                        for (BaseWeatherFeatureCityDayFcDO vo : featureFcVOS) {
//                            WeatherFeatureCityDayHisDO hisVO = new WeatherFeatureCityDayHisDO();
//                            BeanUtils.copyProperties(vo, hisVO);
//                            hisVO.setDate(new java.sql.Date(DateUtil.getMoveDay(hisEndDate, 1).getTime()));
//                            weatherFeatureHisVOS.add(hisVO);
//                        }
//                    }
//                    hisEndDate = DateUtils.addDays(hisEndDate, 1);
//                }
//            }
            datas.put(WEATHER_FEATURE_HIS, weatherFeatureHisVOS);
        } else { //从预测库中取数据
            List<WeatherFeatureCityDayHisDO> hisVOS = new ArrayList<>();

            List<? extends  BaseWeatherFeatureCityDayFcDO> weatherFeatureCityDayFcVOS = fcWeatherFeatureService
                .findFcWeatherFeatureData(cityId, DateUtil.getMoveDay(lastYearStartDate, 1),
                    DateUtil.getMoveDay(endFcstDay, 1),fc_value_type);


            for (BaseWeatherFeatureCityDayFcDO weatherFeatureCityDayFcVO : weatherFeatureCityDayFcVOS) {
                WeatherFeatureCityDayHisDO weatherFeatureCityDayHisVO = new WeatherFeatureCityDayHisDO();
                BeanUtils.copyProperties(weatherFeatureCityDayFcVO, weatherFeatureCityDayHisVO);
                hisVOS.add(weatherFeatureCityDayHisVO);
            }
            datas.put(WEATHER_FEATURE_HIS, hisVOS);
        }

        //获取预测日气象源特性数据
        getForecastDayWeatherFeature(datas, cityId, endFcstDay, weatherCode);

        if (fcstDayWeatherType != null) {
            //实施页面逻辑 自动预测不走这块
            List<WeatherFeatureCityDayHisDO> hisVOS = (List<WeatherFeatureCityDayHisDO>) datas.get(WEATHER_FEATURE_HIS);
            if (CollectionUtils.isEmpty(hisVOS)) {
                return;
            }
            Map<Date, WeatherFeatureCityDayHisDO> dateWeatherMap = hisVOS.stream()
                .collect(Collectors.toMap(e -> e.getDate(), e -> e, (oldv, curv) -> curv));
            //待预测日之前的气象使用历史气象特性，缺的日期使用预测补充
            dateWeatherMap.remove(endFcstDay);
            List<Date> dateList = dateWeatherMap.values().stream().map(WeatherFeatureCityDayHisDO::getDate)
                .collect(Collectors.toList());
            Date maxDate = Collections.max(dateList);
            List<WeatherFeatureCityDayFcDO> supplementWeatherCityFcDOS = algorithmForecastDataService
                .findWeatherFeatureCityDayFcDO(cityId, DateUtil.getMoveDay(maxDate, 1),
                    DateUtil.getMoveDay(endFcstDay, -1));
            if (!CollectionUtils.isEmpty(supplementWeatherCityFcDOS)) {
                supplementWeatherCityFcDOS.forEach(e -> {
                    WeatherFeatureCityDayHisDO weatherFeatureCityDayHisVO = new WeatherFeatureCityDayHisDO();
                    BeanUtils.copyProperties(e, weatherFeatureCityDayHisVO);
                    dateWeatherMap.put(weatherFeatureCityDayHisVO.getDate(), weatherFeatureCityDayHisVO);
                });
            }
            if (fcstDayWeatherType == 0) {
                //待预测日使用 历史气象特性
                List<WeatherFeatureCityDayHisDO> fcstDayWeatherVO = algorithmForecastDataService
                    .findWeatherFeatureCityDayHisDO(cityId, endFcstDay, endFcstDay);
                dateWeatherMap
                    .put(endFcstDay, CollectionUtils.isEmpty(fcstDayWeatherVO) ? null : fcstDayWeatherVO.get(0));
            } else {
                //待预测日使用 预测气象特性
                List<? extends  BaseWeatherFeatureCityDayFcDO> weatherCityFcVOS = fcWeatherFeatureService
                    .findFcWeatherFeatureData(cityId, endFcstDay,
                        endFcstDay,fc_value_type);


                if (CollectionUtils.isEmpty(weatherCityFcVOS)) {
                    dateWeatherMap.put(endFcstDay, null);
                } else {
                    WeatherFeatureCityDayHisDO hisVO = new WeatherFeatureCityDayHisDO();
                    BeanUtils.copyProperties(weatherCityFcVOS.get(0), hisVO);
                    dateWeatherMap.put(endFcstDay, hisVO);
                }
            }
            List<WeatherFeatureCityDayHisDO> resultWeatherFeature = new ArrayList<>();
            for (Date dateKey : dateWeatherMap.keySet()) {
                resultWeatherFeature.add(dateWeatherMap.get(dateKey));
            }
            resultWeatherFeature.remove(null);
            resultWeatherFeature = resultWeatherFeature.stream()
                .sorted(Comparator.comparing(WeatherFeatureCityDayHisDO::getDate)).collect(Collectors.toList());
            datas.put(WEATHER_FEATURE_HIS, resultWeatherFeature);
        }
    }

    private void getForecastDayWeatherFeature(Map<String, Object> datas, String cityId, Date endFcstDay,
        String weatherCode) throws Exception {
        if (WeatherSourceEnum.getEnumByCode(weatherCode) == null) {
            return;
        }
        List<WeatherFeatureCityDayHisDO> hisVOS = (List<WeatherFeatureCityDayHisDO>) datas.get(WEATHER_FEATURE_HIS);
        Map<java.sql.Date, List<WeatherFeatureCityDayHisDO>> his = hisVOS.stream()
            .collect(Collectors.groupingBy(WeatherFeatureCityDayHisDO::getDate));
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String endDateStr = format.format(endFcstDay);
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisVOS = his
            .get(new java.sql.Date(endFcstDay.getTime()));
        WeatherFeatureCityDayHisDO fcDayFeature = null;
        if (CollectionUtils.isNotEmpty(weatherFeatureCityDayHisVOS)) {
            fcDayFeature = weatherFeatureCityDayHisVOS.get(0);
        }
        if (fcDayFeature == null) {
            fcDayFeature = new WeatherFeatureCityDayHisDO();
            fcDayFeature.setDate(new java.sql.Date(endFcstDay.getTime()));
            fcDayFeature.setCityId(cityId);
        }

        if(WeatherSourceEnum.getEnumByCode(weatherCode) == WeatherSourceEnum.MODIFY){
            Map<Integer, WeatherCityFcModifyDO> collect = weatherCityFcModifyService
                .findModifyWeatherByDateAndType(endDateStr, null, cityId).stream()
                .collect(Collectors.toMap(WeatherCityFcModifyDO::getType, Function.identity(), (v1, v2)->v1));
            getFcFeature(fcDayFeature, collect);
        }
        if(WeatherSourceEnum.getEnumByCode(weatherCode) == WeatherSourceEnum.GDT){
            Map<Integer, WeatherCityFcClctGDTDO> map = weatherCityFcClctGDTDAO
                .findWeatherCityFcDO(cityId, null, endFcstDay, endFcstDay).stream()
                .collect(Collectors.toMap(WeatherCityFcClctGDTDO::getType, Function.identity(), (v1, v2) -> v1));
            getFcFeature(fcDayFeature, map);
        }
        if(WeatherSourceEnum.getEnumByCode(weatherCode) == WeatherSourceEnum.JT){
            Map<Integer, WeatherCityFcDO> map = algorithmForecastDataService.findFcWeather(cityId,
                endFcstDay, endFcstDay).stream()
                .collect(Collectors.toMap(WeatherCityFcDO::getType, Function.identity(), (v1, v2) -> v1));
            getFcFeature(fcDayFeature, map);
        }
        if(WeatherSourceEnum.getEnumByCode(weatherCode) == WeatherSourceEnum.ZHT){
            Map<Integer, WeatherCityFcClctZHTDO> map = weatherCityFcClctZHTDAO
                .findWeatherCityFcDO(cityId, null, endFcstDay, endFcstDay).stream()
                .collect(Collectors.toMap(WeatherCityFcClctZHTDO::getType, Function.identity(), (v1, v2) -> v1));
            getFcFeature(fcDayFeature, map);
        }
        if(WeatherSourceEnum.getEnumByCode(weatherCode) == WeatherSourceEnum.ZYT){
            Map<Integer, WeatherCityFcClctZYTDO> map = weatherCityFcClctZYTDAO
                .findWeatherCityFcDO(cityId, null, endFcstDay, endFcstDay).stream()
                .collect(Collectors.toMap(WeatherCityFcClctZYTDO::getType, Function.identity(), (v1, v2) -> v1));
            getFcFeature(fcDayFeature, map);
        }

    }

    private <T extends Base96DO> void getFcFeature(WeatherFeatureCityDayHisDO fcDayFeature,
        Map<Integer, T> collect) {
        if(collect == null || collect.size() < 0){
            return;
        }
        for (Map.Entry<Integer, T> entry : collect.entrySet()) {
            Integer key = entry.getKey();
            T value = entry.getValue();
            if (value != null) {
                List<BigDecimal> bigDecimals = BasePeriodUtils
                    .toList(value, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
                Map<String, BigDecimal> maxMinAvg = BasePeriodUtils
                    .getMaxMinAvg(bigDecimals.stream().filter(t -> t != null).collect(Collectors.toList()), 2);
                BigDecimal count = BigDecimalUtils.addAllValue(bigDecimals);
                if (WeatherEnum.WINDSPEED.getType().equals(key)) {
                    fcDayFeature.setMaxWinds(maxMinAvg.get("max"));
                    fcDayFeature.setAveWinds(maxMinAvg.get("avg"));
                    fcDayFeature.setMinWinds(maxMinAvg.get("min"));
                }

                if (WeatherEnum.RAINFALL.getType().equals(key)) {
                    fcDayFeature.setRainfall(count);
                }

                if (WeatherEnum.HUMIDITY.getType().equals(key)) {
                    fcDayFeature.setHighestHumidity(maxMinAvg.get("max"));
                    fcDayFeature.setAveHumidity(maxMinAvg.get("avg"));
                    fcDayFeature.setLowestHumidity(maxMinAvg.get("min"));
                }

                if (WeatherEnum.TEMPERATURE.getType().equals(key)) {
                    fcDayFeature.setHighestTemperature(maxMinAvg.get("max"));
                    fcDayFeature.setAveTemperature(maxMinAvg.get("avg"));
                    fcDayFeature.setLowestTemperature(maxMinAvg.get("min"));
                }
            }
        }
    }

    public void mergeWeatherWeekFeature(Map<String, Object> datas, String cityId,
        Date startFcstDayDate, Date endFcstDayDate) throws Exception {
        List<WeatherFeatureCityDayHisDO> hisVOS = (List<WeatherFeatureCityDayHisDO>) datas.get(WEATHER_FEATURE_HIS);
        Map<java.sql.Date, List<WeatherFeatureCityDayHisDO>> his = hisVOS.stream()
            .collect(Collectors.groupingBy(WeatherFeatureCityDayHisDO::getDate));
        // 获取周预测用户保存特性气象
        List<WeatherFeatureCityWeekFcServiceDO> weatherCityWeekFeature = weatherFeatureCityWeekFcService.findWeatherFeatureCityWeekFcDO(
            cityId, startFcstDayDate, endFcstDayDate);
        Map<java.sql.Date, List<WeatherFeatureCityWeekFcServiceDO>> fc = weatherCityWeekFeature.stream()
            .collect(Collectors.groupingBy(WeatherFeatureCityWeekFcServiceDO::getDate));
        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startFcstDayDate, endFcstDayDate);
        List<WeatherFeatureCityWeekFcServiceDO> weatherFeatureCityWeekFcServiceDOS = new ArrayList<>();
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOS = new ArrayList<>();
        for (Date date : listBetweenDay) {
            weatherFeatureCityDayHisDOS = his.get(new java.sql.Date(date.getTime()));
            weatherFeatureCityWeekFcServiceDOS = fc.get(new java.sql.Date(date.getTime()));
            if (CollectionUtils.isNotEmpty(weatherFeatureCityDayHisDOS) && CollectionUtils.isNotEmpty(
                weatherFeatureCityWeekFcServiceDOS)) {
                WeatherFeatureCityDayHisDO weatherFeatureCityDayHisDO = weatherFeatureCityDayHisDOS.get(0);
                WeatherFeatureCityWeekFcServiceDO weatherCityWeekFcFeature = weatherFeatureCityWeekFcServiceDOS.get(
                    0);
                weatherFeatureCityDayHisDO.setHighestTemperature(weatherCityWeekFcFeature.getHighestTemperature());
                weatherFeatureCityDayHisDO.setLowestTemperature(weatherCityWeekFcFeature.getLowestTemperature());
            } else if (CollectionUtils.isEmpty(weatherFeatureCityDayHisDOS) && CollectionUtils.isNotEmpty(
                weatherFeatureCityWeekFcServiceDOS)) {
                WeatherFeatureCityDayHisDO weatherFeatureCityDayHisDO = new WeatherFeatureCityDayHisDO();
                WeatherFeatureCityWeekFcServiceDO weatherCityWeekFcFeature = weatherFeatureCityWeekFcServiceDOS.get(0);
                weatherFeatureCityDayHisDO.setId(weatherCityWeekFcFeature.getId());
                weatherFeatureCityDayHisDO.setCityId(hisVOS.get(0).getCityId());
                weatherFeatureCityDayHisDO.setDate(new java.sql.Date(date.getTime()));
                weatherFeatureCityDayHisDO.setHighestTemperature(weatherCityWeekFcFeature.getHighestTemperature());
                weatherFeatureCityDayHisDO.setLowestTemperature(weatherCityWeekFcFeature.getLowestTemperature());
                weatherFeatureCityDayHisDO.setAveTemperature(
                    genAvgTemp(weatherCityWeekFcFeature.getHighestTemperature(),
                        weatherCityWeekFcFeature.getLowestTemperature()));
                hisVOS.add(weatherFeatureCityDayHisDO);
            }
        }
    }


        /**
         * 超短期预测
         */
    @Override
    public Result doShortForecast(String cityId, String caliberId, Date date, AlgorithmEnum algorithmEnum,
        Integer timeSpan, String startTimePoint, String fcHour)
        throws Exception {
        ShortForecastParam param = new ShortForecastParam();
        param.setForecastDate(date);
        param.setTimeSpan(String.valueOf(timeSpan));
        param.setStartTimePoint(startTimePoint);
        param.setForecastPoint(String.valueOf((Integer.valueOf(fcHour) * 60) / timeSpan));
        param.setCityId(cityId);
        param.setAlgorithmEnum(algorithmEnum);
        param.setCaliberId(caliberId);
        Map<String, Object> datas = new HashMap<>(16);
        if (ShortConstants.MINUTE.equals(timeSpan)) {
            PointHisData288(datas, cityId, caliberId, date, Integer.valueOf(startTimePoint));
        } else {
            PointHisData96(datas, cityId, caliberId, date, Integer.valueOf(startTimePoint));
        }
        Result executeResult = execute(param, datas);
        return executeResult;
    }

    /**
     * 15分钟间隔 构造96点历史数据对象
     *
     * @param datas      数据map
     * @param cityId     城市id
     * @param caliberId  口径id
     * @param startFcDay 开始时间
     * @param timePoint 预测起始点数
     * <AUTHOR>
     */
    private void PointHisData96(Map<String, Object> datas, String cityId, String caliberId, Date startFcDay,
        int timePoint) throws Exception {
        //查询起始日前30天的历史负荷 不包含当天
        Date lastMonthDay = DateUtil.getMoveDay(startFcDay, -31);
        List<LoadCityHisDO> resultList = new ArrayList<>();
        List<LoadCityHisDO> lastMonthHisDOS = algorithmForecastDataService.findHisLoad(cityId, caliberId,
            lastMonthDay, DateUtil.getMoveDay(startFcDay, -1));
        resultList.addAll(lastMonthHisDOS);
        Date lastYearDay = DateUtils.addYears(startFcDay, -1);
        //查询去年同期30天的历史负荷数据
        List<LoadCityHisDO> lastYearHisDOS = algorithmForecastDataService.findHisLoad(cityId, caliberId,
            DateUtil.getMoveDay(lastYearDay, -15), DateUtil.getMoveDay(lastYearDay, 15));
        resultList.addAll(lastYearHisDOS);
        //查询当天的最新采集的数据
        List<LoadCityHisDO> todayDOS = algorithmForecastDataService.findHisLoad(cityId, caliberId,
            startFcDay, startFcDay);
        List<BigDecimal> bigDecimals = BasePeriodUtils.toList(todayDOS.get(0),
            Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
        List<BigDecimal> zeroOrNullList = com.tsintergy.lf.core.constants.ColumnUtil.getZeroOrNullList(96, null);
        for (int i = 0; i < timePoint; i++) {
            zeroOrNullList.set(i, bigDecimals.get(i));
        }
        Map<String, BigDecimal> dataMap = com.tsintergy.lf.core.constants.ColumnUtil
            .listToMap(zeroOrNullList, Constants.LOAD_CURVE_START_WITH_ZERO);
        LoadCityHisDO today = new LoadCityHisDO();
        today.setDate(new java.sql.Date(startFcDay.getTime()));
        BasePeriodUtils.setAllFiled(today, dataMap);
        resultList.add(today);
        if (CollectionUtils.isEmpty(resultList)) {
            logger.error("城市id:{}算法执行失败，历史负荷数据为空", cityId);
            throw TsieExceptionUtils.newBusinessException("算法执行失败，历史负荷数据为空");
        }
        datas.put(DATA_LOAD_KEY, resultList);
    }

    /**
     * 5分钟间隔 构造96点历史数据对象
     *
     * @param datas 数据map
     * @param cityId 城市id
     * @param caliberId 口径id
     * @param startFcDay 开始时间
     * @param timePoint 预测起始点数
     * <AUTHOR>
     */
    private void PointHisData288(Map<String, Object> datas, String cityId, String caliberId, Date startFcDay,
        int timePoint) throws Exception {
        //查询起始日前30天的历史负荷 不包含当天
        Date lastMonthDay = DateUtil.getMoveDay(startFcDay, -31);
        List<LoadCityHis288DO> resultList = new ArrayList<>();
        List<LoadCityHis288DO> lastMonthHisDOS = algorithmForecastDataService.findHis288Load(cityId, caliberId,
            lastMonthDay, DateUtil.getMoveDay(startFcDay, -1));
        resultList.addAll(lastMonthHisDOS);
        Date lastYearDay = DateUtils.addYears(startFcDay, -1);
        //查询去年同期30天的历史负荷数据
        List<LoadCityHis288DO> lastYearHisDOS = algorithmForecastDataService.findHis288Load(cityId, caliberId,
            DateUtil.getMoveDay(lastYearDay, -15), DateUtil.getMoveDay(lastYearDay, 15));
        resultList.addAll(lastYearHisDOS);
        //查询当天的最新采集的数据
        List<LoadCityHis288DO> todayDOS = algorithmForecastDataService.findHis288Load(cityId, caliberId,
            startFcDay, startFcDay);
        if (CollectionUtils.isEmpty(todayDOS)) {
            throw TsieExceptionUtils.newBusinessException("算法执行失败，历史负荷数据为空");
        }
        List<BigDecimal> zeroOrNullList = com.tsintergy.lf.core.constants.ColumnUtil.getZeroOrNullList(288, null);
        List<BigDecimal> bigDecimals = BasePeriodUtils.toList(todayDOS.get(0),
            288, Constants.WEATHER_CURVE_START_WITH_ZERO);
        for (int i = 0; i < timePoint; i++) {
            zeroOrNullList.set(i, bigDecimals.get(i));
        }
        LoadCityHis288DO today = new LoadCityHis288DO();
        Map<String, BigDecimal> dataMap = com.tsintergy.lf.core.constants.ColumnUtil
            .listToMap(zeroOrNullList, Constants.LOAD_CURVE_START_WITH_ZERO);
        BasePeriodUtils.setAllFiled(today, dataMap);
        today.setDate(new java.sql.Date(startFcDay.getTime()));
        resultList.add(today);
        if (CollectionUtils.isEmpty(resultList)) {
            logger.error("城市id:{}算法执行失败，历史负荷数据为空", cityId);
            throw TsieExceptionUtils.newBusinessException("算法执行失败，历史负荷数据为空");
        }
        datas.put(DATA_LOAD_KEY, resultList);
    }

    /**
     * 获取平均温度
     * @param max
     * @param min
     * @return
     */
    private BigDecimal genAvgTemp(BigDecimal max, BigDecimal min){
        BigDecimal add = max.add(min);
        return add.divide(new BigDecimal(2),4,BigDecimal.ROUND_HALF_UP);
    }

}
