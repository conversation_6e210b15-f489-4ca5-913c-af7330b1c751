package com.tsintergy.lf.serviceimpl.impl;

import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.AlgorithmEnum;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.shortforecast.ShortForecastMetaData;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.shortforecast.ShortForecastResult;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Result;
import com.tsintergy.aif.algorithm.serviceimpl.client.support.GurNetworkInputHelper;
import com.tsintergy.lf.core.constants.AlgorithmPowerEnum;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.DateUtil;
import com.tsintergy.lf.core.constants.VoltageTypeEnum;
import com.tsintergy.lf.serviceapi.algorithm.api.GurNetworkAlgorithmService;
import com.tsintergy.lf.serviceapi.algorithm.api.LoadFcAlgorithmResultSaveService;
import com.tsintergy.lf.serviceapi.algorithm.dto.GurNetworkAlgorithmParam;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmParamService;
import com.tsintergy.lf.serviceapi.base.forecast.api.AutoForecastService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcWeekService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmParamDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcBasicWeekDO;
import com.tsintergy.lf.serviceapi.base.power.api.SmallPowerLoadFcService;
import com.tsintergy.lf.serviceapi.base.power.pojo.SmallPowerLoadFcBasicDO;
import com.tsintergy.lf.serviceapi.base.recall.api.LoadCityFcRecallService;
import com.tsintergy.lf.serviceapi.base.recall.pojo.LoadCityFcRecallDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcLoadForecastService;
import com.tsintergy.lf.serviceimpl.common.util.ColumnUtil;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/5/29 13:51
 **/
@Service("gurNetworkAlgorithmService")
public class GurNetworkAlgorithmServiceImpl implements GurNetworkAlgorithmService {

    @Autowired
    private GurNetworkAlgorithmClient gurNetworkAlgorithmClient;

    @Autowired
    private CaliberService caliberService;

    @Autowired
    private AlgorithmParamService algorithmParamService;


    @Autowired
    LoadFcAlgorithmResultSaveService loadFcAlgorithmResultSaveService;

    @Autowired
    AutoForecastService autoForecastService;

    @Autowired
    private LoadCityFcWeekService loadCityFcWeekService;

    @Autowired
    private SmallPowerLoadFcService smallPowerLoadFcService;

    @Autowired
    private WeatherCityFcLoadForecastService weatherCityFcLoadForecastService;

    @Autowired
    private LoadCityFcRecallService loadCityFcRecallService;

    @Override
    public void doGurNetworkForecast(String cityId,Date startDate, Date endDate, String caliberId,List<AlgorithmEnum> algorithmEnums) {
        CaliberDO caliberDO = caliberService.findCaliberDOByPk(caliberId);

        try {
            //算法参数列表
            List<AlgorithmParamDO> algorithmParam = algorithmParamService.getAlgorithmParamByAlgorithmIdNoCache(AlgorithmEnum.GUR_NETWORK.getId());
            Map<String, String> paramMap = algorithmParam.stream().collect(Collectors.toMap(AlgorithmParamDO::getParamEn, AlgorithmParamDO::getDefaultValue));
            for (AlgorithmEnum algorithmEnum : algorithmEnums) {
                GurNetworkAlgorithmParam gurNetworkAlgorithmParam = new GurNetworkAlgorithmParam(caliberDO.getName(),startDate,endDate,new String[]{cityId,caliberId});
                gurNetworkAlgorithmParam.setCaliberName(caliberDO.getName());
                gurNetworkAlgorithmParam.setCityId(cityId);
                gurNetworkAlgorithmParam.setCaliberId(caliberDO.getId());
                gurNetworkAlgorithmParam.setAlgorithmEnum(algorithmEnum);
                mergeAlgorithmParam(paramMap,gurNetworkAlgorithmParam);
                //使用的城市id 用逗号隔开
                List<String> userCityList = new ArrayList<>();
                if (Constants.PROVINCE.equals(cityId)){
                    String userCity = paramMap.get("UserCity");
                    userCityList = Arrays.asList(userCity.split(","));
                }else{
                    userCityList.add(cityId);
                }

                gurNetworkAlgorithmParam.setUserCityList(userCityList);
                String algorithmId = algorithmEnum.getId();
                try {
                    if (Constants.SYSTEM_LOAD_CALIBER.equals(caliberId) && Constants.PROVINCE_ID.equals(cityId)){
                        algorithmId = com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum.DP_FORECAST_2.getId();
                    }
                    ShortForecastResult forecastResult = gurNetworkAlgorithmClient.invoke(gurNetworkAlgorithmParam);
                    loadFcAlgorithmResultSaveService.saveShortForecastMetaData(cityId,caliberId,startDate,endDate,forecastResult,algorithmId);
                    //预测完成保存预测使用气象数据
                    String finalAlgorithmId = algorithmId;
                    DateUtil.getListBetweenDay(startDate, endDate).forEach(date -> {
                        weatherCityFcLoadForecastService
                            .insertOrUpdateWeatherInfo(cityId, date, finalAlgorithmId, caliberId);
                    });
                    if (algorithmEnums.size() == 1){
                        for (ShortForecastMetaData shortForecastMetaDatum : forecastResult.getShortForecastMetaData()) {
                            doSaveOrUpdateWeekResult(cityId,caliberId,algorithmId,shortForecastMetaDatum.getDate(),shortForecastMetaDatum.getValue());
                        }
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void doGurNetworkFullForecast(String cityId, Date startDate, Date endDate, String caliberId, List<AlgorithmEnum> algorithmEnums) {
        CaliberDO caliberDO = caliberService.findCaliberDOByPk(caliberId);

        try {
            //算法参数列表
            List<AlgorithmParamDO> algorithmParam = algorithmParamService.getAlgorithmParamByAlgorithmIdNoCache(AlgorithmEnum.GUR_NETWORK.getId());
            Map<String, String> paramMap = algorithmParam.stream().collect(Collectors.toMap(AlgorithmParamDO::getParamEn, AlgorithmParamDO::getDefaultValue));
            for (AlgorithmEnum algorithmEnum : algorithmEnums) {
                GurNetworkAlgorithmParam gurNetworkAlgorithmParam = new GurNetworkAlgorithmParam(caliberDO.getName(),startDate,endDate,new String[]{cityId,caliberId});
                gurNetworkAlgorithmParam.setCaliberName(caliberDO.getName());
                gurNetworkAlgorithmParam.setCityId(cityId);
                gurNetworkAlgorithmParam.setCaliberId(caliberDO.getId());
                gurNetworkAlgorithmParam.setAlgorithmEnum(algorithmEnum);
                gurNetworkAlgorithmParam.setFull(true);
                mergeAlgorithmParam(paramMap,gurNetworkAlgorithmParam);
                //使用的城市id 用逗号隔开
                List<String> userCityList = new ArrayList<>();
                if (Constants.PROVINCE.equals(cityId)){
                    String userCity = paramMap.get("UserCity");
                    userCityList = Arrays.asList(userCity.split(","));
                }else{
                    userCityList.add(cityId);
                }

                gurNetworkAlgorithmParam.setUserCityList(userCityList);
                try {
                    Map<Date, List<BigDecimal>> powerMap = smallPowerLoadFcService.findPowerFcLoad(cityId, AlgorithmPowerEnum.RANGE.id, startDate, endDate, VoltageTypeEnum.TOTAL.type)
                            .stream().collect(Collectors.toMap(SmallPowerLoadFcBasicDO::getDateTime, SmallPowerLoadFcBasicDO::getLoadList));
                    ShortForecastResult forecastResult = gurNetworkAlgorithmClient.invoke(gurNetworkAlgorithmParam);
                    List<ShortForecastMetaData> shortForecastMetaData = new ArrayList<>();
                    for (ShortForecastMetaData shortForecastMetaDatum : forecastResult.getShortForecastMetaData()) {
                        Date date = shortForecastMetaDatum.getDate();
                        List<BigDecimal> value = shortForecastMetaDatum.getValue();
                        List<BigDecimal> power = powerMap.get(date);
                        if (!CollectionUtils.isEmpty(power)){
                            //用电需求预测减去分布式光伏预测
                            List<BigDecimal> list = fullSub(value, power);
                            ShortForecastMetaData shortForecastMetaData1 = new ShortForecastMetaData(date,list);
                            shortForecastMetaData.add(shortForecastMetaData1);
                            doSaveOrUpdateWeekResult(cityId,caliberId,com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum.DP_FORECAST_2.getId(),date,list);
                        }
                    }
                    ShortForecastResult shortForecastResult = new ShortForecastResult(algorithmEnum,shortForecastMetaData);
                    loadFcAlgorithmResultSaveService.saveShortForecastMetaData(cityId,caliberId,startDate,endDate,shortForecastResult,com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum.DP_FORECAST_2.getId());


                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void doGurNetworkRecallForecast(String cityId, Date startDate, Date endDate, String caliberId,
        List<AlgorithmEnum> algorithmEnums, Integer pointNum, Integer weatherType, Integer type, Boolean isRecall) {
        CaliberDO caliberDO = caliberService.findCaliberDOByPk(caliberId);

        try {
            //算法参数列表
            List<AlgorithmParamDO> algorithmParam = algorithmParamService.getAlgorithmParamByAlgorithmIdNoCache(AlgorithmEnum.GUR_NETWORK.getId());
            Map<String, String> paramMap = algorithmParam.stream().collect(Collectors.toMap(AlgorithmParamDO::getParamEn, AlgorithmParamDO::getDefaultValue));
            for (AlgorithmEnum algorithmEnum : algorithmEnums) {
                GurNetworkAlgorithmParam gurNetworkAlgorithmParam = new GurNetworkAlgorithmParam(caliberDO.getName(),startDate,endDate,new String[]{cityId,caliberId});
                gurNetworkAlgorithmParam.setCaliberName(caliberDO.getName());
                gurNetworkAlgorithmParam.setCityId(cityId);
                gurNetworkAlgorithmParam.setCaliberId(caliberDO.getId());
                gurNetworkAlgorithmParam.setAlgorithmEnum(algorithmEnum);
                gurNetworkAlgorithmParam.setPoint(pointNum);
                gurNetworkAlgorithmParam.setRecall(isRecall);
                gurNetworkAlgorithmParam.setFcDayWeatherType(weatherType);
                gurNetworkAlgorithmParam.setFcType(type);
                mergeAlgorithmParam(paramMap,gurNetworkAlgorithmParam);
                //使用的城市id 用逗号隔开
                List<String> userCityList = new ArrayList<>();
                if (Constants.PROVINCE.equals(cityId)){
                    String userCity = paramMap.get("UserCity");
                    userCityList = Arrays.asList(userCity.split(","));
                }else{
                    userCityList.add(cityId);
                }

                gurNetworkAlgorithmParam.setUserCityList(userCityList);
                gurNetworkAlgorithmParam.setRecall(true);
                String algorithmId = algorithmEnum.getId();
                try {
                    if (Constants.SYSTEM_LOAD_CALIBER.equals(caliberId) && Constants.PROVINCE_ID.equals(cityId)){
                        algorithmId = com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum.DP_FORECAST_2.getId();
                    }
                    ShortForecastResult forecastResult = gurNetworkAlgorithmClient.invoke(gurNetworkAlgorithmParam);
                    doSaveOrUpdateGruRecallResult(cityId,caliberId,forecastResult,algorithmId);
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void doSaveOrUpdateGruRecallResult(String cityId, String caliberId, Result result,String algorithmId) {
        ShortForecastResult shortForecastResult = (ShortForecastResult) result;
        List<ShortForecastMetaData> shortForecastMetaData = shortForecastResult.getShortForecastMetaData();
        if (CollectionUtils.isEmpty(shortForecastMetaData)) {
            throw TsieExceptionUtils.newBusinessException("算法执行失败，没有生成FILE_OUT文件");
        }
        Map<Date, List<ShortForecastMetaData>> metaMap = shortForecastMetaData.stream().collect(Collectors.groupingBy(ShortForecastMetaData::getDate));
        for (Map.Entry<Date, List<ShortForecastMetaData>> map : metaMap.entrySet()) {
            Date date = map.getKey();
            List<ShortForecastMetaData> metaDataList = map.getValue();
            for (ShortForecastMetaData metaData : metaDataList) {
                try {
                    List<BigDecimal> value = metaData.getValue();
                    Map<String, BigDecimal> valueMap = com.tsintergy.lf.core.util.ColumnUtil
                        .listToMap(value, Constants.LOAD_CURVE_START_WITH_ZERO);
                    LoadCityFcRecallDO loadCityFcRecallDO = new LoadCityFcRecallDO();
                    loadCityFcRecallDO.setAlgorithmId(algorithmId);
                    loadCityFcRecallDO.setDate(new java.sql.Date(date.getTime()));
                    loadCityFcRecallDO.setCityId(cityId);
                    loadCityFcRecallDO.setCaliberId(caliberId);
                    BasePeriodUtils.setAllFiled(loadCityFcRecallDO,valueMap);
                    loadCityFcRecallService.doSaveOrUpdateLoadCityFcDO96(loadCityFcRecallDO);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public void doSaveOrUpdateWeekResult(String cityId, String caliberId,String algorithmId, Date date, List<BigDecimal> value) {
            try {
//                String algorithmId = AlgorithmEnum.GUR_NETWORK.getId();
                LoadCityFcBasicWeekDO weekLoadFc = loadCityFcWeekService.getWeekLoadFc(date, cityId, caliberId, algorithmId);
                if (weekLoadFc == null) {
                    LoadCityFcBasicWeekDO loadCityFcBasicWeekDO = new LoadCityFcBasicWeekDO(
                            new java.sql.Date(date.getTime()), cityId,
                            caliberId,
                            algorithmId);
                    loadCityFcBasicWeekDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
                    BasePeriodUtils.setAllFiled(loadCityFcBasicWeekDO,
                            com.tsintergy.lf.serviceimpl.common.util.ColumnUtil.listToMap(value, Constants.LOAD_CURVE_START_WITH_ZERO));
                    loadCityFcWeekService.doCreateAndFlush(loadCityFcBasicWeekDO);
                } else {
                    BasePeriodUtils.setAllFiled(weekLoadFc,
                            ColumnUtil.listToMap(value, Constants.LOAD_CURVE_START_WITH_ZERO));
                    loadCityFcWeekService.doUpdateLoadCityFcWeekDO(weekLoadFc);
                }
            } catch (Exception e) {
            }
    }

    /**
     * 算法参数赋值
     * @param paramMap
     * @param gurNetworkAlgorithmParam
     */
    private void mergeAlgorithmParam(Map<String, String> paramMap, GurNetworkAlgorithmParam gurNetworkAlgorithmParam) {
        gurNetworkAlgorithmParam.setUseGPU(paramMap.get(GurNetworkInputHelper.USE_GPU));
        gurNetworkAlgorithmParam.setGPUCardMemory(paramMap.get(GurNetworkInputHelper.GPU_CARD_MEMORY));
        gurNetworkAlgorithmParam.setCPUThreads(paramMap.get(GurNetworkInputHelper.CPU_THREADS));
        gurNetworkAlgorithmParam.setTrainBeginDay(paramMap.get(GurNetworkInputHelper.TRAIN_BEGIN_DAY));
        gurNetworkAlgorithmParam.setWindowDate(paramMap.get(GurNetworkInputHelper.WINDOW_DATE));
        gurNetworkAlgorithmParam.setWindowTmp(paramMap.get(GurNetworkInputHelper.WINDOW_TMP));
        gurNetworkAlgorithmParam.setWindowLoad(paramMap.get(GurNetworkInputHelper.WINDOW_LOAD));
        gurNetworkAlgorithmParam.setBatchSize(paramMap.get(GurNetworkInputHelper.BATCH_SIZE));
        gurNetworkAlgorithmParam.setDecayInterval(paramMap.get(GurNetworkInputHelper.DECAY_INTERVAL));
        gurNetworkAlgorithmParam.setDecayRatio(paramMap.get(GurNetworkInputHelper.DECAY_RATIO));
        gurNetworkAlgorithmParam.setTrainingRounds(paramMap.get(GurNetworkInputHelper.TRAINING_ROUNDS));
        gurNetworkAlgorithmParam.setTrainingLr(paramMap.get(GurNetworkInputHelper.TRAINING_LR));
        gurNetworkAlgorithmParam.setFinetuningNumFreeLayer(paramMap.get(GurNetworkInputHelper.FINETUNING_NUM_FREE_LAYER));
        gurNetworkAlgorithmParam.setFinetuningRounds(paramMap.get(GurNetworkInputHelper.FINETUNING_ROUNDS));
        gurNetworkAlgorithmParam.setFinetuningNumSyn(paramMap.get(GurNetworkInputHelper.FINETUNING_NUM_SYN));
        gurNetworkAlgorithmParam.setFinetuningLr(paramMap.get(GurNetworkInputHelper.FINETUNING_LR));
    }

    private static List<BigDecimal> fullSub(List<BigDecimal> fc, List<BigDecimal> power) {
        List<BigDecimal> result = com.tsintergy.aif.algorithm.serviceapi.base.util.ColumnUtil.getZeroOrNullList(Constants.LOAD_CURVE_POINT_NUM,null);
        for (int i = 0; i < fc.size(); i++) {
            BigDecimal fcLoad = fc.get(i);
            if (Objects.nonNull(fcLoad)){
                BigDecimal powerLoad = power.get(i);
                if (Objects.nonNull(powerLoad)){
                    result.set(i,fcLoad.subtract(powerLoad));
                }
            }
        }
        return result ;
    }
}
