/**
 * Copyright(C),2015‐2020,北京清能互联科技有限公司
 * Author:yangjin
 * Date:2020/5/1218:33
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.weather.impl;


import com.tsintergy.lf.serviceapi.base.weather.api.WeatherStationFcClctGDTService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationFcClctGDTDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherStationFcClctGDTDAO;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *Description: <br>
 *
 *<AUTHOR>
 *@create 2020/5/12
 *@since 1.0.0
 */
@Service
public class WeatherStationFcClctGDTImpl  implements WeatherStationFcClctGDTService {

    @Autowired
    WeatherStationFcClctGDTDAO weatherStationFcClctGDTDAO;

    @Override
    public List<WeatherStationFcClctGDTDO> findByDateAndstationId(Date startDate, Date endDate, String stationId,
        Integer type) throws Exception {
        List<WeatherStationFcClctGDTDO> weatherCityFcDO = weatherStationFcClctGDTDAO
            .findWeatherCityFcDO(type,startDate,endDate, stationId);
        return weatherCityFcDO;
    }

    @Override
    public List<WeatherStationFcClctGDTDO> findByDateAndstationIds(Date startDate, Date endDate, List<String> stationIds, Integer type) throws Exception {
        List<WeatherStationFcClctGDTDO> weatherCityFcDO = weatherStationFcClctGDTDAO
                .findWeatherCityFcDOS(type,startDate,endDate, stationIds);
        return weatherCityFcDO;
    }
}