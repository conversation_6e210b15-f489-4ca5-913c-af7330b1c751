package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityStationFcBasicGDTService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityStationFcBasicGDTDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityStationFcBasicGDTDAO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.UUID;

@Service("weatherCityStationFcBasicGDTService")
public class WeatherCityStationFcBasicGDTServiceImpl implements WeatherCityStationFcBasicGDTService {

    @Autowired
    WeatherCityStationFcBasicGDTDAO weatherCityStationFcBasicGDTDAO;

    @Override
    public void saveOrUpdate(WeatherCityStationFcBasicGDTDO weatherCityStationFcBasicGDTDO) throws Exception {
        List<WeatherCityStationFcBasicGDTDO> weatherCityFcDOS = weatherCityStationFcBasicGDTDAO
                .findWeatherCityFcDO(weatherCityStationFcBasicGDTDO.getCityId(), weatherCityStationFcBasicGDTDO.getType(),
                        weatherCityStationFcBasicGDTDO.getDate(), weatherCityStationFcBasicGDTDO.getDate());
        if (CollectionUtils.isNotEmpty(weatherCityFcDOS)){
            WeatherCityStationFcBasicGDTDO cityFcClctGDTDO = weatherCityFcDOS.get(0);
            weatherCityStationFcBasicGDTDO.setId(cityFcClctGDTDO.getId());
        }else {
            weatherCityStationFcBasicGDTDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-",""));
        }
        weatherCityStationFcBasicGDTDAO.saveOrUpdateEntityByTemplate(weatherCityStationFcBasicGDTDO);
    }

    @Override
    public List<WeatherCityStationFcBasicGDTDO> findFcWeatherData(String cityId, Integer type, Date startDate, Date endDate) throws Exception {
        return weatherCityStationFcBasicGDTDAO.findWeatherCityFcDO(cityId,type,startDate,endDate);
    }
}
