package com.tsintergy.lf.serviceimpl.recall.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.serviceapi.base.recall.pojo.LoadCityFcRecallDO;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2025-05-08
 * @since 1.0.0
 */
@Component
public class LoadCityFcRecallDAO extends BaseAbstractDAO<LoadCityFcRecallDO> {

    /**
     * 获取某个口径预测负荷
     *
     * @param cityId      城市ID
     * @param caliberId   口径ID
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param algorithmId 算法ID
     * @return
     */
    public List<LoadCityFcRecallDO> getLoadCityFcDOs(String cityId, String caliberId, String algorithmId, Date startDate,
        Date endDate) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != algorithmId) {
            param.getQueryConditions().put("_se_algorithmId", algorithmId);
        }
        if (null != cityId) {
            param.getQueryConditions().put("_se_cityId", cityId);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_se_caliberId", caliberId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<LoadCityFcRecallDO> loadCityFcDOS = this.query(param).getDatas();
        return loadCityFcDOS;
    }

    /**
     * 查询预测负荷数据
     *
     * @param cityId      城市ID
     * @param caliberId   口径ID
     * @param algorithmId 算法ID
     * @param date        日期
     * @return
     */
    public LoadCityFcRecallDO getLoadCityFcDO(String cityId, String caliberId, String algorithmId, Date date) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("_ne_date", new java.sql.Date(date.getTime()));
        param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        param.getQueryConditions().put("_ne_cityId", cityId);
        param.getQueryConditions().put("_ne_caliberId", caliberId);
        List<LoadCityFcRecallDO> loadCityFcDOS = this.query(param).getDatas();
        if (loadCityFcDOS != null && loadCityFcDOS.size() > 0) {
            return loadCityFcDOS.get(0);
        }
        return null;
    }

    /**
     * 查询上报预测负荷数据
     *
     * @param cityId    城市ID
     * @param caliberId 口径ID
     * @param date      日期
     * @return
     */
    public LoadCityFcRecallDO getReportLoadCityFcDO(String cityId, String caliberId, Date date) throws Exception {
        if (cityId == null) {
            throw new BusinessException("T706", "城市ID不可为空");
        }
        if (caliberId == null) {
            throw new BusinessException("T706", "口径ID不可为空");
        }
        if (date == null) {
            throw new BusinessException("T706", "日期不可为空");
        }
        DBQueryParam param = DBQueryParamBuilder.create().build();

        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("_ne_date", new java.sql.Date(date.getTime()));
        param.getQueryConditions().put("_ne_report", true);
        param.getQueryConditions().put("_se_cityId", cityId);
        param.getQueryConditions().put("_se_caliberId", caliberId);
        List<LoadCityFcRecallDO> loadCityFcDOS = this.query(param).getDatas();
        if (loadCityFcDOS != null && loadCityFcDOS.size() > 0) {
            return loadCityFcDOS.get(0);
        }
        return null;
    }

    /**
     * 获取预测上报负荷
     *
     * @param cityId    城市ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return
     */
    public List<LoadCityFcRecallDO> getReportLoadCityFcDOs(String cityId, Date startDate, Date endDate, String caliberId,
        String algorithmId) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();

        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != algorithmId) {
            param.getQueryConditions().put("_se_algorithmId", algorithmId);
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != cityId) {
            param.getQueryConditions().put("_se_cityId", cityId);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        param.getQueryConditions().put("_ne_report", true);
        param.setOrderby("date");
        param.setDesc("0");
        List<LoadCityFcRecallDO> loadCityFcDOS = this.query(param).getDatas();
        return loadCityFcDOS;
    }

    public List<LoadCityFcRecallDO> getLoadCityFcDOs(String cityId, Date startDate, Date endDate, String caliberId,
        String algorithmId) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();

        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != algorithmId) {
            param.getQueryConditions().put("_se_algorithmId", algorithmId);
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != cityId) {
            param.getQueryConditions().put("_se_cityId", cityId);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<LoadCityFcRecallDO> loadCityFcDOS = this.query(param).getDatas();
        return loadCityFcDOS;
    }

    /**
     * 保存或更新
     *
     * @param loadCityFcDO
     * @return
     */
    public LoadCityFcRecallDO doSaveOrUpdateLoadCityFcDO(LoadCityFcRecallDO loadCityFcDO) throws Exception {
        LoadCityFcRecallDO oldVO = this.getLoadCityFcDO(loadCityFcDO.getCityId(), loadCityFcDO.getCaliberId(),
            loadCityFcDO.getAlgorithmId(), loadCityFcDO.getDate());
        if (oldVO != null) {
            String id = oldVO.getId();
            ColumnUtil.copyPropertiesIgnoreNull(loadCityFcDO, oldVO);
            oldVO.setId(id);
            oldVO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            this.getSession().flush();
            this.getSession().clear();
            return this.updateAndFlush(oldVO);
        } else {
            return this.createAndFlush(loadCityFcDO);
        }
    }

    public LoadCityFcRecallDO getRecommendLoadCityFcDO(String cityId, String caliberId, Date date) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != cityId) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        if (null != date) {
            param.getQueryConditions().put("_de_date", new java.sql.Date(date.getTime()));
        }
        param.getQueryConditions().put("_ne_recommend", true);
        param.setOrderby("date");
        param.setDesc("0");
        List<LoadCityFcRecallDO> loadCityFcDOS = this.query(param).getDatas();
        if (!CollectionUtils.isEmpty(loadCityFcDOS)) {
            return loadCityFcDOS.get(0);
        }
        return null;
    }

    public LoadCityFcRecallDO getReportLoadCityFcDO(Date date, String cityId, String caliberId, String algorithmId, Boolean report) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();

        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (date != null) {
            param.getQueryConditions().put("_ne_date", new java.sql.Date(date.getTime()));
        }
        if (StringUtils.isNotBlank(algorithmId)) {
            param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        }
        if (StringUtils.isNotBlank(cityId)) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        if (report != null) {
            param.getQueryConditions().put("_ne_report", report);
        }
        List<LoadCityFcRecallDO> loadCityFcDOS = this.query(param).getDatas();
        if (loadCityFcDOS != null && loadCityFcDOS.size() > 0) {
            return loadCityFcDOS.get(0);
        }
        return null;
    }

    public List<LoadCityFcRecallDO> getReportFc(Date startDate, Date endDate, String cityId, String caliberId, String algorithmId) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();

        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (StringUtils.isNotBlank(algorithmId)) {
            param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        }
        if (StringUtils.isNotBlank(cityId)) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        param.getQueryConditions().put("_ne_report", true);
        List<LoadCityFcRecallDO> loadCityFcDOS = this.query(param).getDatas();
        return loadCityFcDOS;
    }

    public List<LoadCityFcRecallDO> getLoadCityFcDOInAlgorithmId(String cityId, String caliberId, List<String> algorithmIds, Date startDate,
        Date endDate) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();

        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != algorithmIds) {
            param.getQueryConditions().put("_sin_algorithmId", algorithmIds);
        }
        if (null != cityId) {
            param.getQueryConditions().put("_se_cityId", cityId);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_se_caliberId", caliberId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<LoadCityFcRecallDO> loadCityFcDOS = this.query(param).getDatas();
        return loadCityFcDOS;
    }

    public List<LoadCityFcRecallDO> findLoadCityFcReportByUserId(String cityId, String caliberId, String userId, Date startDate, Date endDate) {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != userId) {
            param.getQueryConditions().put("_se_userId", userId);
        }
        if (null != cityId) {
            param.getQueryConditions().put("_se_cityId", cityId);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_se_caliberId", caliberId);
        }
        param.getQueryConditions().put("_ne_report", true);
        param.setOrderby("date");
        param.setDesc("0");
        param.setOrderby("date");
        param.setDesc("0");
        List<LoadCityFcRecallDO> loadCityFcDOS = this.query(param).getDatas();
        return loadCityFcDOS;
    }

}
