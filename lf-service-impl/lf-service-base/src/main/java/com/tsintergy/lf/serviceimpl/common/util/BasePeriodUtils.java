/**
 * Copyright(C),2015-2018,北京清能互联科技有限公司
 * Author:   jxm
 * Date:    2018/11/2711:06
 * History:
 * <author><time><version><desc>
 */
package com.tsintergy.lf.serviceimpl.common.util;


import com.tsintergy.lf.serviceapi.base.base.pojo.Base96DO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.BaseWeatherStatisticsMonthAccuracyDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.BaseWeatherStatisticsYearAccuracyDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.StatisticsAccuracyWeatherCityMonthHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.StatisticsAccuracyWeatherCityYearHisDO;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 *Description:96/24点数组<br>
 *
 *<AUTHOR>
 *@create2018/11/27
 *@since1.0.0
 */
public class BasePeriodUtils {
    public static final String[] column96 = { "t0000" ,
            "t0015" ,
            "t0030" ,
            "t0045" ,
            "t0100" ,
            "t0115" ,
            "t0130" ,
            "t0145" ,
            "t0200" ,
            "t0215" ,
            "t0230" ,
            "t0245" ,
            "t0300" ,
            "t0315" ,
            "t0330" ,
            "t0345" ,
            "t0400" ,
            "t0415" ,
            "t0430" ,
            "t0445" ,
            "t0500" ,
            "t0515" ,
            "t0530" ,
            "t0545" ,
            "t0600" ,
            "t0615" ,
            "t0630" ,
            "t0645" ,
            "t0700" ,
            "t0715" ,
            "t0730" ,
            "t0745" ,
            "t0800" ,
            "t0815" ,
            "t0830" ,
            "t0845" ,
            "t0900" ,
            "t0915" ,
            "t0930" ,
            "t0945" ,
            "t1000" ,
            "t1015" ,
            "t1030" ,
            "t1045" ,
            "t1100" ,
            "t1115" ,
            "t1130" ,
            "t1145" ,
            "t1200" ,
            "t1215" ,
            "t1230" ,
            "t1245" ,
            "t1300" ,
            "t1315" ,
            "t1330" ,
            "t1345" ,
            "t1400" ,
            "t1415" ,
            "t1430" ,
            "t1445" ,
            "t1500" ,
            "t1515" ,
            "t1530" ,
            "t1545" ,
            "t1600" ,
            "t1615" ,
            "t1630" ,
            "t1645" ,
            "t1700" ,
            "t1715" ,
            "t1730" ,
            "t1745" ,
            "t1800" ,
            "t1815" ,
            "t1830" ,
            "t1845" ,
            "t1900" ,
            "t1915" ,
            "t1930" ,
            "t1945" ,
            "t2000" ,
            "t2015" ,
            "t2030" ,
            "t2045" ,
            "t2100" ,
            "t2115" ,
            "t2130" ,
            "t2145" ,
            "t2200" ,
            "t2215" ,
            "t2230" ,
            "t2245" ,
            "t2300" ,
            "t2315" ,
            "t2330" ,
            "t2345" };
    public static final String[] column24 = {
            "t0000" ,
            "t0100" ,
            "t0200" ,
            "t0300" ,
            "t0400" ,
            "t0500" ,
            "t0600" ,
            "t0700" ,
            "t0800" ,
            "t0900" ,
            "t1000" ,
            "t1100" ,
            "t1200" ,
            "t1300" ,
            "t1400" ,
            "t1500" ,
            "t1600" ,
            "t1700" ,
            "t1800" ,
            "t1900" ,
            "t2000" ,
            "t2100" ,
            "t2200" ,
            "t2300"
            };


    public static BigDecimal calcAvg(BaseWeatherStatisticsYearAccuracyDO accYear) {
        List<BigDecimal> accuracyList = new ArrayList<>();
        try {
            for (int i = 1; i < 13; i++) {
                String methodName = "getMonth" + i;
                if (i < 10) {
                    methodName = "getMonth" + 0 + i;
                }
                Method method = StatisticsAccuracyWeatherCityYearHisDO.class.getMethod(methodName,null);
                BigDecimal accuracy = (BigDecimal) method.invoke(accYear,null);
                if (accuracy != null) {
                    accuracyList.add(accuracy);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        if (!CollectionUtils.isEmpty(accuracyList)) {
            return com.tsieframework.core.base.vo.util.BasePeriodUtils.getMaxMinAvg(accuracyList,4).get("avg");
        }
        return null;
    }


    public static BigDecimal calcAvg(BaseWeatherStatisticsMonthAccuracyDO accMonth) {
        List<BigDecimal> accuracyList = new ArrayList<>();
        try {
            for (int i = 1; i < 32; i++) {
                String methodName = "getDay" + i;
                if (i < 10) {
                    methodName = "getDay" + 0 + i;
                }
                Method method = StatisticsAccuracyWeatherCityMonthHisDO.class.getMethod(methodName,null);
                BigDecimal accuracy = (BigDecimal) method.invoke(accMonth,null);
                if (accuracy != null) {
                    accuracyList.add(accuracy);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        if (!CollectionUtils.isEmpty(accuracyList)) {
            return com.tsieframework.core.base.vo.util.BasePeriodUtils.getMaxMinAvg(accuracyList,4).get("avg");
        }
        return null;
    }


    public static List<BigDecimal> listAdd(List<BigDecimal> list1, List<BigDecimal> list2) {
        if (CollectionUtils.isEmpty(list1) && CollectionUtils.isEmpty(list2)) {
            return null;
        } else if (CollectionUtils.isEmpty(list1) && !CollectionUtils.isEmpty(list2)) {
            return list2;
        } else  if (!CollectionUtils.isEmpty(list1) && CollectionUtils.isEmpty(list2)) {
            return list1;
        } else {
            Assert.isTrue(list1.size() == list2.size());
            List<BigDecimal> result = new ArrayList<>();
            for (int i = 0; i < list1.size(); i++) {
                BigDecimal value1 = list1.get(i);
                BigDecimal value2 = list2.get(i);
                BigDecimal addValue = null;
                if (value1 != null && value2 == null) {
                    addValue = value1;
                } else if (value1 == null && value2 != null) {
                    addValue = value2;
                } else if (value1 != null && value2 != null) {
                    addValue = value1.add(value2);
                }
                result.add(addValue);
            }
            return result;
        }
    }

    public static List<BigDecimal> listNotNullAdd(List<BigDecimal> list1, List<BigDecimal> list2) {
        if (CollectionUtils.isEmpty(list1) && CollectionUtils.isEmpty(list2)) {
            return null;
        } else if (CollectionUtils.isEmpty(list1) && !CollectionUtils.isEmpty(list2)) {
            return list2;
        } else  if (!CollectionUtils.isEmpty(list1) && CollectionUtils.isEmpty(list2)) {
            return list1;
        } else {
            Assert.isTrue(list1.size() == list2.size());
            List<BigDecimal> result = new ArrayList<>();
            for (int i = 0; i < list1.size(); i++) {
                BigDecimal value1 = list1.get(i);
                BigDecimal value2 = list2.get(i);
                BigDecimal addValue = null;
                if (value1 == null && value2 != null) {
                    addValue = null;
                } else if (value1 != null && value2 == null) {
                    addValue = null;
                }else if (value1 != null && value2 != null) {
                    addValue = value1.add(value2);
                }
                result.add(addValue);
            }
            return result;
        }
    }

    public static List<BigDecimal> listDivideData(List<BigDecimal> list, BigDecimal data) {
        List<BigDecimal> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(item -> {
                if (item != null) {
                    // 使用 RoundingMode 枚举替代 BigDecimal 常量
                    item = item.divide(data, 4, RoundingMode.HALF_UP);
                    result.add(item);
                } else {
                    result.add(null);
                }
            });
        }
        return result;
    }

    public static List<BigDecimal> listDivide(List<BigDecimal> list1, List<BigDecimal> list2) {
        Assert.isTrue(!CollectionUtils.isEmpty(list1));
        Assert.isTrue(!CollectionUtils.isEmpty(list2));
        Assert.isTrue(list1.size() == list2.size());
        List<BigDecimal> result = new ArrayList<>();
        for (int i = 0; i < list1.size(); i++) {
            BigDecimal value1 = list1.get(i);
            BigDecimal value2 = list2.get(i);
            BigDecimal divideValue = null;
            if (value1 == null || value2 == null) {
                divideValue = null;
            } else {
                divideValue = value1.divide(value2,4,BigDecimal.ROUND_HALF_UP);
            }
            result.add(divideValue);
        }
        return result;
    }

    public static BigDecimal subtract(BigDecimal value1,BigDecimal value2) {
        if (value1 == null && value2 != null) {
            return BigDecimal.ZERO.subtract(value2);
        } else if (value1 != null && value2 == null) {
            return value1;
        } else if (value1 != null && value2 != null) {
            return value1.subtract(value2);
        } else {
            return null;
        }
    }

    public static List<BigDecimal> getCountListWithTwoList(List<BigDecimal> list, List<BigDecimal> list1) {
        List<BigDecimal> decimals = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            decimals.add(null);
            BigDecimal big = list.get(i);
            BigDecimal big1 = list1.get(i);
            if (big != null) {
                BigDecimal count = big.add(big1 == null ? BigDecimal.ZERO : big1);
                decimals.set(i, count);
            }
        }
        return decimals;
    }



    /**
     * 补全缺失的BigDecimal字段值
     * @param data 包含时间字段的对象
     */
    public static void interpolateMissingValues(Base96DO data) throws Exception {
        // 获取所有时间字段名（按顺序）
        List<String> timeFields = generateTimeFieldNames();

        // 遍历所有时间字段
        for (int i = 0; i < timeFields.size(); i++) {
            String currentField = timeFields.get(i);
            BigDecimal currentValue = getFieldValue(data, currentField);

            // 如果当前值为空，进行插值
            if (currentValue == null) {
                // 找到前一个非空值
                BigDecimal prevValue = findPreviousNonNullValue(data, timeFields, i);
                // 找到后一个非空值
                BigDecimal nextValue = findNextNonNullValue(data, timeFields, i);

                // 计算插值并设置字段值
                if (prevValue != null && nextValue != null) {
                    BigDecimal interpolated = calculateInterpolation(prevValue, nextValue);
                    setFieldValue(data, currentField, interpolated);
                }
            }
        }
    }

    /**
     * 生成时间字段名列表（t0000到t2400，每15分钟）
     */
    private static List<String> generateTimeFieldNames() {
        List<String> fields = new ArrayList<>();
        for (int hour = 0; hour <= 24; hour++) {
            for (int minute = 0; minute < 60; minute += 15) {
                String time = String.format("T%02d%02d", hour, minute);
                // 排除超出24小时的情况（如t2400是合法终点）
                if (hour == 24 && minute != 0) break;
                fields.add(time);
            }
        }
        return fields;
    }

    /**
     * 向前查找最近的非空值
     */
    private static BigDecimal findPreviousNonNullValue(Base96DO data, List<String> fields, int currentIndex) {
        for (int i = currentIndex - 1; i >= 0; i--) {
            BigDecimal value = getFieldValue(data, fields.get(i));
            if (value != null) return value;
        }
        return null;
    }

    /**
     * 向后查找最近的非空值
     */
    private static BigDecimal findNextNonNullValue(Base96DO data, List<String> fields, int currentIndex) {
        for (int i = currentIndex + 1; i < fields.size(); i++) {
            BigDecimal value = getFieldValue(data, fields.get(i));
            if (value != null) return value;
        }
        return null;
    }

    /**
     * 线性插值计算（平均值）
     */
    private static BigDecimal calculateInterpolation(BigDecimal prev, BigDecimal next) {
        return prev.add(next)
                .divide(new BigDecimal(2), 4, RoundingMode.HALF_UP);
    }

    /**
     * 反射获取字段值
     */
    private static BigDecimal getFieldValue(Base96DO data, String fieldName) {
        try {
            Method method = data.getClass().getSuperclass().getSuperclass().getMethod("get" + fieldName);
            return (BigDecimal) method.invoke(data);
        } catch (Exception e) {
            throw new RuntimeException("字段不存在: " + fieldName, e);
        }
    }

    /**
     * 反射设置字段值
     */
    private static void setFieldValue(Base96DO data, String fieldName, BigDecimal value) {
        try {
            Method method = data.getClass().getSuperclass().getSuperclass().getMethod("set" + fieldName,BigDecimal.class);
            method.invoke(data,value);
        } catch (Exception e) {
            throw new RuntimeException("字段设置失败: " + fieldName, e);
        }
    }

}

