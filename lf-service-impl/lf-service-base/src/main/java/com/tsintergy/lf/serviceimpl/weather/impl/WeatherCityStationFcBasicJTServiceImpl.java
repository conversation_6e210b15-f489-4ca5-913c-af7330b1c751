package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityStationFcBasicJTService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityStationFcBasicJTDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityStationFcBasicJTDAO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.UUID;

@Service("weatherCityStationFcBasicJTService")
public class WeatherCityStationFcBasicJTServiceImpl implements WeatherCityStationFcBasicJTService {

    @Autowired
    WeatherCityStationFcBasicJTDAO weatherCityStationFcBasicJTDAO;

    @Override
    public void saveOrUpdate(WeatherCityStationFcBasicJTDO weatherCityStationFcBasicJTDO) throws Exception {
        List<WeatherCityStationFcBasicJTDO> weatherCityFcDOS = weatherCityStationFcBasicJTDAO
                .findWeatherCityFcDO(weatherCityStationFcBasicJTDO.getCityId(), weatherCityStationFcBasicJTDO.getType(),
                        weatherCityStationFcBasicJTDO.getDate(), weatherCityStationFcBasicJTDO.getDate());
        if (CollectionUtils.isNotEmpty(weatherCityFcDOS)){
            WeatherCityStationFcBasicJTDO cityFcClctGDTDO = weatherCityFcDOS.get(0);
            weatherCityStationFcBasicJTDO.setId(cityFcClctGDTDO.getId());
        }else {
            weatherCityStationFcBasicJTDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-",""));
        }
        weatherCityStationFcBasicJTDAO.saveOrUpdateEntityByTemplate(weatherCityStationFcBasicJTDO);
    }

    @Override
    public List<WeatherCityStationFcBasicJTDO> findFcWeatherData(String cityId, Integer type, Date startDate, Date endDate) throws Exception {
        return weatherCityStationFcBasicJTDAO.findWeatherCityFcDO(cityId,type,startDate,endDate);
    }
}
