/**
 * Copyright(C),2015‐2020,北京清能互联科技有限公司
 * Author:yangjin
 * Date:2020/5/1218:30
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.weather.dao;



import cn.hutool.core.collection.CollectionUtil;
import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationFcClctGDTDO;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;

/**
 *Description: <br>
 *
 *<AUTHOR>
 *@create 2020/7/21
 *@since 1.0.0
 */
@Component
public class WeatherStationFcClctGDTDAO extends BaseAbstractDAO<WeatherStationFcClctGDTDO> {



    public List<WeatherStationFcClctGDTDO> findWeatherCityFcDO(Integer type, Date startDate, Date endDate,String stationId) throws Exception {

        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if(null != startDate){
            param.getQueryConditions().put("_dnl_date",startDate);
        }
        if (null != endDate){
            param.getQueryConditions().put("_dnm_date",endDate);
        }
        if (null != type){
            param.getQueryConditions().put("_ne_type", type);
        }

        if (null != stationId) {
            param.getQueryConditions().put("_ne_stationId", stationId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<WeatherStationFcClctGDTDO> weatherCityFcVOs = this.query(param).getDatas();
        return weatherCityFcVOs;
    }

    public List<WeatherStationFcClctGDTDO> findWeatherCityFcDOS(Integer type, Date startDate, Date endDate,List<String> stationIds) throws Exception {

        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if(null != startDate){
            param.getQueryConditions().put("_dnl_date",startDate);
        }
        if (null != endDate){
            param.getQueryConditions().put("_dnm_date",endDate);
        }
        if (null != type){
            param.getQueryConditions().put("_ne_type", type);
        }

        if (CollectionUtil.isNotEmpty(stationIds)) {
            param.getQueryConditions().put("_sin_stationId", stationIds);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<WeatherStationFcClctGDTDO> weatherCityFcVOs = this.query(param).getDatas();
        return weatherCityFcVOs;
    }
}