/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.weather.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsintergy.lf.serviceapi.base.weather.pojo.BaseWeatherStatisticsMonthAccuracyDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.StatisticsAccuracyWeatherCityMonthHisGDTDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.StatisticsAccuracyWeatherCityMonthHisZHTDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.StatisticsAccuracyWeatherCityMonthHisZYTDO;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/4/12 10:33
 * @Version: 1.0.0
 */
@Component
public class
StatisticsAccuracyWeatherCityMonthHisZHTDAO extends BaseAbstractDAO<StatisticsAccuracyWeatherCityMonthHisZHTDO> {


    /**
     * 查询预测气象准确率  ---月
     *
     * @param cityId 城市ID
     * @param weatherType 气象类型
     * @param startDate 开始日期
     * @return endDate 结束日期
     */
    public <T extends BaseWeatherStatisticsMonthAccuracyDO> List<T> find(String cityId, Integer weatherType,
        String startDate,
        String endDate) throws Exception {
        //sql只做年份筛选   月份筛选为逻辑处理
        String startYear = startDate.substring(0, 4);
        String endYear = endDate.substring(0, 4);
        DBQueryParamBuilder param = DBQueryParamBuilder.create().queryDataOnly();
        if (cityId != null && StringUtils.isNoneEmpty(cityId) && !"0".equals(cityId)) {
            param.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (weatherType != null && weatherType != 0) {
            param.where(QueryOp.NumberEqualTo, "type", weatherType);
        }
        if (startDate != null && endDate != null) {
            param.where(QueryOp.StringNoLessThan, "year", startYear);
            param.where(QueryOp.StringNoMoreThan, "year", endYear);
        }
        param.addOrderByAsc("year");
        param.addOrderByAsc("month");
        List<StatisticsAccuracyWeatherCityMonthHisZHTDO> list = this.query(param.build()).getDatas();
        return (List<T>) list;
    }

}