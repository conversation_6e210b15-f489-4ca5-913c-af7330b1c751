package com.tsintergy.lf.serviceimpl.weather.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.tsintergy.lf.core.constants.ColumnUtil;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.DateUtil;
import com.tsintergy.lf.core.constants.WeatherTypeEnum;
import com.tsintergy.lf.serviceapi.base.base.api.BaseWeatherCityStationRelationService;
import com.tsintergy.lf.serviceapi.base.base.pojo.BaseWeatherCityStationRelationDO;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.tsintergy.lf.serviceapi.base.weather.pojo.*;
import com.tsintergy.lf.serviceimpl.common.util.BasePeriodUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("weatherStationCityAveDayService")
public class WeatherStationCityAveDayServiceImpl implements WeatherStationCityAveDayService {

    @Autowired
    WeatherStationFcClctGDTService weatherStationFcClctGDTService;

    @Autowired
    WeatherStationFcClctZHService weatherStationFcClctZHService;

    @Autowired
    WeatherStationFcClctZYTService weatherStationFcClctZYTService;

    @Autowired
    WeatherStationFcClctJTService weatherStationFcClctJTService;

    @Autowired
    WeatherCityStationFcBasicGDTService weatherCityStationFcBasicGDTService;

    @Autowired
    WeatherCityStationFcBasicZHService weatherCityStationFcBasicZHService;

    @Autowired
    WeatherCityStationFcBasicZYTService weatherCityStationFcBasicZYTService;

    @Autowired
    WeatherCityStationFcBasicJTService weatherCityStationFcBasicJTService;

    @Autowired
    BaseWeatherCityStationRelationService baseWeatherCityStationRelationService;

    @Autowired
    WeatherStationHisClctService weatherStationHisClctService;

    @Autowired
    WeatherCityStationHisBasicService weatherCityStationHisBasicService;

    @Override
    public void calculateAveFcDay(String cityId, Date startDate, Date endDate) throws Exception {
        List<BaseWeatherCityStationRelationDO> baseData = baseWeatherCityStationRelationService.findAll().stream().filter(t -> Constants.GUANGZHOU_ID.equals(t.getCityId())).collect(Collectors.toList());
        if (!CollectionUtil.isEmpty(baseData)) {
            List<String> stationIds = Arrays.asList(baseData.get(0).getStationId().split(","));
            // GDT
            List<WeatherStationFcClctGDTDO> gdtData = weatherStationFcClctGDTService.findByDateAndstationIds(startDate,
                    endDate, stationIds, null);
            Map<String, List<WeatherStationFcClctGDTDO>> collectGdt = new HashMap<>();
            if (!CollectionUtil.isEmpty(gdtData)) {
                collectGdt = gdtData.stream().collect(Collectors.groupingBy(t -> DateUtil.getDateToStrFORMAT(t.getDate(),
                        "yyyy-MM-dd") + "-" + t.getType()));
            }
            // ZH
            List<WeatherStationFcClctZHDO> zhData = weatherStationFcClctZHService.findByDateAndstationIds(startDate,
                    endDate, stationIds, null);
            Map<String, List<WeatherStationFcClctZHDO>> collectZh = new HashMap<>();
            if (!CollectionUtil.isEmpty(zhData)) {
                collectZh = zhData.stream().collect(Collectors.groupingBy(t -> DateUtil.getDateToStrFORMAT(t.getDate(),
                        "yyyy-MM-dd") + "-" + t.getType()));
            }
            // ZYT
            List<WeatherStationFcClctZYTDO> zytData = weatherStationFcClctZYTService.findByDateAndstationIds(startDate,
                    endDate, stationIds, null);
            Map<String, List<WeatherStationFcClctZYTDO>> collectZyt = new HashMap<>();
            if (!CollectionUtil.isEmpty(zytData)) {
                collectZyt = zytData.stream().collect(Collectors.groupingBy(t -> DateUtil.getDateToStrFORMAT(t.getDate(),
                        "yyyy-MM-dd") + "-" + t.getType()));
            }
            // JT
            List<WeatherStationFcClctJTDO> jtData = weatherStationFcClctJTService.findByDateAndstationIds(startDate,
                    endDate, stationIds, null);
            Map<String, List<WeatherStationFcClctJTDO>> collectJt = new HashMap<>();
            if (!CollectionUtil.isEmpty(jtData)) {
                collectJt = jtData.stream().collect(Collectors.groupingBy(t -> DateUtil.getDateToStrFORMAT(t.getDate(),
                        "yyyy-MM-dd") + "-" + t.getType()));
            }
            // 计算
            Map<String, List<WeatherStationFcClctGDTDO>> finalCollectGdt = collectGdt;
            DateUtil.getListBetweenDay(startDate, endDate).forEach(t -> {
                for (WeatherTypeEnum value : WeatherTypeEnum.values()) {
                    String key = DateUtil.getDateToStrFORMAT(t, "yyyy-MM-dd") + "-" + value.getType();
                    List<WeatherStationFcClctGDTDO> weatherGDTDOS = finalCollectGdt.get(key);
                    if (CollectionUtil.isNotEmpty(weatherGDTDOS)) {
                        List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, null);
                        for (WeatherStationFcClctGDTDO weatherGDTDO : weatherGDTDOS) {
                            List<BigDecimal> weatherList = weatherGDTDO.getWeatherList();
                            zeroList = BasePeriodUtils.listAdd(zeroList, weatherList);
                        }
                        List<BigDecimal> bigDecimals = BasePeriodUtils.listDivideData(zeroList, BigDecimal.valueOf(weatherGDTDOS.size()));
                        WeatherCityStationFcBasicGDTDO weatherGDTDO = new WeatherCityStationFcBasicGDTDO();
                        weatherGDTDO.setCityId(cityId);
                        weatherGDTDO.setType(value.getType());
                        weatherGDTDO.setDate(new java.sql.Date(t.getTime()));
                        Map<String, BigDecimal> decimalMap = null;
                        try {
                            decimalMap = ColumnUtil.listToMap(bigDecimals, Constants.LOAD_CURVE_START_WITH_ZERO);
                            com.tsieframework.core.base.vo.util.BasePeriodUtils.setAllFiled(weatherGDTDO, decimalMap);
                            weatherCityStationFcBasicGDTService.saveOrUpdate(weatherGDTDO);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                }
            });
            Map<String, List<WeatherStationFcClctZHDO>> finalCollectZh = collectZh;
            DateUtil.getListBetweenDay(startDate, endDate).forEach(t -> {
                for (WeatherTypeEnum value : WeatherTypeEnum.values()) {
                    String key = DateUtil.getDateToStrFORMAT(t, "yyyy-MM-dd") + "-" + value.getType();
                    List<WeatherStationFcClctZHDO> weatherGDTDOS = finalCollectZh.get(key);
                    if (CollectionUtil.isNotEmpty(weatherGDTDOS)) {
                        List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, null);
                        for (WeatherStationFcClctZHDO weatherGDTDO : weatherGDTDOS) {
                            List<BigDecimal> weatherList = weatherGDTDO.getWeatherList();
                            zeroList = BasePeriodUtils.listAdd(zeroList, weatherList);
                        }
                        List<BigDecimal> bigDecimals = BasePeriodUtils.listDivideData(zeroList, BigDecimal.valueOf(weatherGDTDOS.size()));
                        WeatherCityStationFcBasicZHDO weatherGDTDO = new WeatherCityStationFcBasicZHDO();
                        weatherGDTDO.setCityId(cityId);
                        weatherGDTDO.setType(value.getType());
                        weatherGDTDO.setDate(new java.sql.Date(t.getTime()));
                        Map<String, BigDecimal> decimalMap = null;
                        try {
                            decimalMap = ColumnUtil.listToMap(bigDecimals, Constants.LOAD_CURVE_START_WITH_ZERO);
                            com.tsieframework.core.base.vo.util.BasePeriodUtils.setAllFiled(weatherGDTDO, decimalMap);
                            weatherCityStationFcBasicZHService.saveOrUpdate(weatherGDTDO);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                }
            });
            Map<String, List<WeatherStationFcClctZYTDO>> finalCollectZyt = collectZyt;
            DateUtil.getListBetweenDay(startDate, endDate).forEach(t -> {
                for (WeatherTypeEnum value : WeatherTypeEnum.values()) {
                    String key = DateUtil.getDateToStrFORMAT(t, "yyyy-MM-dd") + "-" + value.getType();
                    List<WeatherStationFcClctZYTDO> weatherGDTDOS = finalCollectZyt.get(key);
                    if (CollectionUtil.isNotEmpty(weatherGDTDOS)) {
                        List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, null);
                        for (WeatherStationFcClctZYTDO weatherGDTDO : weatherGDTDOS) {
                            List<BigDecimal> weatherList = weatherGDTDO.getWeatherList();
                            zeroList = BasePeriodUtils.listAdd(zeroList, weatherList);
                        }
                        List<BigDecimal> bigDecimals = BasePeriodUtils.listDivideData(zeroList, BigDecimal.valueOf(weatherGDTDOS.size()));
                        WeatherCityStationFcBasicZYTDO weatherGDTDO = new WeatherCityStationFcBasicZYTDO();
                        weatherGDTDO.setCityId(cityId);
                        weatherGDTDO.setType(value.getType());
                        weatherGDTDO.setDate(new java.sql.Date(t.getTime()));
                        Map<String, BigDecimal> decimalMap = null;
                        try {
                            decimalMap = ColumnUtil.listToMap(bigDecimals, Constants.LOAD_CURVE_START_WITH_ZERO);
                            com.tsieframework.core.base.vo.util.BasePeriodUtils.setAllFiled(weatherGDTDO, decimalMap);
                            weatherCityStationFcBasicZYTService.saveOrUpdate(weatherGDTDO);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                }
            });
            Map<String, List<WeatherStationFcClctJTDO>> finalCollectJt = collectJt;
            DateUtil.getListBetweenDay(startDate, endDate).forEach(t -> {
                for (WeatherTypeEnum value : WeatherTypeEnum.values()) {
                    String key = DateUtil.getDateToStrFORMAT(t, "yyyy-MM-dd") + "-" + value.getType();
                    List<WeatherStationFcClctJTDO> weatherGDTDOS = finalCollectJt.get(key);
                    if (CollectionUtil.isNotEmpty(weatherGDTDOS)) {
                        List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, null);
                        for (WeatherStationFcClctJTDO weatherGDTDO : weatherGDTDOS) {
                            List<BigDecimal> weatherList = weatherGDTDO.getWeatherList();
                            zeroList = BasePeriodUtils.listAdd(zeroList, weatherList);
                        }
                        List<BigDecimal> bigDecimals = BasePeriodUtils.listDivideData(zeroList, BigDecimal.valueOf(weatherGDTDOS.size()));
                        WeatherCityStationFcBasicJTDO weatherGDTDO = new WeatherCityStationFcBasicJTDO();
                        weatherGDTDO.setCityId(cityId);
                        weatherGDTDO.setType(value.getType());
                        weatherGDTDO.setDate(new java.sql.Date(t.getTime()));
                        Map<String, BigDecimal> decimalMap = null;
                        try {
                            decimalMap = ColumnUtil.listToMap(bigDecimals, Constants.LOAD_CURVE_START_WITH_ZERO);
                            com.tsieframework.core.base.vo.util.BasePeriodUtils.setAllFiled(weatherGDTDO, decimalMap);
                            weatherCityStationFcBasicJTService.saveOrUpdate(weatherGDTDO);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                }
            });
        }
    }

    @Override
    public void calculateAveHisDay(String cityId, Date startDate, Date endDate) throws Exception {
        List<BaseWeatherCityStationRelationDO> baseData = baseWeatherCityStationRelationService.findAll().stream().filter(t -> Constants.GUANGZHOU_ID.equals(t.getCityId())).collect(Collectors.toList());
        if (!CollectionUtil.isEmpty(baseData)) {
            List<String> stationIds = Arrays.asList(baseData.get(0).getStationId().split(","));
            List<WeatherStationHisClctDO> weatherStationHisClctDOLists = weatherStationHisClctService.getWeatherStationHisClctDOLists(startDate,
                    endDate, stationIds, null);
            Map<String, List<WeatherStationHisClctDO>> collectHis = new HashMap<>();
            if (!CollectionUtil.isEmpty(weatherStationHisClctDOLists)) {
                collectHis = weatherStationHisClctDOLists.stream().collect(Collectors.groupingBy(t -> DateUtil.getDateToStrFORMAT(t.getDate(),
                        "yyyy-MM-dd") + "-" + t.getType()));
            }
            Map<String, List<WeatherStationHisClctDO>> finalCollectHis = collectHis;
            DateUtil.getListBetweenDay(startDate, endDate).forEach(t -> {
                for (WeatherTypeEnum value : WeatherTypeEnum.values()) {
                    String key = DateUtil.getDateToStrFORMAT(t, "yyyy-MM-dd") + "-" + value.getType();
                    List<WeatherStationHisClctDO> weatherGDTDOS = finalCollectHis.get(key);
                    if (CollectionUtil.isNotEmpty(weatherGDTDOS)) {
                        List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                        for (WeatherStationHisClctDO weatherGDTDO : weatherGDTDOS) {
                            List<BigDecimal> weatherList = weatherGDTDO.getWeatherList();
                            zeroList = BasePeriodUtils.listAdd(zeroList, weatherList);
                        }
                        List<BigDecimal> bigDecimals = BasePeriodUtils.listDivideData(zeroList, BigDecimal.valueOf(weatherGDTDOS.size()));
                        WeatherCityStationHisBasicDO weatherGDTDO = new WeatherCityStationHisBasicDO();
                        weatherGDTDO.setCityId(cityId);
                        weatherGDTDO.setType(value.getType());
                        weatherGDTDO.setDate(new java.sql.Date(t.getTime()));
                        Map<String, BigDecimal> decimalMap = null;
                        try {
                            decimalMap = ColumnUtil.listToMap(bigDecimals, Constants.LOAD_CURVE_START_WITH_ZERO);
                            com.tsieframework.core.base.vo.util.BasePeriodUtils.setAllFiled(weatherGDTDO, decimalMap);
                            weatherCityStationHisBasicService.saveOrUpdate(weatherGDTDO);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                }
            });
        }
    }
}
