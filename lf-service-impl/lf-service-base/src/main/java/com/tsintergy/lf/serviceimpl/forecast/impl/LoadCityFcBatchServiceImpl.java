package com.tsintergy.lf.serviceimpl.forecast.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.lf.core.constants.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcBatchDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingBatchInitService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingBatchInitDO;
import com.tsintergy.lf.serviceimpl.forecast.dao.LoadCityFcBatchDAO;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @description
 * @date 2021/2/3 14:30
 */
@Service
public class LoadCityFcBatchServiceImpl implements LoadCityFcBatchService {

    @Autowired
    private LoadCityFcBatchDAO loadCityFcBatchDAO;

    @Autowired
    private SettingBatchInitService settingBatchInitService;

    @Override
    public void save(LoadCityFcDO loadCityFcDO) {
        LoadCityFcBatchDO loadCityFcBatchDO = new LoadCityFcBatchDO();
        BeanUtils.copyProperties(loadCityFcDO, loadCityFcBatchDO, "id");
        //此处的创建时间和修改时间都是 load_city_fc_basic的时间, 所以updateTime才是此批次的预测时间
        loadCityFcBatchDO.setCreatetime(loadCityFcBatchDO.getUpdatetime() == null ? loadCityFcBatchDO.getCreatetime() : loadCityFcBatchDO.getUpdatetime());
        //防止出现相同批次号
        synchronized (this) {
            List<LoadCityFcBatchDO> loadCityFcBatchDOS = loadCityFcBatchDAO.selectList(new LambdaQueryWrapper<LoadCityFcBatchDO>()
                    .eq(LoadCityFcBatchDO::getDate, loadCityFcBatchDO.getDate())
                    .eq(LoadCityFcBatchDO::getCityId, loadCityFcBatchDO.getCityId())
                    .eq(LoadCityFcBatchDO::getCaliberId, loadCityFcBatchDO.getCaliberId())
                    .eq(LoadCityFcBatchDO::getAlgorithmId, loadCityFcBatchDO.getAlgorithmId()));
            if (CollectionUtils.isEmpty(loadCityFcBatchDOS)) {
                loadCityFcBatchDO.setBatchId(1);
            } else {
                Integer batchId = loadCityFcBatchDOS.stream().max(Comparator.comparing(LoadCityFcBatchDO::getBatchId)).get().getBatchId();
                loadCityFcBatchDO.setBatchId(++batchId);
            }
            loadCityFcBatchDAO.insert(loadCityFcBatchDO);
        }
    }

    @Override
    public List<LoadCityFcBatchDO> selectByCondition(String cityId, Date date, String caliberId, String algorithmId) {
        List<LoadCityFcBatchDO> result = new ArrayList<>();
        if (AlgorithmEnum.RESULT_REPORT.getId().equals(algorithmId)) {
            //最终上报包括自动上报以及人工手动上报结果
            List<LoadCityFcBatchDO> autoReportList = loadCityFcBatchDAO
                .selectList(new LambdaQueryWrapper<LoadCityFcBatchDO>()
                    .eq(LoadCityFcBatchDO::getDate, date)
                    .eq(LoadCityFcBatchDO::getCityId, cityId)
                    .eq(LoadCityFcBatchDO::getCaliberId, caliberId)
                    .ne(LoadCityFcBatchDO::getAlgorithmId, AlgorithmEnum.FORECAST_MODIFY.getId())
                    .eq(LoadCityFcBatchDO::getReport, true));
            if (!CollectionUtils.isEmpty(autoReportList)) {
                result.addAll(autoReportList);
            }
            List<LoadCityFcBatchDO> modifyReportList = loadCityFcBatchDAO
                .selectList(new LambdaQueryWrapper<LoadCityFcBatchDO>()
                    .eq(LoadCityFcBatchDO::getDate, date)
                    .eq(LoadCityFcBatchDO::getCityId, cityId)
                    .eq(LoadCityFcBatchDO::getCaliberId, caliberId)
                    .eq(LoadCityFcBatchDO::getAlgorithmId, AlgorithmEnum.FORECAST_MODIFY.getId()));
            if (!CollectionUtils.isEmpty(modifyReportList)) {
                result.addAll(modifyReportList);
            }
            if (!CollectionUtils.isEmpty(result)) {
                AtomicInteger num = new AtomicInteger(1);
                result.forEach(loadCityFcBatchDO -> loadCityFcBatchDO.setBatchId(num.getAndIncrement()));
            }

        } else {
            result = loadCityFcBatchDAO.selectList(new LambdaQueryWrapper<LoadCityFcBatchDO>()
                .eq(LoadCityFcBatchDO::getDate, new java.sql.Date(date.getTime()))
                .eq(LoadCityFcBatchDO::getCityId, cityId)
                .eq(LoadCityFcBatchDO::getCaliberId, caliberId)
                .eq(LoadCityFcBatchDO::getAlgorithmId, algorithmId));
        }
        return result;
    }

    public List<LoadCityFcBatchDO> findBatchFc(String cityId, String caliberId, Date startDate,Date endDate,String algorithmId){
        return loadCityFcBatchDAO.selectList(new LambdaQueryWrapper<LoadCityFcBatchDO>()
                .ge(Objects.nonNull(startDate),LoadCityFcBatchDO::getDate, new java.sql.Date(startDate.getTime()))
                .le(Objects.nonNull(endDate),LoadCityFcBatchDO::getDate, new java.sql.Date(endDate.getTime()))
                .eq(Objects.nonNull(cityId),LoadCityFcBatchDO::getCityId, cityId)
                .eq(Objects.nonNull(caliberId),LoadCityFcBatchDO::getCaliberId, caliberId)
                .eq(Objects.nonNull(algorithmId),LoadCityFcBatchDO::getAlgorithmId, algorithmId));

    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public LoadCityFcBatchDO findOneByConditionByBatchId(String cityId, Date date, String caliberId,
        String algorithmId, String batchId, Integer day) throws Exception {
        List<LoadCityFcBatchDO> byConditionByBatchId = loadCityFcBatchDAO.selectList(new LambdaQueryWrapper<LoadCityFcBatchDO>()
            .eq(Objects.nonNull(date),LoadCityFcBatchDO::getDate, new java.sql.Date(date.getTime()))
            .eq(Objects.nonNull(cityId),LoadCityFcBatchDO::getCityId, cityId)
            .eq(Objects.nonNull(caliberId),LoadCityFcBatchDO::getCaliberId, caliberId)
            .eq(Objects.nonNull(algorithmId),LoadCityFcBatchDO::getAlgorithmId, algorithmId));

        if (CollectionUtil.isEmpty(byConditionByBatchId)){
            return null;
        }
        List<LoadCityFcBatchDO> result = new ArrayList<>();
        SettingBatchInitDO batch = settingBatchInitService.getBatchById(batchId);
        for (LoadCityFcBatchDO loadCityFcBatchDO : byConditionByBatchId){
            if (DateUtil.isWithinTimeRange(new Timestamp(loadCityFcBatchDO.getCreatetime().getTime()),
                batch.getStartTime(), batch.getEndTime())){
                result.add(loadCityFcBatchDO);
            }
        }
        if (CollectionUtil.isEmpty(result)){
            return null;
        }
        List<LoadCityFcBatchDO> resultFilterByDay = result.stream().filter(src -> {
            Date anObject = DateUtils.addDays(src.getDate(), (-day));
            return DateUtil.getDateToStr(new Date(src.getCreatetime().getTime()))
                .equals(DateUtil.getDateToStr(anObject));
        }).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(resultFilterByDay)){
            return null;
        }
        Optional<LoadCityFcBatchDO> resultDO = resultFilterByDay.stream().sorted(Comparator.comparing(LoadCityFcBatchDO::getCreatetime).reversed()).findFirst();
        if (resultDO.isPresent()){
            return resultDO.get();
        }
        return null;
    }
}
