package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsintergy.lf.serviceapi.base.weather.api.WeatherStationHisClctService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationHisClctDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherStationHisClctDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Date;
import java.util.List;

@Service("weatherStationHisClctService")
public class WeatherStationHisClctServiceImpl implements WeatherStationHisClctService {

    @Autowired
    WeatherStationHisClctDAO weatherStationHisClctDAO;

    @Override
    public List<WeatherStationHisClctDO> getWeatherStationHisClctDOLists(Date startDate, Date endDate, List<String> stationIds, Integer type) throws Exception {
        List<WeatherStationHisClctDO> weatherCityFcDOs = weatherStationHisClctDAO.findWeatherCityFcDOs(type, startDate, endDate, stationIds);
        return weatherCityFcDOs;
    }

}
