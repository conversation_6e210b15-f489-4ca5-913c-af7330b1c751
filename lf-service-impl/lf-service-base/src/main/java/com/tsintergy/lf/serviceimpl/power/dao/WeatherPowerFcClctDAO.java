/**
 * Copyright(C),2015‐2023,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.power.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsintergy.lf.serviceapi.base.power.pojo.WeatherPowerFcClctDO;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2023/2/6 20:00
 * @Version: 1.0.0
 */
@Component
public class WeatherPowerFcClctDAO extends BaseAbstractDAO<WeatherPowerFcClctDO>{

    public List<WeatherPowerFcClctDO> findPowerWeatherCityFcDO(String countryId, Integer type, Date startDate, Date endDate) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if(null != startDate){
            param.getQueryConditions().put("_dnl_date",new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate){
            param.getQueryConditions().put("_dnm_date",new java.sql.Date(endDate.getTime()));
        }
        if (null != type){
            param.getQueryConditions().put("_ne_type", type);
        }
        if (null != countryId) {
            param.getQueryConditions().put("_ne_countryId", countryId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<WeatherPowerFcClctDO> weatherCityFcVOs = this.query(param).getDatas();
        return weatherCityFcVOs;
    }


    public List<WeatherPowerFcClctDO> findPowerFcWeather(List<String> cityIds, Integer type, Date startDate, Date endDate) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        cityIds = cityIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if(null != startDate){
            param.getQueryConditions().put("_dnl_date",new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate){
            param.getQueryConditions().put("_dnm_date",new java.sql.Date(endDate.getTime()));
        }
        if (null != type){
            param.getQueryConditions().put("_ne_type", type);
        }
        if (!CollectionUtils.isEmpty(cityIds)) {
            param.getQueryConditions().put("_sin_cityId", cityIds);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<WeatherPowerFcClctDO> weatherCityFcVOs = this.query(param).getDatas();
        return weatherCityFcVOs;
    }

    public List<WeatherPowerFcClctDO> findPowerFcWeatherByCountyIds(List<String> countyIds, Integer type, Date startDate, Date endDate) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if(null != startDate){
            param.getQueryConditions().put("_dnl_date",new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate){
            param.getQueryConditions().put("_dnm_date",new java.sql.Date(endDate.getTime()));
        }
        if (null != type){
            param.getQueryConditions().put("_ne_type", type);
        }
        if (!CollectionUtils.isEmpty(countyIds)) {
            param.getQueryConditions().put("_sin_countryId", countyIds);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<WeatherPowerFcClctDO> weatherCityFcVOs = this.query(param).getDatas();
        return weatherCityFcVOs;
    }
}