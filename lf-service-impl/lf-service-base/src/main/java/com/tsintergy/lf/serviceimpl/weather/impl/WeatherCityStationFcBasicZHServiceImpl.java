package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityStationFcBasicZHService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityStationFcBasicZHDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityStationFcBasicZHDAO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.UUID;

@Service("weatherCityStationFcBasicZHService")
public class WeatherCityStationFcBasicZHServiceImpl implements WeatherCityStationFcBasicZHService {

    @Autowired
    WeatherCityStationFcBasicZHDAO WeatherCityStationFcBasicZHDAO;

    @Override
    public void saveOrUpdate(WeatherCityStationFcBasicZHDO weatherCityStationFcBasicZHDO) throws Exception {
        List<WeatherCityStationFcBasicZHDO> weatherCityFcDOS = WeatherCityStationFcBasicZHDAO
                .findWeatherCityFcDO(weatherCityStationFcBasicZHDO.getCityId(), weatherCityStationFcBasicZHDO.getType(),
                        weatherCityStationFcBasicZHDO.getDate(), weatherCityStationFcBasicZHDO.getDate());
        if (CollectionUtils.isNotEmpty(weatherCityFcDOS)){
            WeatherCityStationFcBasicZHDO cityFcClctGDTDO = weatherCityFcDOS.get(0);
            weatherCityStationFcBasicZHDO.setId(cityFcClctGDTDO.getId());
        }else {
            weatherCityStationFcBasicZHDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-",""));
        }
        WeatherCityStationFcBasicZHDAO.saveOrUpdateEntityByTemplate(weatherCityStationFcBasicZHDO);
    }

    @Override
    public List<WeatherCityStationFcBasicZHDO> findFcWeatherData(String cityId, Integer type, Date startDate, Date endDate) throws Exception {
        return WeatherCityStationFcBasicZHDAO.findWeatherCityFcDO(cityId,type,startDate,endDate);
    }
}
