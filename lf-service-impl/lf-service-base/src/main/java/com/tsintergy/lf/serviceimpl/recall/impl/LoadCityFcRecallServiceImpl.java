package com.tsintergy.lf.serviceimpl.recall.impl;

import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.evalucation.api.AccuracyLoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcBatchDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.recall.api.LoadCityFcRecallService;
import com.tsintergy.lf.serviceapi.base.recall.dto.LoadFcQueryDTO;
import com.tsintergy.lf.serviceapi.base.recall.pojo.LoadCityFcRecallDO;
import com.tsintergy.lf.serviceimpl.recall.dao.LoadCityFcRecallDAO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2025-05-07
 * @since 1.0.0
 */
@Service("loadCityFcRecallService")
public class LoadCityFcRecallServiceImpl extends BaseServiceImpl implements LoadCityFcRecallService {


    @Autowired
    LoadCityHisService loadCityHisService;

    @Autowired
    LoadCityFcService loadCityFcService;

    @Autowired
    LoadCityFcRecallService loadCityFcRecallService;

    @Autowired
    AccuracyLoadCityFcService accuracyLoadCityFcService;

    @Autowired
    LoadCityFcRecallDAO loadCityFcRecallDAO;

    @Autowired
    LoadCityFcBatchService loadCityFcBatchService;

    @Override
    public LoadFcQueryDTO getLoadBatchFcQueryDTO(String cityId, String caliberId, Date date, String algorithmId
        , String batchId, Integer day) {
        try {
            LoadCityFcBatchDO loadCityFcBatchDO = loadCityFcBatchService.findOneByConditionByBatchId(cityId, date, caliberId
                , algorithmId, batchId, day);
            List<BigDecimal> loadCityFcBatchDOList = new ArrayList<>();
            if (loadCityFcBatchDO != null) {
                loadCityFcBatchDOList = BasePeriodUtils
                    .toList(loadCityFcBatchDO, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
            }
            LoadCityHisDO loadCityHisDO = loadCityHisService.getLoadCityHisDO(cityId, caliberId, date);
            List<BigDecimal> loadCityHisDOList = new ArrayList<>();
            if (loadCityHisDO != null) {
                loadCityHisDOList = BasePeriodUtils
                    .toList(loadCityHisDO, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
            }
            LoadCityFcRecallDO loadCityFcRecallDO = loadCityFcRecallService.getLoadCityFcRecallDO(cityId, caliberId, date, algorithmId);
            List<BigDecimal> loadCityFcRecallDOList = new ArrayList<>();
            if (loadCityFcRecallDO != null) {
                loadCityFcRecallDOList = BasePeriodUtils
                    .toList(loadCityFcRecallDO, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
            }
            LoadFcQueryDTO dto = new LoadFcQueryDTO();
            if (loadCityFcRecallDOList != null) {
                dto.setRecallFc(loadCityFcRecallDOList);
            }
            if (loadCityHisDOList != null) {
                dto.setLoadHis(loadCityHisDOList);
            }
            if (loadCityFcBatchDOList != null) {
                dto.setAlgorithmFc(loadCityFcBatchDOList);
            }
            return dto;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public LoadCityFcRecallDO getLoadCityFcRecallDO(String cityId, String caliberId, Date date, String algorithmId) throws Exception {
        return loadCityFcRecallDAO.getLoadCityFcDO(cityId,caliberId,algorithmId,date);
    }

    @Override
    public List<LoadCityFcRecallDO> getLoadAllData(String cityId, String caliberId, Date startDate, Date endDate,
        String algorithmId) throws Exception {
        return loadCityFcRecallDAO.getLoadCityFcDOs(cityId,caliberId,algorithmId,startDate,endDate);
    }

    @Override
    public LoadCityFcRecallDO doSaveOrUpdateLoadCityFcDO96(LoadCityFcRecallDO loadCityFcRecallDO) throws Exception {
        return loadCityFcRecallDAO.doSaveOrUpdateLoadCityFcDO(loadCityFcRecallDO);
    }
}
