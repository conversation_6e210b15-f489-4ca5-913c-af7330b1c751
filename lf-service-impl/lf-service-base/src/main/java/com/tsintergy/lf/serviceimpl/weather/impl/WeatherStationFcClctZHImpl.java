/**
 * Copyright(C),2015‐2020,北京清能互联科技有限公司
 * Author:yangjin
 * Date:2020/5/1218:33
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.weather.impl;


import com.tsintergy.lf.serviceapi.base.weather.api.WeatherStationFcClctZHService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationFcClctZHDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherStationFcClctZHDAO;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *Description: <br>
 *
 *<AUTHOR>
 *@create 2020/5/12
 *@since 1.0.0
 */
@Service
public class WeatherStationFcClctZHImpl implements WeatherStationFcClctZHService {

    @Autowired
    WeatherStationFcClctZHDAO weatherStationFcClctZHDAO;

    @Override
    public List<WeatherStationFcClctZHDO> findByDateAndstationId(Date startDate, Date endDate, String stationId,
        Integer type) throws Exception {
        List<WeatherStationFcClctZHDO> weatherCityFcDO = weatherStationFcClctZHDAO
            .findWeatherCityFcDO(type, startDate, endDate, stationId);
        return weatherCityFcDO;
    }

    @Override
    public List<WeatherStationFcClctZHDO> findByDateAndstationIds(Date startDate, Date endDate, List<String> stationIds, Integer type) throws Exception {
        List<WeatherStationFcClctZHDO> weatherCityFcDO = weatherStationFcClctZHDAO
                .findWeatherCityFcDOs(type, startDate, endDate, stationIds);
        return weatherCityFcDO;
    }
}