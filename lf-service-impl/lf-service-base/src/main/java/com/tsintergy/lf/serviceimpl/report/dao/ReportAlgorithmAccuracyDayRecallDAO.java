package com.tsintergy.lf.serviceimpl.report.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAlgorithmAccuracyRecallDayDO;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2025-05-07
 * @since 1.0.0
 */
@Component
public class ReportAlgorithmAccuracyDayRecallDAO extends BaseAbstractDAO<ReportAlgorithmAccuracyRecallDayDO> {


    @Autowired
    ReportAlgorithmAccuracyDayRecallDAO reportAccuracyDayDAO;


    public List<ReportAlgorithmAccuracyRecallDayDO> findReportAccuracies(String cityId, String caliberId, String algorithmId, Date startDate, Date endDate, Boolean isReport) throws Exception {
        DBQueryParamBuilder builder = DBQueryParamBuilder.create().queryDataOnly();
        if (StringUtils.isNotBlank(cityId) ) {
            builder.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (StringUtils.isNotBlank(caliberId) ) {
            builder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (endDate != null) {
            builder.where(QueryOp.DateNoMoreThan, "date", new java.sql.Date(endDate.getTime()));
        }
        if (startDate != null) {
            builder.where(QueryOp.DateNoLessThan, "date", new java.sql.Date(startDate.getTime()));
        }
        if (StringUtils.isNotBlank(algorithmId) ) {
            builder.where(QueryOp.StringEqualTo, "algorithmId", algorithmId);
        }
        if (Objects.nonNull(isReport)){
            builder.where(QueryOp.NumberEqualTo, "report", isReport);
        }
        builder.addOrderByAsc("date");
        DBQueryParam dbQueryParam = builder.build();
        List<ReportAlgorithmAccuracyRecallDayDO> accuracyDayVOS = reportAccuracyDayDAO.query(dbQueryParam).getDatas();
        return accuracyDayVOS;
    }

    public ReportAlgorithmAccuracyRecallDayDO findReportAccuracy(String cityId, String caliberId ,String algorithmId,Date date) throws Exception {
        DBQueryParamBuilder builder = DBQueryParamBuilder.create().queryDataOnly();
        if (StringUtils.isNotBlank(cityId) && cityId != null) {
            builder.where(QueryOp.StringEqualTo, "cityId", cityId);
        }

        if (StringUtils.isNotBlank(caliberId) && caliberId != null) {
            builder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (date != null) {
            builder.where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()));
        }
        if (algorithmId != null) {
            builder.where(QueryOp.StringEqualTo, "algorithmId", algorithmId);
        }
        builder.addOrderByAsc("date");
        DBQueryParam dbQueryParam = builder.build();
        List<ReportAlgorithmAccuracyRecallDayDO> accuracyDayVOS = reportAccuracyDayDAO.query(dbQueryParam).getDatas();
        if (accuracyDayVOS!=null&&accuracyDayVOS.size()>0)
        {
            return accuracyDayVOS.get(0);
        }
        return null;
    }

}
