package com.tsintergy.lf.serviceimpl.system.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingBatchInitDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/6/5 13:52
 **/

@Component
public class SettingBatchInitDAO extends BaseAbstractDAO<SettingBatchInitDO> {

    public List<SettingBatchInitDO> getBatchList() {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        List<SettingBatchInitDO> settingSystemVOs = this.query(param).getDatas();
        return settingSystemVOs;
    }

    public SettingBatchInitDO getBatchById(String id) {
        DBQueryParamBuilder builder = DBQueryParamBuilder.create().queryDataOnly();
        builder.where(QueryOp.StringEqualTo, "id", id);
        List<SettingBatchInitDO> datas = this.query(builder.build()).getDatas();
        if (CollectionUtils.isNotEmpty(datas)) {
            return datas.get(0);
        }
        return null;
    }
}
