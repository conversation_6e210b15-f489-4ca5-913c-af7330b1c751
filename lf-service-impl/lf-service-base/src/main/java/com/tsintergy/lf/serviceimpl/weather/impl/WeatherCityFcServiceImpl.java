
package com.tsintergy.lf.serviceimpl.weather.impl;


import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.DateUtil;
import com.tsintergy.lf.core.constants.WeatherSourceEnum;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.Base96DO;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingSystemDO;
import com.tsintergy.lf.serviceapi.base.weather.annotation.FcWeatherDataSource;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.tsintergy.lf.serviceapi.base.weather.collect.WeatherFcCollectDTO;
import com.tsintergy.lf.serviceapi.base.weather.collect.WeatherFcCollectVO;
import com.tsintergy.lf.serviceapi.base.weather.dto.*;
import com.tsintergy.lf.serviceapi.base.weather.pojo.*;
import com.tsintergy.lf.serviceimpl.common.constants.DataUtil;
import com.tsintergy.lf.serviceimpl.common.util.LoadCalUtil;
import com.tsintergy.lf.serviceimpl.common.util.PeriodDataUtil;
import com.tsintergy.lf.serviceimpl.system.constant.SystemConstant;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcClctGDTDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcClctZHTDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcClctZYTDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcDAO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $Id: WeatherCityFcServiceImpl.java, v 0.1 2018-01-31 10:59:53 tao Exp $$
 */

@Service
@FcWeatherDataSource(source = "jt")
public class WeatherCityFcServiceImpl extends AbstractBaesWeatherServiceImpl implements WeatherCityFcService {

    private static final Logger logger = LogManager.getLogger(WeatherCityFcServiceImpl.class);

    @Autowired
    WeatherCityFcModifyService weatherCityFcModifyService;

    @Autowired
    private WeatherCityFcClctGDTDAO weatherCityFcClctGDTDAO;

    @Autowired
    private WeatherCityFcClctZHTDAO weatherCityFcClctZHTDAO;

    @Autowired
    private WeatherCityFcClctZYTDAO weatherCityFcClctZYTDAO;

    @Autowired
    CityService cityService;

    @Autowired
    SettingSystemService settingSystemService;
    @Autowired
    WeatherCityFcDAO weatherCityFcDAO;
    @Autowired
    private WeatherCityFcClctZYTService weatherCityFcClctZYTService;

    @Autowired
    private WeatherCityFcClctGDTService weatherCityFcClctGDTService;

    @Autowired
    private WeatherCityFcClctZHService weatherCityFcClctZHService;
    @Autowired
    private StatisticsSynthesizeWeatherCityDayFcService statisticsSynthesizeWeatherCityDayFcService;

    @Override
    protected List<? extends BaseWeatherDO> findWeatherCityVO(String cityId, Integer type, Date startDate, Date endDate)
        throws Exception {
        return weatherCityFcDAO.findWeatherCityFcDO(cityId, type, startDate, endDate);
    }

    @Override
    protected List<? extends BaseWeatherDO> findStatisticsSynthesizeWeatherCityDayVO(String cityId, Integer type,
        Date startDate, Date endDate) throws Exception {
        return statisticsSynthesizeWeatherCityDayFcService
            .findStatisticsSynthesizeWeatherCityDayFcDO(cityId, type, startDate, endDate);
    }


    @Override
    public DataPackage queryWeatherCityFcDO(DBQueryParam param) throws Exception {
        try {
            return weatherCityFcDAO.query(param);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public DataPackage queryWeatherCityFcDO(DBQueryParam param,String weatherType) throws Exception {
        try{
            switch (weatherType){
                case "weather_city_fc_basic" : {
                    return weatherCityFcDAO.query(param);
                }
                case "gdt" : {
                    return weatherCityFcClctGDTDAO.query(param);
                }
                case "zyt" : {
                    return weatherCityFcClctZYTDAO.query(param);
                }
                case "zht" : {
                    return weatherCityFcClctZHTDAO.query(param);
                }
                default:{
                    return new DataPackage();
                }
            }
        }catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    /**
     * 查找预测数据
     */
    @Override
    public List<WeatherDTO> findWeatherCityFcDTOs(String cityId, Integer type, Date startDate, Date endDate)
        throws Exception {
        try {
            //查询全部
            if (StringUtils.isNotBlank(cityId) && cityId.equals("1")) {
                cityId = null;
            }
            List<WeatherCityFcDO> weatherCityFcVOS = weatherCityFcDAO
                .findWeatherCityFcDO(cityId, type, startDate, endDate);
            if (weatherCityFcVOS.size() < 1) {
                return new ArrayList<>();
            }
            List<WeatherDTO> weatherDTOS = new ArrayList<WeatherDTO>(10);
            for (WeatherCityFcDO weatherCityHisVO : weatherCityFcVOS) {
                WeatherDTO weatherDTO = new WeatherDTO();
                weatherDTO.setId(weatherCityHisVO.getId());
                weatherDTO.setDate(weatherCityHisVO.getDate());
                weatherDTO.setWeek(DateUtil.getWeek(weatherCityHisVO.getDate()));
                weatherDTO.setCity(this.cityService.findCityById(weatherCityHisVO.getCityId()).getCity());
                weatherDTO.setData(BasePeriodUtils.toList(weatherCityHisVO, Constants.WEATHER_CURVE_POINT_NUM,
                    Constants.WEATHER_CURVE_START_WITH_ZERO));
                weatherDTOS.add(weatherDTO);
            }
            return weatherDTOS;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public WeatherCityFcDO doCreate(WeatherCityFcDO vo) throws Exception {
        try {
            return (WeatherCityFcDO) weatherCityFcDAO.create(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public BaseWeatherDO doCreate(BaseWeatherDO vo,String weatherType) throws Exception {
        try{
            switch (weatherType){
                case "weather_city_fc_basic" : {
                    WeatherCityFcDO weatherCityFcDO = new WeatherCityFcDO();
                    BeanUtils.copyProperties(vo,weatherCityFcDO);
                    return weatherCityFcDAO.create(weatherCityFcDO);
                }
                case "gdt" : {
                    WeatherCityFcClctGDTDO weatherCityFcClctGDTDO = new WeatherCityFcClctGDTDO();
                    BeanUtils.copyProperties(vo,weatherCityFcClctGDTDO);
                    return weatherCityFcClctGDTDAO.create(weatherCityFcClctGDTDO);
                }
                case "zyt" : {
                    WeatherCityFcClctZYTDO weatherCityFcClctZYTDO = new WeatherCityFcClctZYTDO();
                    BeanUtils.copyProperties(vo,weatherCityFcClctZYTDO);
                    return weatherCityFcClctZYTDAO.create(weatherCityFcClctZYTDO);
                }
                case "zht" : {
                    WeatherCityFcClctZHTDO weatherCityFcClctZHTDO = new WeatherCityFcClctZHTDO();
                    BeanUtils.copyProperties(vo,weatherCityFcClctZHTDO);
                    return weatherCityFcClctZHTDAO.create(weatherCityFcClctZHTDO);
                }
                default:{
                    return new BaseWeatherDO();
                }
            }
        }catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public void doRemoveWeatherCityFcDO(WeatherCityFcDO vo) throws Exception {
        try {
            weatherCityFcDAO.remove(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public void doRemoveWeatherCityFcDOByPK(Serializable pk) throws Exception {
        try {
            weatherCityFcDAO.removeByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public WeatherCityFcDO doUpdateWeatherCityFcDO(WeatherCityFcDO vo) throws Exception {
        try {
            return (WeatherCityFcDO) weatherCityFcDAO.update(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public BaseWeatherDO doUpdateBaseWeatherDO(BaseWeatherDO vo,String weatherType) throws Exception {
        try {
            switch (weatherType) {
                case "weather_city_fc_basic": {
                    WeatherCityFcDO weatherCityFcDO = new WeatherCityFcDO();
                    BeanUtils.copyProperties(vo,weatherCityFcDO);
                    weatherCityFcDAO.getSession().clear();
                    return weatherCityFcDAO.update(weatherCityFcDO);
                }
                case "gdt": {
                    WeatherCityFcClctGDTDO weatherCityFcClctGDTDO = new WeatherCityFcClctGDTDO();
                    BeanUtils.copyProperties(vo,weatherCityFcClctGDTDO);
                    weatherCityFcClctGDTDAO.getSession().clear();
                    return weatherCityFcClctGDTDAO.update(weatherCityFcClctGDTDO);
                }
                case "zyt": {
                    WeatherCityFcClctZYTDO weatherCityFcClctZYTDO = new WeatherCityFcClctZYTDO();
                    BeanUtils.copyProperties(vo,weatherCityFcClctZYTDO);
                    weatherCityFcClctZYTDAO.getSession().clear();
                    return weatherCityFcClctZYTDAO.update(weatherCityFcClctZYTDO);
                }
                case "zht": {
                    WeatherCityFcClctZHTDO weatherCityFcClctZHTDO = new WeatherCityFcClctZHTDO();
                    BeanUtils.copyProperties(vo,weatherCityFcClctZHTDO);
                    weatherCityFcClctZHTDAO.getSession().clear();
                    return weatherCityFcClctZHTDAO.update(weatherCityFcClctZHTDO);
                }
                default:{
                    return new BaseWeatherDO();
                }
            }
        }catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public WeatherCityFcDO findWeatherCityFcDOByPk(Serializable pk) throws Exception {
        try {
            return (WeatherCityFcDO) weatherCityFcDAO.findByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }
    @Override
    public <T extends BaseWeatherDO> List<T> getWeatherDataFromSettings(String cityId, Integer type, Date startDate, Date endDate) throws Exception {
        SettingSystemDO byFieldId = settingSystemService.findByFieldId(SystemConstant.DEFAULT_FC_WEATHER_SOURCE);
        List<T> fcWeatherData =new ArrayList<>();
        switch (byFieldId.getValue()) {
            case "zyt": {
                fcWeatherData = weatherCityFcClctZYTService
                    .findFcWeatherData(cityId, type, startDate, endDate);
                break;
            }
            case "gdt": {
                fcWeatherData = weatherCityFcClctGDTService
                    .findFcWeatherData(cityId, type, startDate, endDate);
                break;
            }
            case "zht": {
                fcWeatherData = weatherCityFcClctZHService
                    .findFcWeatherData(cityId, type, startDate, endDate);
                break;
            }
            default:{
                fcWeatherData = (List<T>) this.findFcWeatherData(cityId, type, startDate, endDate);
            }
        }
        return fcWeatherData;
    }


    @Override
    public <T extends BaseWeatherDO> List<T> getWeatherDataFromWeatherTypes(String cityId, Integer type, Date startDate, Date endDate,String weatherType) throws Exception {
        List<T> fcWeatherData =new ArrayList<>();
        switch (weatherType) {
            case "zyt": {
                fcWeatherData = weatherCityFcClctZYTService
                        .findFcWeatherData(cityId, type, startDate, endDate);
                break;
            }
            case "gdt": {
                fcWeatherData = weatherCityFcClctGDTService
                        .findFcWeatherData(cityId, type, startDate, endDate);
                break;
            }
            case "zht": {
                fcWeatherData = weatherCityFcClctZHService
                        .findFcWeatherData(cityId, type, startDate, endDate);
                break;
            }
            case "weather_city_fc_basic" :{
                fcWeatherData = (List<T>) this.findFcWeatherData(cityId, type, startDate, endDate);
                break;
            }
            default:{
                return fcWeatherData;
            }
        }
        return fcWeatherData;
    }

   @Override
    public List<BaseWeatherDO> findWeatherCityFcDOs(String cityId, Integer type, Date startDate, Date endDate)
        throws Exception {
        try {
            return getWeatherDataFromSettings(cityId, type, startDate, endDate);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public List<BaseWeatherDO> findWeatherCityFcDOs(String cityId, Integer type, Date startDate, Date endDate,String weatherType)
            throws Exception {
        try {
            return getWeatherDataFromWeatherTypes(cityId, type, startDate, endDate,weatherType);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public List<WeatherNameDTO> findAllByDateAndCityId(String cityId, Date targetDate) throws Exception {
        List<WeatherNameDTO> weatherNameDTOS = new ArrayList<WeatherNameDTO>(4);
        List<BaseWeatherDO> weatherCityFcVOS = getWeatherDataFromSettings(cityId, null, targetDate, targetDate);
        if (weatherCityFcVOS.size() < 1) {
            return null;
        }
        for (BaseWeatherDO weatherCityFcVO : weatherCityFcVOS) {
            WeatherNameDTO weatherNameDTO = new WeatherNameDTO();
            weatherNameDTO.setName(WeatherEnum.getValueByName(weatherCityFcVO.getType()));
            weatherNameDTO.setWeather(BasePeriodUtils
                .toList(weatherCityFcVO, Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO));
            weatherNameDTOS.add(weatherNameDTO);
        }
        return weatherNameDTOS;
    }


    @Override
    public void doInsertOrUpdate(WeatherCityFcDO weatherCityFcVO) throws Exception {
        if (weatherCityFcVO.getDate() == null || weatherCityFcVO.getCityId() == null
            || weatherCityFcVO.getType() == null) {
            throw new BusinessException("01C20180008", "");
        }
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly()
            .where(QueryOp.DateEqualTo, "date", weatherCityFcVO.getDate())
            .where(QueryOp.StringEqualTo, "cityId", weatherCityFcVO.getCityId())
            .where(QueryOp.NumberEqualTo, "type", weatherCityFcVO.getType());
        List<WeatherCityFcDO> weatherCityFcVOS = this.queryWeatherCityFcDO(dbQueryParamBuilder.build()).getDatas();
        if (weatherCityFcVOS == null || weatherCityFcVOS.size() < 1) {
            weatherCityFcVO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            this.doCreate(weatherCityFcVO);
            return;
        }
        weatherCityFcDAO.getSession().flush();
        weatherCityFcDAO.getSession().clear();
        WeatherCityFcDO old = weatherCityFcVOS.get(0);
        PeriodDataUtil.replaceValues(old, weatherCityFcVO);
        old.setUpdatetime(new Timestamp(System.currentTimeMillis()));
        this.doUpdateWeatherCityFcDO(old);
    }

    @Override
    public void doInsertOrUpdate(BaseWeatherDO baseWeatherDO, String weatherType) throws Exception {
        if (baseWeatherDO.getDate() == null || baseWeatherDO.getCityId() == null
                || baseWeatherDO.getType() == null) {
            throw new BusinessException("01C20180008", "");
        }
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly()
                .where(QueryOp.DateEqualTo, "date", baseWeatherDO.getDate())
                .where(QueryOp.StringEqualTo, "cityId", baseWeatherDO.getCityId())
                .where(QueryOp.NumberEqualTo, "type", baseWeatherDO.getType());
        List<BaseWeatherDO> baseWeatherDOS = this.queryWeatherCityFcDO(dbQueryParamBuilder.build(),weatherType).getDatas();
        if (baseWeatherDOS == null || baseWeatherDOS.size() < 1) {
            baseWeatherDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            this.doCreate(baseWeatherDO,weatherType);
            return;
        }
        switch (weatherType){
            case "weather_city_fc" : {
                weatherCityFcDAO.getSession().flush();
                weatherCityFcDAO.getSession().clear();
            }
            case "gdt":{
                weatherCityFcClctGDTDAO.getSession().flush();
                weatherCityFcClctGDTDAO.getSession().flush();
            }
            case "zyt":{
                weatherCityFcClctZYTDAO.getSession().flush();
                weatherCityFcClctZYTDAO.getSession().flush();
            }
            case "zht":{
                weatherCityFcClctZHTDAO.getSession().flush();
                weatherCityFcClctZHTDAO.getSession().flush();
            }
        }


        BaseWeatherDO old = baseWeatherDOS.get(0);
        PeriodDataUtil.replaceValues(old, baseWeatherDO);
        old.setUpdatetime(new Timestamp(System.currentTimeMillis()));
        this.doUpdateBaseWeatherDO(old,weatherType);

    }

    @Override
    public List<BigDecimal> find96WeatherCityFcValue(Date date, String cityId, Integer type) throws Exception {
        cityId = cityService.findWeatherCityId(cityId);
        List<BaseWeatherDO> weatherDataFromSettings = getWeatherDataFromSettings(cityId, type, date, date);
        if (!CollectionUtils.isEmpty(weatherDataFromSettings)) {
            return BasePeriodUtils.toList(weatherDataFromSettings.get(0), Constants.WEATHER_CURVE_POINT_NUM,
                Constants.WEATHER_CURVE_START_WITH_ZERO);
        }
        return null;
    }


    @Override
    public WeatherDeatilDTO findWeatherDeatilDTO(Date date, String cityId) throws Exception {
        cityId = cityService.findWeatherCityId(cityId);
        // 原气象源
        List<BaseWeatherDO> weatherCityFcVOs = this.findWeatherCityFcDOs(cityId, null, date, date);
        Map<Integer, BaseWeatherDO> weatherMap = weatherCityFcVOs.stream()
            .collect(Collectors.toMap(BaseWeatherDO::getType, Function.identity()));
        // 广东台气象源
        Map<Integer, WeatherCityFcClctGDTDO> weatherGDTMap = weatherCityFcClctGDTDAO
            .findWeatherCityFcDO(cityId, null, date, date).stream()
            .collect(Collectors.toMap(WeatherCityFcClctGDTDO::getType, Function.identity()));
        // 综合气象源
        Map<Integer, WeatherCityFcClctZHTDO> weatherZHMap = weatherCityFcClctZHTDAO
            .findWeatherCityFcDO(cityId, null, date, date).stream()
            .collect(Collectors.toMap(WeatherCityFcClctZHTDO::getType, Function.identity()));
        // 中央台气象源
        Map<Integer, WeatherCityFcClctZYTDO> weatherZYTMap = weatherCityFcClctZYTDAO
            .findWeatherCityFcDO(cityId, null, date, date).stream()
            .collect(Collectors.toMap(WeatherCityFcClctZYTDO::getType, Function.identity()));
        String weatherCityId = cityService.findWeatherCityId(cityId);

        List<WeatherCityFcModifyDO> modifyWeatherByDateAndType = weatherCityFcModifyService
            .findModifyWeatherByDateAndType(new SimpleDateFormat("yyyy-MM-dd").format(date), null, weatherCityId);
        Map<Integer, List<WeatherCityFcModifyDO>> modifyWeather = null;
        if (!CollectionUtils.isEmpty(modifyWeatherByDateAndType)) {
            modifyWeather = modifyWeatherByDateAndType.stream()
                .collect(Collectors.groupingBy(WeatherCityFcModifyDO::getType));
        }

        List<WeatherCurveDTOList> weatherCurveDTOListList = new ArrayList<>();
        List<WeatherFeatureDTO> weatherFeatureDTOS = new ArrayList<>();
        for (WeatherEnum weatherEnum : WeatherEnum.values()) {
            if (weatherEnum.getType() > 4) {
                continue;
            }
            // 气象数据源
            List<WeatherCurveDTO> weatherCurveDTOS = new ArrayList<>();
            BaseWeatherDO fcVO = weatherMap.get(weatherEnum.getType());
            getWeatherCurve(weatherFeatureDTOS, weatherEnum, fcVO, weatherCurveDTOS, WeatherSourceEnum.JT);
            WeatherCityFcClctGDTDO weatherCityFcClctGDTDO = weatherGDTMap.get(weatherEnum.getType());
            getWeatherCurve(weatherFeatureDTOS, weatherEnum, weatherCityFcClctGDTDO, weatherCurveDTOS, WeatherSourceEnum.GDT);
            WeatherCityFcClctZHTDO weatherCityFcClctZHDO = weatherZHMap.get(weatherEnum.getType());
            getWeatherCurve(weatherFeatureDTOS, weatherEnum, weatherCityFcClctZHDO, weatherCurveDTOS, WeatherSourceEnum.ZHT);
            WeatherCityFcClctZYTDO weatherCityFcClctZYTDO = weatherZYTMap.get(weatherEnum.getType());
            getWeatherCurve(weatherFeatureDTOS, weatherEnum, weatherCityFcClctZYTDO, weatherCurveDTOS, WeatherSourceEnum.ZYT);

            // 人工修正
            List<BigDecimal> weatherFcModifyValues = null;
            if ((modifyWeather == null || CollectionUtils.isEmpty(modifyWeather.get(weatherEnum.getType())) ||
                (modifyWeather.get(weatherEnum.getType()).size() > 0
                    && modifyWeather.get(weatherEnum.getType()).get(0) == null)) && !CollectionUtils.isEmpty(weatherCurveDTOS)) {
                weatherFcModifyValues = weatherCurveDTOS.get(0).getCurve();
            } else {
                weatherFcModifyValues = BasePeriodUtils
                    .toList(modifyWeather.get(weatherEnum.getType()).get(0), Constants.WEATHER_CURVE_POINT_NUM,
                        Constants.WEATHER_CURVE_START_WITH_ZERO);
            }
            WeatherCurveDTO weatherModifyDTO = new WeatherCurveDTO(WeatherSourceEnum.MODIFY.getCode(),
                weatherFcModifyValues != null ? weatherFcModifyValues : DataUtil
                    .generate96Zero());
            weatherCurveDTOS.add(weatherModifyDTO);
            calWeatherFeature(weatherFeatureDTOS, weatherFcModifyValues, WeatherSourceEnum.MODIFY, weatherEnum);

            WeatherCurveDTOList weatherCurveDTOList = new WeatherCurveDTOList(String.valueOf(weatherEnum.getType()),
                weatherCurveDTOS);
            weatherCurveDTOListList.add(weatherCurveDTOList);
        }

        return new WeatherDeatilDTO(weatherCurveDTOListList, weatherFeatureDTOS);
    }

    private <T extends Base96DO> void getWeatherCurve(List<WeatherFeatureDTO> weatherFeatureDTOS,
        WeatherEnum weatherEnum,
        T fcVO, List<WeatherCurveDTO> weatherCurveDTOS, WeatherSourceEnum sourceEnum) {
        if (fcVO != null) {
            List<BigDecimal> weatherFcValues = BasePeriodUtils
                .toList(fcVO, Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
            WeatherCurveDTO weatherFcDTO = new WeatherCurveDTO(sourceEnum.getCode(), weatherFcValues);
            weatherCurveDTOS.add(weatherFcDTO);
            calWeatherFeature(weatherFeatureDTOS, weatherFcValues, sourceEnum, weatherEnum);
        }
    }

    private void calWeatherFeature(List<WeatherFeatureDTO> weatherFeatureDTOS, List<BigDecimal> weatherFcValues,
        WeatherSourceEnum sourceEnum, WeatherEnum weatherEnum) {
        Map<String, WeatherFeatureDTO> featureDTOMap = weatherFeatureDTOS.stream()
            .collect(Collectors.toMap(WeatherFeatureDTO::getWeatherName, Function.identity(), (v1, v2) -> v2));
        WeatherFeatureDTO weatherFeatureDTO;
        if(featureDTOMap.containsKey(sourceEnum.getName())){
            weatherFeatureDTO = featureDTOMap.get(sourceEnum.getName());
        }else {
            weatherFeatureDTO = new WeatherFeatureDTO(sourceEnum.getName());
            weatherFeatureDTOS.add(weatherFeatureDTO);
        }
        if (weatherEnum.equals(WeatherEnum.TEMPERATURE)) {
            if (weatherFcValues != null) {
                weatherFeatureDTO.setHighestTemperature(LoadCalUtil.max(weatherFcValues));
                weatherFeatureDTO.setLowestTemperature(LoadCalUtil.min(weatherFcValues));
            }
        }
        if (weatherEnum.equals(WeatherEnum.RAINFALL)) {
            if (weatherFcValues != null) {
                weatherFeatureDTO.setRainfall(BigDecimalUtils.addAllValue(weatherFcValues));
            }
        }
        if (weatherEnum.equals(WeatherEnum.HUMIDITY)) {
            if (weatherFcValues != null) {
                weatherFeatureDTO
                    .setAveHumidity(BasePeriodUtils.getMaxMinAvg(weatherFcValues, 4).get(BasePeriodUtils.LOAD_AVG));
            }
        }
    }

    @Override
    public List<WeatherCityFcDO> findWeatherCityFcDOs(String cityId, Date date, Date date1) throws Exception {
        return weatherCityFcDAO.findWeatherCityFcDO(cityId, date, date);
    }

    @Override
    public List<WeatherCityFcDO> findWeatherCityHisDOs(List<String> cityIds, Integer type, Date hisStartDate,
        Date endDate) {
        return weatherCityFcDAO.findWeatherCityHisDOs(cityIds, type, hisStartDate, endDate);
    }

    private Map<String, List<WeatherFcCollectDTO>> weatherFcCollectVOListToDTOMap(
        List<WeatherFcCollectVO> weatherFcCollectVOS) {
        Map<String, List<WeatherFcCollectDTO>> map = new HashMap<String, List<WeatherFcCollectDTO>>();
        for (WeatherFcCollectVO weatherFcCollectVO : weatherFcCollectVOS) {
            Date date = DateUtils.addHours(weatherFcCollectVO.getForecastStartTime(), weatherFcCollectVO.getTimes());
            String time = DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_STR);
            if (map.get(time) == null) {
                map.put(time, new ArrayList<WeatherFcCollectDTO>());
            }
            WeatherFcCollectDTO weatherFcCollectDTO = new WeatherFcCollectDTO();
            weatherFcCollectDTO.setDate(date);
            weatherFcCollectDTO.setHumidity(weatherFcCollectVO.getHumidity());
            weatherFcCollectDTO.setRainfall(weatherFcCollectVO.getRainfall());
            weatherFcCollectDTO.setStationNo(weatherFcCollectVO.getStationNo());
            weatherFcCollectDTO.setTemperature(weatherFcCollectVO.getTemperature());
            weatherFcCollectDTO.setWindSpeed(weatherFcCollectVO.getWindSpeed());
            map.get(time).add(weatherFcCollectDTO);
        }
        return map;
    }


    @Override
    public List<WeatherCityFcDO>  findFcWeatherData(String cityId, Integer type, Date startDate, Date endDate)
        throws Exception {
        return weatherCityFcDAO.findWeatherCityFcDO(cityId,type,startDate,endDate);
    }
}
