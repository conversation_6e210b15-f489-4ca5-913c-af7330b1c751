package com.tsintergy.lf.serviceimpl.report.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.DateUtil;
import com.tsintergy.lf.serviceapi.base.area.api.BasePlanService;
import com.tsintergy.lf.serviceapi.base.check.api.SettingCheckService;
import com.tsintergy.lf.serviceapi.base.check.pojo.SettingCheckPrecisionDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.recall.api.LoadCityFcRecallService;
import com.tsintergy.lf.serviceapi.base.recall.pojo.LoadCityFcRecallDO;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAlgorithmAccuracyDayRecallService;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAlgorithmAccuracyRecallDayDO;
import com.tsintergy.lf.serviceimpl.common.constants.LoadCalUtil;
import com.tsintergy.lf.serviceimpl.report.dao.ReportAlgorithmAccuracyDayRecallDAO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2025-05-07
 * @since 1.0.0
 */
@Service("reportAlgorithmAccuracyDayRecallService")
public class ReportAlgorithmAccuracyDayRecallServiceImpl  implements ReportAlgorithmAccuracyDayRecallService {

    @Autowired
    private LoadFeatureCityDayHisService loadFeatureCityDayHisService;

    @Autowired
    private BasePlanService basePlanService;

    @Autowired
    private ReportAlgorithmAccuracyDayRecallDAO reportAlgorithmAccuracyDayDAO;

    @Autowired
    private SettingCheckService settingCheckService;

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private LoadCityFcRecallService loadCityFcRecallService;

    @Override
    public List<ReportAlgorithmAccuracyRecallDayDO> doStatSaveReportAccuracy(String cityId, String caliberId, Date startDate, Date endDate) throws Exception {
        List<ReportAlgorithmAccuracyRecallDayDO> reportAccuracyDayVOS = new ArrayList<>();
        List<LoadCityFcRecallDO> loadAllData = loadCityFcRecallService.getLoadAllData(cityId, caliberId, startDate,
            endDate, null);
        List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.getLoadCityHisDOS(cityId, caliberId, startDate,
            endDate);
        List<LoadFeatureCityDayFcDO> fcFeature = featureData(loadAllData);
        Map<String, BigDecimal> pointAccuracyData = getPointAccuracyData(loadAllData, loadCityHisDOS);
        List<LoadFeatureCityDayHisDO> realLoads = loadFeatureCityDayHisService
            .findLoadFeatureCityDayHisDOS(cityId, startDate, endDate, caliberId);
        Map<String, LoadFeatureCityDayHisDO> hisFeatureMap = realLoads.stream().collect(
            Collectors.toMap(reportLoadDayVO -> reportLoadDayVO.getCityId() + "-" + reportLoadDayVO.getCaliberId()
                + "-" + reportLoadDayVO.getDate().getTime(), Function.identity()));
        Date date2015 = DateUtil.getDate("2025-01-01", "yyyy-MM-dd");
        List<String> defaultAreaIds = basePlanService.getDefaultAreaIds();
        Map<String, SettingCheckPrecisionDO> map = settingCheckService.findStandMap();
        for (LoadFeatureCityDayFcDO featureDTO : fcFeature) {
            //特殊处理，澳门和地方电不做统计
            if (featureDTO.getCityId().equals("23") || featureDTO.getCityId().equals("24") || defaultAreaIds.contains(featureDTO.getCityId())) {
                continue;
            }
            String hisKey = featureDTO.getCityId() + "-" + featureDTO.getCaliberId() + "-"
                + featureDTO.getDate().getTime();
            String algorithmKey = featureDTO.getCityId() + "-" + featureDTO.getCaliberId() + "-" + featureDTO.getAlgorithmId() + "-"
                + featureDTO.getDate().getTime();

            LoadFeatureCityDayHisDO loadDayVO = hisFeatureMap.get(hisKey);
            BigDecimal bigDecimal = pointAccuracyData.get(algorithmKey);
            ReportAlgorithmAccuracyRecallDayDO reportAccuracyDayVO = new ReportAlgorithmAccuracyRecallDayDO();
            reportAccuracyDayVO.setCityId(featureDTO.getCityId());
            reportAccuracyDayVO.setCaliberId(featureDTO.getCaliberId());
            reportAccuracyDayVO.setDate(featureDTO.getDate());
            reportAccuracyDayVO.setAlgorithmId(featureDTO.getAlgorithmId());
            reportAccuracyDayVO.setReport(featureDTO.getReport());
            if (bigDecimal != null) {
                reportAccuracyDayVO.setPointAccuracy(bigDecimal);
            } else {
                reportAccuracyDayVO.setPointAccuracy(BigDecimal.ZERO);
            }
            if (loadDayVO != null) {
                BigDecimal hisMaxLoad = loadDayVO.getMaxLoad();
                BigDecimal hisMinLoad = loadDayVO.getMinLoad();
                //计算全网的最大/最小负荷预测准确率时， 实际值从滚动负荷表里取
                reportAccuracyDayVO
                    .setMaxAccuracy(LoadCalUtil.calcMaxMinAccuracy(hisMaxLoad, featureDTO.getMaxLoad()));
                reportAccuracyDayVO
                    .setMinAccuracy(LoadCalUtil.calcMaxMinAccuracy(hisMinLoad, featureDTO.getMinLoad()));
                // TODO 修改 2025年日综合准确率为新版计算公式 之前则使用旧公式
                if (reportAccuracyDayVO.getDate().compareTo(date2015) >= 0) {
                    reportAccuracyDayVO
                        .setComprehensiveAccuracy(LoadCalUtil.compreGradient(
                            reportAccuracyDayVO.getPointAccuracy(),
                            reportAccuracyDayVO.getMaxAccuracy(),
                            reportAccuracyDayVO.getMinAccuracy()));
                } else {
                    reportAccuracyDayVO
                        .setComprehensiveAccuracy(LoadCalUtil.compreGradient(
                            reportAccuracyDayVO.getPointAccuracy(),
                            reportAccuracyDayVO.getMaxAccuracy(),
                            reportAccuracyDayVO.getMinAccuracy(), reportAccuracyDayVO.getEneryAccuracy()));
                }

            } else {
                reportAccuracyDayVO.setMinAccuracy(BigDecimal.ZERO);
                reportAccuracyDayVO.setMaxAccuracy(BigDecimal.ZERO);
                // TODO
                reportAccuracyDayVO.setEneryAccuracy(BigDecimal.ZERO);
                reportAccuracyDayVO.setComprehensiveAccuracy(BigDecimal.ZERO);
            }
            //计算合格率
            SettingCheckPrecisionDO settingCheckPrecisionDO = map.get(featureDTO.getCityId() + "-" + DateUtil.getYearByDate(featureDTO.getDate()));
            if (settingCheckPrecisionDO != null) {
                Map<String, BigDecimal> decimalMap = LoadCalUtil.toMap(settingCheckPrecisionDO);
                BigDecimal standard = decimalMap.get(DateUtil.getMonthDate(featureDTO.getDate()));
                BigDecimal comprehensiveAccuracy = reportAccuracyDayVO.getComprehensiveAccuracy().multiply(new BigDecimal("100"));
                reportAccuracyDayVO.setStandardAccuracy(standard);
                if (comprehensiveAccuracy.compareTo(standard) >= 0) {
                    reportAccuracyDayVO.setPass(BigDecimal.ONE);
                } else {
                    reportAccuracyDayVO.setPass(BigDecimal.ZERO);
                }
            }
            reportAccuracyDayVOS.add(reportAccuracyDayVO);
        }
        return reportAccuracyDayVOS;
    }

    public List<LoadFeatureCityDayFcDO> featureData(List<LoadCityFcRecallDO> loadAllData) {
        List<LoadFeatureCityDayFcDO> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(loadAllData)) {
            for (LoadCityFcRecallDO loadAllDatum : loadAllData) {
                LoadFeatureCityDayFcDO loadFeatureCityDayFcDO = new LoadFeatureCityDayFcDO();
                loadFeatureCityDayFcDO.setCityId(loadAllDatum.getCityId());
                loadFeatureCityDayFcDO.setCaliberId(loadAllDatum.getCaliberId());
                loadFeatureCityDayFcDO.setAlgorithmId(loadAllDatum.getAlgorithmId());
                loadFeatureCityDayFcDO.setDate(loadAllDatum.getDate());
                loadFeatureCityDayFcDO.setReport(loadAllDatum.getReport());
                loadFeatureCityDayFcDO.setMaxLoad(BigDecimalUtils.getMax(loadAllDatum.getloadList()));
                loadFeatureCityDayFcDO.setMinLoad(BigDecimalUtils.getMin(loadAllDatum.getloadList()));
                result.add(loadFeatureCityDayFcDO);
            }
        }
        return result;
    }

    public Map<String, BigDecimal> getPointAccuracyData(List<LoadCityFcRecallDO> loadAllData, List<LoadCityHisDO>
        loadCityHisDOS) throws Exception {
        Map<String, BigDecimal> result = new HashMap<>();
        Map<String, LoadCityHisDO> collect = new HashMap<>();
        if (!CollectionUtils.isEmpty(loadCityHisDOS)) {
            collect = loadCityHisDOS.stream().collect(
                Collectors.toMap(loadCityHisDO -> loadCityHisDO.getCityId() + "-" + loadCityHisDO.getCaliberId()
                    + "-" + loadCityHisDO.getDate().getTime(), Function.identity()));
        }
        if (!CollectionUtils.isEmpty(loadAllData)) {
            for (LoadCityFcRecallDO loadAllDatum : loadAllData) {
                String key = loadAllDatum.getCityId() + "-" + loadAllDatum.getCaliberId() + "-" +
                    loadAllDatum.getDate().getTime();
                LoadCityHisDO loadCityHisDO = collect.get(key);
                BigDecimal dayAccuracy = LoadCalUtil.getDayAccuracy(loadCityHisDO, loadAllDatum,
                    Constants.LOAD_CURVE_POINT_NUM);
                String keyResult = loadAllDatum.getCityId() + "-" + loadAllDatum.getCaliberId() + "-" +
                    loadAllDatum. getAlgorithmId() + "-" + loadAllDatum.getDate().getTime();
                result.put(keyResult, dayAccuracy);
            }
        }
        return result;
    }

    @Override
    public void doSaveOrUpdate(List<ReportAlgorithmAccuracyRecallDayDO> reportAccuracyDayVOS) throws Exception {
        if (CollectionUtils.isEmpty(reportAccuracyDayVOS)) {
            return;
        }
        for (ReportAlgorithmAccuracyRecallDayDO accuracyDayVO : reportAccuracyDayVOS) {
            ReportAlgorithmAccuracyRecallDayDO ReportAccuracyDayDO = reportAlgorithmAccuracyDayDAO
                .findReportAccuracy(accuracyDayVO.getCityId(), accuracyDayVO.getCaliberId(), accuracyDayVO.getAlgorithmId(), accuracyDayVO.getDate());
            if (Objects.nonNull(ReportAccuracyDayDO)) {
                BeanUtils.copyProperties(accuracyDayVO, ReportAccuracyDayDO, "id", "createTime");
                ReportAccuracyDayDO.setUpdateTime(new Date());
                reportAlgorithmAccuracyDayDAO.updateAndFlush(ReportAccuracyDayDO);
            } else {
                accuracyDayVO.setCreateTime(new Date());
                reportAlgorithmAccuracyDayDAO.createAndFlush(accuracyDayVO);
            }
        }
    }

    @Override
    public List<ReportAlgorithmAccuracyRecallDayDO> getReportAccuracy(String cityId, String caliberId, String algorithmId, Date startDate, Date endDate, Boolean isReport) throws Exception {
        return reportAlgorithmAccuracyDayDAO.findReportAccuracies(cityId, caliberId, algorithmId, startDate, endDate, isReport);
    }
}
