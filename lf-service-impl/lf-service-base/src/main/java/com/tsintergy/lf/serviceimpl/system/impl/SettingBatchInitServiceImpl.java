package com.tsintergy.lf.serviceimpl.system.impl;

import com.tsintergy.lf.serviceapi.base.system.api.SettingBatchInitService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingBatchInitDO;
import com.tsintergy.lf.serviceimpl.system.dao.SettingBatchInitDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/6/5 14:00
 **/
@Service("settingBatchInitService")
public class SettingBatchInitServiceImpl implements SettingBatchInitService {

    @Autowired
    private SettingBatchInitDAO settingBatchInitDAO;
    @Override
    public List<SettingBatchInitDO> getBatchList() {
        return settingBatchInitDAO.getBatchList();
    }

    @Override
    public SettingBatchInitDO getBatchById(String id) {
        if (id != null) {
            return settingBatchInitDAO.getBatchById(id);
        } else {
            SettingBatchInitDO batchInitDO = new SettingBatchInitDO();
            batchInitDO.setStartTime("00:00");
            batchInitDO.setEndTime("23:59");
            return batchInitDO;
        }
    }
}
