package com.tsintergy.lf.serviceimpl.power.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.exception.DAOException;
import com.tsintergy.lf.core.constants.DateUtil;
import com.tsintergy.lf.serviceapi.base.power.pojo.BasePowerInfoDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/3/21 16:00
 **/
@Component
public class BasePowerInfoDAO extends BaseAbstractDAO<BasePowerInfoDO> {


    /**
     * date 为null 查询最新
     *
     * @param cityId
     * @param date
     * @return
     * @throws DAOException
     */
    public BasePowerInfoDO getNowPowerInfo(String cityId, Date date) throws DAOException {
        DBQueryParamBuilder builder = DBQueryParamBuilder.create().queryDataOnly();
        builder.addOrderByDesc("date");
        if (date != null) {
            builder.where(QueryOp.DateLessThan, "date", date);
        }
        if (StringUtils.isNotEmpty(cityId)) {
            builder.where(QueryOp.StringEqualTo, "cityId", cityId);
            DBQueryParam dbQueryParam = builder.build();

            List<BasePowerInfoDO> datas = this.query(dbQueryParam).getDatas();
            if (!CollectionUtils.isEmpty(datas)) {
                return datas.get(0);
            }
        } else {
            DBQueryParam dbQueryParam = builder.build();
            List<BasePowerInfoDO> datas = this.query(dbQueryParam).getDatas();
            if (!CollectionUtils.isEmpty(datas)) {
                Date nowDate = datas.get(0).getDate();
                builder.where(QueryOp.DateEqualTo, "date", nowDate);
                DBQueryParam build = builder.build();
                datas = this.query(build).getDatas();

                if (!CollectionUtils.isEmpty(datas)) {
                    BigDecimal lowCapacity = BigDecimal.ZERO;
                    BigDecimal midCapacity = BigDecimal.ZERO;
                    BigDecimal totalCapacity = BigDecimal.ZERO;
                    BigDecimal sumCapacity = BigDecimal.ZERO;

                    Integer lowNum = 0;
                    Integer midNum = 0;
                    Integer sumNum = 0;
                    for (BasePowerInfoDO data : datas) {
                        if (data.getLowCapacity() != null) {
                            lowCapacity = lowCapacity.add(new BigDecimal(data.getLowCapacity()));
                        }
                        if (data.getMidCapacity() != null) {
                            midCapacity = midCapacity.add(new BigDecimal(data.getMidCapacity()));
                        }
                        if (data.getLowNum() != null) {
                            lowNum = lowNum + data.getLowNum();
                        }
                        if (data.getMidNum() != null) {
                            midNum = midNum + data.getMidNum();
                        }
                        if (data.getTotalCapacity() != null) {
                            totalCapacity = totalCapacity.add(new BigDecimal(data.getTotalCapacity()));
                        }
                        if (data.getSumNum() != null) {
                            sumNum = sumNum + data.getSumNum();
                        }
                        if (data.getSumCapacity() != null) {
                            sumCapacity = sumCapacity.add(new BigDecimal(data.getSumCapacity()));
                        }

                    }
                    BasePowerInfoDO basePowerInfoDO = new BasePowerInfoDO();
                    basePowerInfoDO.setDate(nowDate);
                    basePowerInfoDO.setMidCapacity(midCapacity.toString());
                    basePowerInfoDO.setLowCapacity(lowCapacity.toString());
                    basePowerInfoDO.setLowNum(lowNum);
                    basePowerInfoDO.setMidNum(midNum);
                    basePowerInfoDO.setTotalCapacity(totalCapacity.toString());
                    basePowerInfoDO.setTotalNum(lowNum+midNum);
                    basePowerInfoDO.setSumCapacity(sumCapacity.toString());
                    basePowerInfoDO.setSumNum(sumNum);
                    return basePowerInfoDO;
                }
            }
        }
        return null;
    }


    public List<BasePowerInfoDO> getNowPowerInfoList(String cityId, Date date) {
        DBQueryParamBuilder builder = DBQueryParamBuilder.create().queryDataOnly();
        builder.addOrderByDesc("date");
        builder.where(QueryOp.DateLessThan, "date", date);
        if (Objects.nonNull(cityId)) {
            builder.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        DBQueryParam dbQueryParam = builder.build();
        List<BasePowerInfoDO> datas = this.query(dbQueryParam).getDatas();
        if (!CollectionUtils.isEmpty(datas)) {
            BasePowerInfoDO basePowerInfoDO = datas.get(0);
            Date nowDate = basePowerInfoDO.getDate();
            DBQueryParamBuilder builder1 = DBQueryParamBuilder.create().queryDataOnly();
            builder1.where(QueryOp.DateEqualTo, "date", nowDate);
            DBQueryParam dbQueryParam1 = builder.build();
            List<BasePowerInfoDO> datas1 = this.query(dbQueryParam1).getDatas();
            return datas1;
        }
        return null;
    }

}
