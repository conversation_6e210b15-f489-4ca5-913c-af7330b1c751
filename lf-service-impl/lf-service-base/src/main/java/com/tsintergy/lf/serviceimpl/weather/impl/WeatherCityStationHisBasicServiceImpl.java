package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityStationHisBasicService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityStationHisBasicDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityStationHisBasicDAO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service("weatherCityStationHisBasicService")
public class WeatherCityStationHisBasicServiceImpl implements WeatherCityStationHisBasicService {

    @Autowired
    WeatherCityStationHisBasicDAO weatherCityStationHisBasicDAO;

    @Override
    public void saveOrUpdate(WeatherCityStationHisBasicDO weatherCityStationHisBasicDO) throws Exception {
        List<WeatherCityStationHisBasicDO> weatherCityFcDOS = weatherCityStationHisBasicDAO
                .findWeatherCityFcDO(weatherCityStationHisBasicDO.getCityId(), weatherCityStationHisBasicDO.getType(),
                        weatherCityStationHisBasicDO.getDate(), weatherCityStationHisBasicDO.getDate());
        if (CollectionUtils.isNotEmpty(weatherCityFcDOS)){
            WeatherCityStationHisBasicDO cityFcClctGDTDO = weatherCityFcDOS.get(0);
            weatherCityStationHisBasicDO.setId(cityFcClctGDTDO.getId());
        }else {
            weatherCityStationHisBasicDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-",""));
        }
        weatherCityStationHisBasicDAO.saveOrUpdateEntityByTemplate(weatherCityStationHisBasicDO);
    }
}
