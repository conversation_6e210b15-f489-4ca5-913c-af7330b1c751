
package com.tsintergy.lf.serviceimpl.weather.impl;


import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.weather.annotation.FcWeatherDataSource;
import com.tsintergy.lf.serviceapi.base.weather.api.BaseFcWeatherService;
import com.tsintergy.lf.serviceapi.base.weather.api.FcWeatherService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.BaseWeatherDO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class FcWeatherServiceImpl  implements FcWeatherService {




    private static final Logger logger = LogManager.getLogger(FcWeatherServiceImpl.class);

    @Autowired
    CityService cityService;

    private Map<String,BaseFcWeatherService> baseFcWeatherServiceMap;

    @Autowired
    public void setBaseFcWeatherServiceMap(List<BaseFcWeatherService> baseFcWeatherServiceLists) {
        // 检查传入的列表是否为空或 null
        if (baseFcWeatherServiceLists == null || baseFcWeatherServiceLists.isEmpty()) {
            // 若为空，记录警告日志并将 map 初始化为空 Map
            logger.warn("No BaseFcWeatherService implementations found.");
            baseFcWeatherServiceMap = java.util.Collections.emptyMap();
            return;
        }
        baseFcWeatherServiceMap = baseFcWeatherServiceLists.stream()
                // 过滤掉没有 FcWeatherDataSource 注解的服务
                .filter(service -> {
                    FcWeatherDataSource annotation = AnnotationUtils.findAnnotation(service.getClass(), FcWeatherDataSource.class);
                    if (annotation == null) {
                        // 记录警告日志
                        logger.warn("Service {} does not have FcWeatherDataSource annotation", service.getClass().getName());
                        return false;
                    }
                    return true;
                })
                .collect(Collectors.toMap(
                        // 获取注解中的 source 值作为 Map 的 key
                        service -> AnnotationUtils.findAnnotation(service.getClass(), FcWeatherDataSource.class).source(),
                        // 服务实例作为 Map 的 value
                        v -> v,
                        // 处理 key 冲突，保留第一个值
                        (v1, v2) -> v1
                ));
    }

    @Override
    public <T extends BaseWeatherDO>List<T> findFcWeatherData(String cityId, Integer type, Date startDate, Date endDate,String source)
        throws Exception {
        if(cityId != null){
            cityId = cityService.findWeatherCityId(cityId);
        }
        BaseFcWeatherService baseFcWeatherService = baseFcWeatherServiceMap.get(source);
        return  baseFcWeatherService.findFcWeatherData(cityId,type,startDate,endDate);
    }

    @Override
    public <T extends BaseWeatherDO> List<T> findFcWeatherDataWithCityId(String cityId, Integer type, Date startDate, Date endDate, String source) throws Exception {
        if(cityId != null){
            if ("1".equals(cityId)){
                cityId = "1";
            }else {
                cityId = cityService.findWeatherCityId(cityId);
            }
        }
        BaseFcWeatherService baseFcWeatherService = baseFcWeatherServiceMap.get(source);
        return  baseFcWeatherService.findFcWeatherData(cityId,type,startDate,endDate);
    }
}
