package com.tsintergy.lf.serviceimpl.weather.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityStationHisBasicDO;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
public class WeatherCityStationHisBasicDAO extends BaseAbstractDAO<WeatherCityStationHisBasicDO> {

    public List<WeatherCityStationHisBasicDO> findWeatherCityFcDO(String cityId, Integer type, Date startDate, Date endDate) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if(null != startDate){
            param.getQueryConditions().put("_dnl_date",new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate){
            param.getQueryConditions().put("_dnm_date",new java.sql.Date(endDate.getTime()));
        }
        if (null != type){
            param.getQueryConditions().put("_ne_type", type);
        }
        if (null != cityId) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<WeatherCityStationHisBasicDO> weatherCityFcVOs = this.query(param).getDatas();
        return weatherCityFcVOs;
    }

}