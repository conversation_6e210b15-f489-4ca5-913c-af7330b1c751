package com.tsintergy.lf.serviceimpl.area.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.serviceapi.base.area.pojo.LoadAreaFcDO;
import com.tsintergy.lf.serviceimpl.common.util.ColumnUtil;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 *
 * <AUTHOR>
 * @version $Id: LoadAreaFcDAO.java, v 0.1 2018-01-31 10:23:36 tao Exp $$
 */

@Component
public class LoadAreaFcDAO extends BaseAbstractDAO<LoadAreaFcDO> {


    /**
     * 获取某个口径预测负荷
     *
     * @param areaId      城市ID
     * @param caliberId   口径ID
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @param algorithmId 算法ID
     * @return
     */
    public List<LoadAreaFcDO> getLoadAreaFcDOs(String areaId, String caliberId, String algorithmId, Date startDate,
                                               Date endDate) throws Exception {

        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != algorithmId) {
            param.getQueryConditions().put("_se_algorithmId", algorithmId);
        }
        if (null != areaId) {
            param.getQueryConditions().put("_se_areaId", areaId);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_se_caliberId", caliberId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<LoadAreaFcDO> loadAreaFcDOS = this.query(param).getDatas();
        return loadAreaFcDOS;
    }

    public List<LoadAreaFcDO> getLoadAreaFcDOs(List<String> areaIds, String caliberId, String algorithmId, Date startDate,
        Date endDate) throws Exception {

        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != algorithmId) {
            param.getQueryConditions().put("_se_algorithmId", algorithmId);
        }
        if (!CollectionUtils.isEmpty(areaIds)) {
            param.getQueryConditions().put("_sin_areaId", areaIds);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_se_caliberId", caliberId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<LoadAreaFcDO> loadAreaFcDOS = this.query(param).getDatas();
        return loadAreaFcDOS;
    }


    public void deleteById(LoadAreaFcDO loadAreaFc) throws Exception {
        this.remove(loadAreaFc);
    }

    /**
     * 查询预测负荷数据
     *
     * @param areaId      城市ID
     * @param caliberId   口径ID
     * @param algorithmId 算法ID
     * @param date        日期
     * @return
     */
    public LoadAreaFcDO getLoadAreaFcDO(String areaId, String caliberId, String algorithmId, Date date) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("_ne_date", new java.sql.Date(date.getTime()));
        param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        param.getQueryConditions().put("_ne_areaId", areaId);
        param.getQueryConditions().put("_ne_caliberId", caliberId);
        List<LoadAreaFcDO> loadAreaFcDOS =this.query(param).getDatas();
        if (loadAreaFcDOS != null && loadAreaFcDOS.size() > 0) {
            return loadAreaFcDOS.get(0);
        }
        return null;
    }

    /**
     * 查询上报预测负荷数据
     *
     * @param areaId    城市ID
     * @param caliberId 口径ID
     * @param date      日期
     * @return
     */
    public LoadAreaFcDO getReportLoadAreaFcDO(String areaId, String caliberId, Date date) throws Exception {
        if (areaId == null) {
            throw new BusinessException("T706","城市ID不可为空");
        }
        if (caliberId == null) {
            throw new BusinessException("T706","口径ID不可为空");
        }
        if (date == null) {
            throw new BusinessException("T706","日期不可为空");
        }
        DBQueryParam param = DBQueryParamBuilder.create().build();

        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("_ne_date", new java.sql.Date(date.getTime()));
        param.getQueryConditions().put("_ne_report", true);
        param.getQueryConditions().put("_se_areaId", areaId);
        param.getQueryConditions().put("_se_caliberId", caliberId);
        List<LoadAreaFcDO> loadAreaFcDOS = this.query(param).getDatas();
        if (loadAreaFcDOS != null && loadAreaFcDOS.size() > 0) {
            return loadAreaFcDOS.get(0);
        }
        return null;
    }

    /**
     * 获取预测上报负荷
     *
     * @param areaId    城市ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return
     */
    public List<LoadAreaFcDO> getReportLoadAreaFcDOs(String areaId, Date startDate, Date endDate, String caliberId,
                                                     String algorithmId) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();

        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != algorithmId) {
            param.getQueryConditions().put("_se_algorithmId", algorithmId);
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != areaId) {
            param.getQueryConditions().put("_se_areaId", areaId);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        param.getQueryConditions().put("_ne_report", true);
        param.setOrderby("date");
        param.setDesc("0");
        List<LoadAreaFcDO> loadAreaFcDOS = this.query(param).getDatas();
        return loadAreaFcDOS;
    }
    /**
     * 获取预测上报负荷
     *
     * @param areaIds    城市ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return
     */
    public List<LoadAreaFcDO> getReportLoadAreaFcDOs(List<String> areaIds, Date startDate, Date endDate) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();

        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != areaIds) {
            param.getQueryConditions().put("_sin_areaId", areaIds);
        }
        param.getQueryConditions().put("_ne_report", true);
        param.setOrderby("date");
        param.setDesc("0");
        List<LoadAreaFcDO> loadAreaFcDOS = this.query(param).getDatas();
        return loadAreaFcDOS;
    }

    /**
     * 保存或更新
     *
     * @param loadAreaFcDO
     * @return
     */
    public LoadAreaFcDO doSaveOrUpdateLoadAreaFcDO(LoadAreaFcDO loadAreaFcDO) throws Exception {
        LoadAreaFcDO oldVO = this.getLoadAreaFcDO(loadAreaFcDO.getAreaId(), loadAreaFcDO.getCaliberId(),
                loadAreaFcDO.getAlgorithmId(), loadAreaFcDO.getDate());
        if (oldVO != null) {
            String id = oldVO.getId();
            ColumnUtil.copyPropertiesIgnoreNull(loadAreaFcDO, oldVO);
            oldVO.setId(id);
            oldVO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            this.getSession().flush();
            this.getSession().clear();
            return (LoadAreaFcDO) this.updateAndFlush(oldVO);
        } else {
            return (LoadAreaFcDO) this.createAndFlush(loadAreaFcDO);
        }
    }


    public LoadAreaFcDO getRecommendLoadAreaFcDO(String areaId, String caliberId, Date date) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != areaId) {
            param.getQueryConditions().put("_ne_areaId", areaId);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        if (null != date) {
            param.getQueryConditions().put("_de_date", new java.sql.Date(date.getTime()));
        }
        param.getQueryConditions().put("_ne_recommend", true);
        param.setOrderby("date");
        param.setDesc("0");
        List<LoadAreaFcDO> loadAreaFcDOS = this.query(param).getDatas();
        if (!CollectionUtils.isEmpty(loadAreaFcDOS)) {
            return loadAreaFcDOS.get(0);
        }
        return null;
    }


    public LoadAreaFcDO getReportLoadAreaFcDO(Date date, String areaId, String caliberId, String algorithmId, Boolean report) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();

        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (date != null) {
            param.getQueryConditions().put("_ne_date", new java.sql.Date(date.getTime()));
        }
        if (StringUtils.isNotBlank(algorithmId)) {
            param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        }
        if (StringUtils.isNotBlank(areaId)) {
            param.getQueryConditions().put("_ne_areaId", areaId);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        if (report != null) {
            param.getQueryConditions().put("_ne_report", report);
        }
        List<LoadAreaFcDO> loadAreaFcDOS = this.query(param).getDatas();
        if (loadAreaFcDOS != null && loadAreaFcDOS.size() > 0) {
            return loadAreaFcDOS.get(0);
        }
        return null;
    }


    public List<LoadAreaFcDO> getReportFc(Date startDate, Date endDate, String areaId, String caliberId, String algorithmId) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();

        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (StringUtils.isNotBlank(algorithmId)) {
            param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        }
        if (StringUtils.isNotBlank(areaId)) {
            param.getQueryConditions().put("_ne_areaId", areaId);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        param.getQueryConditions().put("_ne_report", true);
        List<LoadAreaFcDO> loadAreaFcDOS = this.query(param).getDatas();
        return loadAreaFcDOS;
    }


    public List<LoadAreaFcDO> getLoadAreaFcDOInAlgorithmId(String areaId, String caliberId, List<String> algorithmIds, Date startDate,
                                                           Date endDate) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();

        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != algorithmIds) {
            param.getQueryConditions().put("_sin_algorithmId", algorithmIds);
        }
        if (null != areaId) {
            param.getQueryConditions().put("_se_areaId", areaId);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_se_caliberId", caliberId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<LoadAreaFcDO> loadAreaFcDOS = this.query(param).getDatas();
        return loadAreaFcDOS;
    }

    public List<LoadAreaFcDO> findLoadAreaFcReportByUserId(String areaId, String caliberId, String userId, Date startDate, Date endDate) {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != userId) {
            param.getQueryConditions().put("_se_userId", userId);
        }
        if (null != areaId) {
            param.getQueryConditions().put("_se_areaId", areaId);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_se_caliberId", caliberId);
        }
        param.getQueryConditions().put("_ne_report", true);
        param.setOrderby("date");
        param.setDesc("0");
        param.setOrderby("date");
        param.setDesc("0");
        List<LoadAreaFcDO> loadAreaFcDOS = this.query(param).getDatas();
        return loadAreaFcDOS;
    }
}