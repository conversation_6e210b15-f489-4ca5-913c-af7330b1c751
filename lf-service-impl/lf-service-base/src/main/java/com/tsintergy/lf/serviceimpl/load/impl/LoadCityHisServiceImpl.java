
package com.tsintergy.lf.serviceimpl.load.impl;


import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.area.api.BaseAreaService;
import com.tsintergy.lf.serviceapi.base.area.api.BasePlanService;
import com.tsintergy.lf.serviceapi.base.area.pojo.BaseAreaDO;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisClctService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.dto.AreaLoadRateDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.CityLoadDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.CityValueDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.CurveViewDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.Date24LoadDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadCommonDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadHisDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadHisDataDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadQueryDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHis288DO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.power.api.SmallPowerLoadFcService;
import com.tsintergy.lf.serviceapi.base.power.api.SmallPowerLoadHisService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceimpl.common.constants.LoadCalUtil;
import com.tsintergy.lf.serviceimpl.common.util.ColumnUtil;
import com.tsintergy.lf.serviceimpl.forecast.dao.LoadCityFcDAO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadCityHis288DAO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadCityHisDAO;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @version $Id: LoadCityHisServiceImpl.java, v 0.1 2018-01-31 10:50:38 tao Exp $$
 */

@Service
public class LoadCityHisServiceImpl extends BaseServiceImpl implements LoadCityHisService {

    private static final Logger logger = LogManager.getLogger(LoadCityHisServiceImpl.class);

    //调度
    private static final String DIAO_DU = "1";

    //统调
    private static final String TONG_DIAO = "2";

    //地调
    private static final String DI_DIAO = "3";

    //网供
    private static final String WANG_GONG = "4";


    @Autowired
    private SettingSystemService settingSystemService;

    @Autowired
    private CityService cityService;

    @Autowired
    private LoadCityHis288DAO loadCityHis288DAO;


    @Autowired
    LoadCityHisClctService loadCityHisClctService;

    @Autowired
    LoadCityFcService loadCityFcService;

    @Autowired
    AlgorithmService algorithmService;

    @Autowired
    CaliberService caliberService;

    @Autowired
    LoadCityHisDAO loadCityHisDAO;

    @Autowired
    private LoadCityFcDAO loadCityFcDAO;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private WeatherCityFcService weatherCityFcService;

    @Autowired
    private BasePlanService basePlanService;

    @Autowired
    private BaseAreaService baseAreaService;

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private SmallPowerLoadHisService smallPowerLoadHisService;

    @Autowired
    private SmallPowerLoadFcService smallPowerLoadFcService;

    @Override
    public DataPackage queryLoadCityHisDO(DBQueryParam param) throws Exception {
        try {
            return loadCityHisDAO.query(param);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("T706", e.getMessage());
        }
    }

    @Override
    public LoadCityHisDO doCreate(LoadCityHisDO vo) throws Exception {
        try {
            return (LoadCityHisDO) loadCityHisDAO.create(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("T706", e.getMessage());
        }
    }

    @Override
    public void doRemoveLoadCityHisDO(LoadCityHisDO vo) throws Exception {
        try {
            loadCityHisDAO.remove(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("T706", e.getMessage());
        }
    }

    @Override
    public void doRemoveLoadCityHisDOByPK(Serializable pk) throws Exception {
        try {
            loadCityHisDAO.removeByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("T706", e.getMessage());
        }
    }

    @Override
    public LoadCityHisDO doUpdateLoadCityHisDO(LoadCityHisDO vo) throws Exception {
        try {
            return (LoadCityHisDO) loadCityHisDAO.update(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("T706", e.getMessage());
        }
    }

    @Override
    public LoadCityHisDO findLoadCityHisDOByPk(Serializable pk) throws Exception {
        try {
            return (LoadCityHisDO) loadCityHisDAO.findByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("T706", e.getMessage());
        }
    }

    @Override
    public List<CityValueDTO> find24LoadCityByDateAndCityIds(Date date, List<String> cityIds, String caliberId)
        throws Exception {
        List<CityValueDTO> cityValueDTOS = new ArrayList<CityValueDTO>();
        try {
            List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.getLoadCityHisDOS(cityIds, date, date, caliberId);
            if (LoadCityHisDOS == null || LoadCityHisDOS.size() < 1) {
                throw TsieExceptionUtils.newBusinessException("T706");
            } else {
                for (LoadCityHisDO LoadCityHisDO : LoadCityHisDOS) {
                    CityValueDTO cityValueDTO = new CityValueDTO();
                    cityValueDTO.setCityId(LoadCityHisDO.getCityId());
                    cityValueDTO.setCity(cityService.findCityById(LoadCityHisDO.getCityId()).getCity());
                    cityValueDTO.setValue(
                        BasePeriodUtils.toList(LoadCityHisDO, 24, Constants.LOAD_CURVE_START_WITH_ZERO)); // 转24点负荷数据
                    cityValueDTOS.add(cityValueDTO);
                }
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("T706", e);
        }
        return cityValueDTOS;

    }

    @Override
    public List<BigDecimal> findLoadCityHisDO(Date date, String cityId, String caliberId) throws Exception {
        List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.getLoadCityHisDO(cityId, date, date, caliberId);
        if (LoadCityHisDOS.size() < 1) {
            return new ArrayList<>();
        }
        return BasePeriodUtils
            .toList(LoadCityHisDOS.get(0), Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
    }

    @Override
    public List<BigDecimal> findLoadCityHis288DO(Date date, String cityId, String caliberId) throws Exception {
        List<LoadCityHis288DO> LoadCityHisDOS = loadCityHis288DAO.getLoadCityHisDO(cityId, date, date, caliberId);
        if (LoadCityHisDOS.size() < 1) {
            return new ArrayList<>();
        }
        return BasePeriodUtils.toList(LoadCityHisDOS.get(0), 288, Constants.LOAD_CURVE_START_WITH_ZERO);

    }

    @Override
    public List<BigDecimal> findLoadCityHisDO(String cityId, Date startDate, Date endDate, String caliberId)
        throws Exception {
        List<LoadCityHisDO> powerLoadHisCityClctDOS = null;
        try {
            powerLoadHisCityClctDOS = this.getLoadCityHisDOS(cityId, caliberId, startDate, endDate);
        } catch (Exception e) {
            logger.error("历史出力查询异常...", e);
            throw TsieExceptionUtils.newBusinessException("01C20180003");
        }
        List<BigDecimal> result = new ArrayList<>();
        List nullList = new ArrayList() {
            {
                for (int i = 0; i < 96; i++) {
                    add(null);
                }
            }
        };
        if (!CollectionUtils.isEmpty(powerLoadHisCityClctDOS) && powerLoadHisCityClctDOS.size() > 0) {
            Map<Date, LoadCityHisDO> mapData = powerLoadHisCityClctDOS.stream()
                .collect(Collectors.toMap(LoadCityHisDO::getDate, Function.identity(), (o, n) -> n));
            List<Date> listDate = DateUtil.getListBetweenDay(startDate, endDate);
            for (Date date : listDate) {
                LoadCityHisDO hisCityClctDO = mapData.get(date);
                if (hisCityClctDO == null) {
                    result.addAll(nullList);
                } else {
                    result.addAll(BasePeriodUtils
                        .toList(hisCityClctDO, Constants.LOAD_CURVE_POINT_NUM,
                            Constants.LOAD_CURVE_START_WITH_ZERO));
                }
            }
        }
        return result;
    }

    @Override
    public List<LoadCityHisDO> find24LoadCityHisDO(Date date, String cityId, String caliberId) throws Exception {
        List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.getLoadCityHisDO(cityId, date, date, caliberId);
        return LoadCityHisDOS;
    }


    @Override
    public List<LoadHisDataDTO> findLoadCityVOsByCityId(Date startDate, Date endDate, String cityId, String caliberId)
        throws Exception {
        List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.getLoadCityHisDO(cityId, startDate, endDate, caliberId);
        if (LoadCityHisDOS.size() < 1) {
            return new ArrayList<>();
        }
        List<LoadHisDataDTO> loadCommonDTOS = new ArrayList<LoadHisDataDTO>(10);
        for (LoadCityHisDO LoadCityHisDO : LoadCityHisDOS) {
            LoadHisDataDTO loadCommonDTO = new LoadHisDataDTO();
            loadCommonDTO.setId(LoadCityHisDO.getId());
            loadCommonDTO.setDate(LoadCityHisDO.getDate());
            List<BigDecimal> bigDecimals = BasePeriodUtils
                .toList(LoadCityHisDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
            List<BigDecimal> collect = new ArrayList<>();
            if (bigDecimals != null) {
                collect = bigDecimals.stream().map(e -> {
                    if (e != null) {
                        return e.setScale(0,BigDecimal.ROUND_HALF_UP);
                    }
                    return e;
                })
                    .collect(Collectors.toList());
            }
            loadCommonDTO.setData(collect);
            loadCommonDTO.setWeek(DateUtil.getWeek(LoadCityHisDO.getDate()));
            loadCommonDTO.setCity(cityService.findCityById(LoadCityHisDO.getCityId()).getCity());
            loadCommonDTOS.add(loadCommonDTO);
        }
        return loadCommonDTOS;
    }

    @Override
    public List<LoadCommonDTO<Date>> findLoadCityVOsByCityIdAndDates(String cityId, List<Date> dates, String caliberId)
        throws Exception {
        List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.getLoadCityHisDOs(cityId, dates, caliberId);
        if (LoadCityHisDOS.size() < 1) {
            throw TsieExceptionUtils.newBusinessException("T706", "");
        }
        List<LoadCommonDTO<Date>> loadCommonDTOS = new ArrayList<LoadCommonDTO<Date>>(10);
        for (LoadCityHisDO LoadCityHisDO : LoadCityHisDOS) {
            LoadCommonDTO<Date> loadCommonDTO = new LoadCommonDTO<Date>();
            loadCommonDTO.setName(LoadCityHisDO.getDate());
            loadCommonDTO.setData(BasePeriodUtils
                .toList(LoadCityHisDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            loadCommonDTOS.add(loadCommonDTO);
        }
        return loadCommonDTOS;
    }

    @Override
    public List<LoadCityHisDO> findLoadCityDOsByCityIdInDates(String cityId, List<Date> dates, String caliberId)
        throws Exception {
        List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.getLoadCityHisDOs(cityId, dates, caliberId);
        if (LoadCityHisDOS.size() < 1) {
            throw TsieExceptionUtils.newBusinessException("01C20180002");
        } else {
            return LoadCityHisDOS;
        }
    }


    @Override
    public CityValueDTO find24LoadCityBetweenDate(String cityId, Date startDate, Date endDate, String caliberId)
        throws Exception {
        try {
            List<LoadCityHisDO> LoadCityHisDOS = loadCityHisDAO.getLoadCityHisDO(cityId, startDate, endDate, caliberId);
            if (LoadCityHisDOS.size() < 1) {
                throw TsieExceptionUtils.newBusinessException("T706", "");
            } else {
                CityValueDTO cityValueDTO = new CityValueDTO();
                cityValueDTO.setCityId(LoadCityHisDOS.get(0).getCityId());
                cityValueDTO.setCity(cityService.findCityById(cityValueDTO.getCityId()).getCity());
                List<BigDecimal> values = new ArrayList<BigDecimal>();
                for (LoadCityHisDO LoadCityHisDO : LoadCityHisDOS) {
                    values.addAll(BasePeriodUtils.toList(LoadCityHisDO, 24, Constants.LOAD_CURVE_START_WITH_ZERO));
                }
                cityValueDTO.setValue(values);
                return cityValueDTO;
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("01C20180003", e);
        }
    }

    @Override
    public List<LoadCityHisDO> doUpdateLoadCityHisDO(List<LoadHisDTO> loadHisDTOS, String caliberId) throws Exception {
        List<LoadCityHisDO> LoadCityHisDOS = new ArrayList<LoadCityHisDO>();
        for (LoadHisDTO loadHisDTO : loadHisDTOS) {
            LoadCityHisDO LoadCityHisDO = new LoadCityHisDO();
            LoadCityHisDO.setId(loadHisDTO.getId());
            LoadCityHisDO.setCityId(loadHisDTO.getCityId());
            LoadCityHisDO.setCaliberId(caliberId);
            LoadCityHisDO.setDate(new java.sql.Date(loadHisDTO.getName().getTime()));
//            LoadCityHisDO.setAllFied(loadHisDTO.getData());
            BasePeriodUtils.setAllFiled(LoadCityHisDO,
                ColumnUtil.listToMap(loadHisDTO.getData(), Constants.LOAD_CURVE_START_WITH_ZERO));
            LoadCityHisDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            LoadCityHisDOS.add(this.doUpdateLoadCityHisDO(LoadCityHisDO));
        }
        return LoadCityHisDOS;
    }

    @Override
    public List<LoadCityHisDO> doUpdateLoadCityHisDOFromExcel(List<List<List<String>>> dataList)
        throws BusinessException {
        List<LoadCityHisDO> LoadCityHisDOS = new ArrayList<LoadCityHisDO>();
        try {
            List<List<String>> rows = dataList.get(0);
            for (int i = 1; i < rows.size(); i++) {
                LoadCityHisDO excelLoadCityHisDO = new LoadCityHisDO();
                excelLoadCityHisDO.setCityId(cityService.findCityByName(rows.get(i).get(0)).getId());
                excelLoadCityHisDO.setDate(java.sql.Date.valueOf(rows.get(i).get(1)));
                CaliberDO caliberVO = caliberService.findCaliberDOByName(rows.get(i).get(2));
                if (null != caliberVO) {
                    excelLoadCityHisDO.setCaliberId(caliberVO.getId());
                } else {
                    throw TsieExceptionUtils.newBusinessException("01C20180008", "");
                }
                LoadCityHisDO LoadCityHisDO = loadCityHisDAO
                    .getLoadCityHisDO(excelLoadCityHisDO.getCityId(), excelLoadCityHisDO.getDate(),
                        excelLoadCityHisDO.getCaliberId());
                if (LoadCityHisDO == null) {
                    LoadCityHisDO = new LoadCityHisDO();
                    LoadCityHisDO.setCityId(excelLoadCityHisDO.getCityId());
                    LoadCityHisDO.setCaliberId(excelLoadCityHisDO.getCaliberId());
                    LoadCityHisDO.setDate(excelLoadCityHisDO.getDate());
                }

                List<BigDecimal> list = new ArrayList<BigDecimal>();
                for (String data : rows.get(i).subList(4, rows.get(i).size())) {
                    list.add(new BigDecimal(data));
                }
                BasePeriodUtils
                    .setAllFiled(LoadCityHisDO, ColumnUtil.listToMap(list, Constants.LOAD_CURVE_START_WITH_ZERO));

                if (StringUtils.isEmpty(LoadCityHisDO.getId())) {// 插入
                    LoadCityHisDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-", ""));
                    LoadCityHisDOS.add(this.doCreate(LoadCityHisDO));
                } else {
                    LoadCityHisDOS.add(this.doUpdateLoadCityHisDO(LoadCityHisDO));
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
            throw TsieExceptionUtils.newBusinessException("T706", e.getMessage());
        }
        return LoadCityHisDOS;
    }


    public Workbook getWorkbok(InputStream in, File file) throws IOException {
        Workbook wb = null;
        if (file.getName().endsWith("xlsx")) {
            wb = new XSSFWorkbook(in);
        }
        return wb;
    }

    private Object getValue(Cell cell) {
        Object obj = null;
        switch (cell.getCellTypeEnum()) {
            case BOOLEAN:
                obj = cell.getBooleanCellValue();
                break;
            case ERROR:
                obj = cell.getErrorCellValue();
                break;
            case NUMERIC:
                obj = cell.getNumericCellValue();
                break;
            case STRING:
                obj = cell.getStringCellValue();
                break;
            default:
                break;
        }
        return obj;
    }

    /**
     * 功能描述: <br>
     * <p>
     * 获取文件夹下所有的文件名
     *
     * @return:
     * @since: 1.0.0
     * @Author:wangchen
     * @Date: 2018/9/18 9:03
     */
    public ArrayList<String> getFiles(String path) {
        ArrayList<String> files = new ArrayList<String>();
        File file = new File(path);
        File[] tempList = file.listFiles();

        for (int i = 0; i < tempList.length; i++) {
            if (tempList[i].isFile()) {
                files.add(tempList[i].toString());
                System.out.println(tempList[i].toString());
            }
        }
        return files;
    }


    @Override
    public List<LoadCityHisDO> getLoadCityHisDOS(String cityId, String caliberId, Date startDate, Date endDate)
        throws Exception {
        return loadCityHisDAO.getLoadCityHisDO(cityId, startDate, endDate, caliberId);
    }

    @Override
    public void doInsertOrUpdate(LoadCityHisDO LoadCityHisDO) throws Exception {
        LoadCityHisDO LoadCityHisDOTemp = loadCityHisDAO
            .getLoadCityHisDO(LoadCityHisDO.getCityId(), LoadCityHisDO.getDate(), LoadCityHisDO.getCaliberId());
        if (LoadCityHisDOTemp == null) {
            loadCityHisDAO.createAndFlush(LoadCityHisDO);
            return;
        }
        loadCityHisDAO.getSession().flush();
        loadCityHisDAO.getSession().clear();
        LoadCityHisDO.setId(LoadCityHisDOTemp.getId());
        LoadCityHisDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
        loadCityHisDAO.updateAndFlush(LoadCityHisDO);
    }


    /**
     * 预测查询--负荷预测
     */
    @Override
    public LoadQueryDTO findLoad(String cityId, String caliberId, Date startDate, Date endDate, String algorithmId)
        throws Exception {
        //真实数据
        List<LoadCityHisDO> loadCityHisDOS = loadCityHisDAO.getLoadCityHisDO(cityId, startDate, endDate, caliberId);
        //查询上报数据
        List<LoadCityFcDO> loadCityFcDOS = loadCityFcService.findReportLoadFc(cityId, caliberId, startDate, endDate);
        //查询算法的数据
        List<LoadCityFcDO> fcVOS = loadCityFcService
            .findFcByAlgorithmId(cityId, caliberId, algorithmId, startDate, endDate);

        Map<Date, List<BigDecimal>> fullHisPowerMap = smallPowerLoadHisService.findFullRepairHisPowerList(cityId, startDate, endDate);

        //系统实际负荷
        List<LoadCityHisDO> sysHisLoad = loadCityHisDAO.getLoadCityHisDO(cityId, startDate, endDate, Constants.SYSTEM_LOAD_CALIBER);

        //系统预测负荷
        List<LoadCityFcDO> sysFcLoad = loadCityFcService
                .findFcByAlgorithmId(cityId, Constants.SYSTEM_LOAD_CALIBER, AlgorithmEnum.DP_FORECAST_2.getId(), startDate, endDate);

        List<LoadCityFcDO> sysFcLoadReport = loadCityFcService.findReportLoadFc(cityId,  Constants.SYSTEM_LOAD_CALIBER, startDate, endDate);

        if (loadCityHisDOS.isEmpty() && loadCityFcDOS.isEmpty() && fcVOS.isEmpty()
                && CollectionUtils.isEmpty(sysHisLoad) && CollectionUtils.isEmpty(sysFcLoad)) {
            return null;
        }



        //分别转map
        Map<Date, LoadCityHisDO> realMap = loadCityHisDOS.stream()
            .collect(Collectors.toMap(LoadCityHisDO::getDate, Function.identity(), (key1, key2) -> key2));
        Map<Date, LoadCityFcDO> reportMap = loadCityFcDOS.stream()
            .collect(Collectors.toMap(LoadCityFcDO::getDate, Function.identity(), (key1, key2) -> key2));
        Map<Date, LoadCityFcDO> fcMap = fcVOS.stream()
            .collect(Collectors.toMap(LoadCityFcDO::getDate, Function.identity(), (key1, key2) -> key2));

        Map<Date, LoadCityFcDO> fcSysMap = sysFcLoad.stream()
                .collect(Collectors.toMap(LoadCityFcDO::getDate, Function.identity(), (key1, key2) -> key2));

        Map<Date, LoadCityHisDO> hisSysMap = sysHisLoad.stream()
                .collect(Collectors.toMap(LoadCityHisDO::getDate, Function.identity(), (key1, key2) -> key2));

        Map<Date, LoadCityFcDO> sysFcLoadReportMap = sysFcLoadReport.stream()
                .collect(Collectors.toMap(LoadCityFcDO::getDate, Function.identity(), (key1, key2) -> key2));

        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        List<BigDecimal> real = new ArrayList<>();
        List<BigDecimal> fc = new ArrayList<>();
        List<BigDecimal> report = new ArrayList<>();
        List<BigDecimal> sysHisLoadResult = new ArrayList<>();
        //全负荷预测曲线
        List<BigDecimal> sysFcLoadResult = new ArrayList<>();

        List<BigDecimal> sysReportLoadResult = new ArrayList<>();

        List<BigDecimal> nullList = LoadCalUtil.getNullList(Constants.LOAD_CURVE_POINT_NUM);
        LoadQueryDTO queryDTO = new LoadQueryDTO();
        for (Date date : dateList) {
            LoadCityHisDO hisVO = realMap.get(date);
            if (hisVO != null) {
                real.addAll(BasePeriodUtils
                    .toList(hisVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            } else {
                real.addAll(nullList);
            }
            LoadCityFcDO reportVo = reportMap.get(date);
            report.addAll(getList(reportVo));
            LoadCityFcDO fcVO = fcMap.get(date);
            fc.addAll(getList(fcVO));

            //预测系统负荷
            LoadCityFcDO sysFcVO = fcSysMap.get(date);
            if (Objects.nonNull(sysFcVO)){
                sysFcLoadResult.addAll(BasePeriodUtils
                        .toList(sysFcVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            }else{
                sysFcLoadResult.addAll(nullList);
            }

            //最终上报预测系统负荷
            LoadCityFcDO sysReportFcVO = sysFcLoadReportMap.get(date);
            if (Objects.nonNull(sysReportFcVO)){
                sysReportLoadResult.addAll(BasePeriodUtils
                        .toList(sysReportFcVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            }else {
                sysReportLoadResult.addAll(nullList);

            }

            //实际系统负荷
            LoadCityHisDO sysHisVO = hisSysMap.get(date);
            if (Objects.nonNull(sysHisVO)){
                sysHisLoadResult.addAll(BasePeriodUtils
                        .toList(sysHisVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            }else{
                sysHisLoadResult.addAll(nullList);
            }
        }
        queryDTO.setFc(fc);
        queryDTO.setReal(real);
        queryDTO.setReport(report);
        queryDTO.setAlgorithmName(algorithmService.findAlgorithmVOByPk(algorithmId).getAlgorithmCn());

//        queryDTO.setHisSysLoad(sysHisLoadResult);
//        queryDTO.setFcSysLoad(sysFcLoadResult);
//        queryDTO.setReportSysLoad(sysReportLoadResult);
        return queryDTO;
    }

    private static void fullSum(List<BigDecimal> real, List<BigDecimal> full) {
        for (int i = 0; i < real.size(); i++) {
            BigDecimal hisLoad = real.get(i);
            if (Objects.nonNull(hisLoad)){
                BigDecimal fullLoad = full.get(i);
                if (Objects.nonNull(fullLoad)){
                    full.set(i,fullLoad.add(hisLoad));
                }
            }
        }
    }

    @Override
    public List<CityLoadDTO> find24CityLoadDTO(Date today, List<String> cityIds, String caliberId) throws Exception {
        Map<String, LoadCityHisDO> todayHisLoadsMap = getLoadCityHisDOMap(today, cityIds, caliberId);
        Map<String, LoadCityHisDO> yesterdayLoadsMap = getLoadCityHisDOMap(DateUtils.addDays(today, -1), cityIds,
            caliberId);
        Map<String, LoadCityFcDO> todayFcMap = getLoadCityFcDOMap(today, cityIds, caliberId);
        Map<String, WeatherCityHisDO> todayTemperaturesHisVOMap = getWeatherCityVOMap(today, cityIds);
        Map<String, String> cityMap = cityService.findAllCitys().stream()
            .collect(Collectors.toMap(CityDO::getId, CityDO::getCity));
        List<CityLoadDTO> cityLoadDTOS = new ArrayList<>();
        for (String cityId : cityIds) {
            CityLoadDTO cityLoadDTO = new CityLoadDTO();
            cityLoadDTO.setCityName(cityMap.get(cityId));
            setTodayHisLoads(todayHisLoadsMap, cityId, cityLoadDTO);
            setYesterdayHisLoads(yesterdayLoadsMap, cityId, cityLoadDTO);
            setTodayFcLoads(todayFcMap, cityId, cityLoadDTO, today, caliberId);
            setTodayTemperatures(todayTemperaturesHisVOMap, cityId, cityLoadDTO);
            cityLoadDTOS.add(cityLoadDTO);
        }
        return cityLoadDTOS;
    }

    @Override
    public List<AreaLoadRateDTO> find24AreaLoadRateDTO(Date date, String caliberId) throws Exception {
        Map<String, String> cityMap = cityService.findAllCitys().stream()
            .filter(cityVO -> StringUtils.isNotBlank(cityVO.getArea()))
            .collect(Collectors.toMap(CityDO::getId, CityDO::getArea));
        List<LoadCityHisDO> LoadCityHisDOS = this.findLoadCityHisDO(date, caliberId);
        if (!CollectionUtils.isEmpty(LoadCityHisDOS)) {
            Map<String, List<BigDecimal>> areaLoadsMap = new HashMap<>();
            List<BigDecimal> provinceLoads = null;
            for (LoadCityHisDO LoadCityHisDO : LoadCityHisDOS) {
                if (LoadCityHisDO.getCityId().equals(Constants.PROVINCE_ID)) {
                    provinceLoads = BasePeriodUtils
                        .toList(LoadCityHisDO, Constants.LOAD_CURVE_POINT_NUM_24, Constants.LOAD_CURVE_START_WITH_ZERO);
                    continue;
                }
                CalcAreaLoads(cityMap, areaLoadsMap, LoadCityHisDO);
            }

            if (!areaLoadsMap.isEmpty()) {
                List<AreaLoadRateDTO> areaLoadRateDTOS = new ArrayList<>();
                Set<String> areaNames = areaLoadsMap.keySet();
                Iterator<String> iterator = areaNames.iterator();
                while (iterator.hasNext()) {
                    String areaName = iterator.next();
                    List<BigDecimal> areaLoads = areaLoadsMap.get(areaName);
                    AreaLoadRateDTO areaLoadRateDTO = new AreaLoadRateDTO();
                    areaLoadRateDTO.setAreaName(areaName);
                    List<BigDecimal> areaLoadsRate = com.tsintergy.lf.serviceimpl.common.util.BasePeriodUtils
                        .listDivide(areaLoads, provinceLoads);
                    areaLoadRateDTO.setLoadRates(areaLoadsRate);
                    areaLoadRateDTOS.add(areaLoadRateDTO);
                }
                return areaLoadRateDTOS;
            }
        }

        return null;
    }

    @Override
    public Date24LoadDTO findDate24LoadDTOS(Date date, String cityId, String caliberId) throws Exception {
        LoadCityHisDO LoadCityHisDO = null;
        List<LoadCityHisDO> LoadCityHisDOs = loadCityHisDAO.getLoadCityHisDO(cityId, date, date, caliberId);
        if (!CollectionUtils.isEmpty(LoadCityHisDOs)) {
            LoadCityHisDO = LoadCityHisDOs.get(0);
        }
        LoadCityFcDO LoadCityFcDOS = loadCityFcDAO.getReportLoadCityFcDO(cityId, caliberId, date);
        Date24LoadDTO date24LoadDTO = new Date24LoadDTO();
        date24LoadDTO.setCityId(cityId);
        if (LoadCityFcDOS != null) {
            date24LoadDTO.setFcLoads(BasePeriodUtils
                .toList(LoadCityFcDOS, Constants.LOAD_CURVE_POINT_NUM_24, Constants.LOAD_CURVE_START_WITH_ZERO));
        }
        if (LoadCityHisDO != null) {
            date24LoadDTO.setHisLoads(BasePeriodUtils
                .toList(LoadCityHisDO, Constants.LOAD_CURVE_POINT_NUM_24, Constants.LOAD_CURVE_START_WITH_ZERO));
        }
        return date24LoadDTO;
    }

    @Override
    public LoadCityHisDO findLoadOfBaseDay(Date forecastDay, String caliberId, String cityId) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create()
            .where(QueryOp.DateNoMoreThan, "date", new java.sql.Date(forecastDay.getTime()))
            .where(QueryOp.StringEqualTo, "caliberId", caliberId)
            .where(QueryOp.StringEqualTo, "cityId", cityId)
            .addOrderByDesc("date");

        return (LoadCityHisDO) getDAO(LoadCityHisDAO.class).query(dbQueryParamBuilder.build()).getDatas().get(0);
    }

    @Override
    public void doStatAllAreaHisLoad(Date startDate, Date endDate, String caliberId) throws Exception {
        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);
        // 获取分区信息
        List<String> defaultAreaIds = basePlanService.getDefaultAreaIds();
        //获取粤东粤西区域id
        List<String> eastOrWestAreaIds = basePlanService.getEastOrWestAreaIds();

        List<String> allAreaIds = new ArrayList<>();
        allAreaIds.addAll(defaultAreaIds);
        allAreaIds.addAll(eastOrWestAreaIds);
        // 获取分区城市信息
        List<BaseAreaDO> defaultCityIds = baseAreaService.getDefaultCityIds(allAreaIds);
        // 获取实际负荷数据
        Map<String, LoadCityHisDO> collect = loadCityHisDAO.getLoadCityHisDO("", startDate, endDate, "").stream()
            .filter(t -> !"1".equals(t.getCityId()))
            .collect(Collectors.toMap(t -> {
                return t.getCityId() +"-"+t.getCaliberId() +"-" + DateUtil.getDateToStrFORMAT(t.getDate(), "yyyy-MM-dd");
            }, t -> t));
        for (Date date : listBetweenDay) {
            List<List<BigDecimal>> loadCityAreaHisValueSum = new ArrayList<>();
            for (BaseAreaDO defaultCityId : defaultCityIds) {
                List<List<BigDecimal>> loadCityHisValueSum = new ArrayList<>();
                String cityIds = defaultCityId.getCityIds();
                String[] split = cityIds.split(",");
                for (String s : split) {
                    LoadCityHisDO loadCityHisDO = collect.get(s + "-"+ caliberId + "-" + DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd"));
                    if (!Objects.isNull(loadCityHisDO)) {
                        loadCityHisValueSum.add(BasePeriodUtils.toList(loadCityHisDO, Constants.LOAD_CURVE_POINT_NUM,
                            Constants.LOAD_CURVE_START_WITH_ZERO));
                    }
                }
                if (!CollectionUtils.isEmpty(loadCityHisValueSum)) {
                    // List<BigDecimal> zeroList = com.tsintergy.lf.core.util.ColumnUtil.getZeroOrNullList(96, null);
                    List<BigDecimal> bigDecimals1 = null;
                    for (List<BigDecimal> bigDecimals : loadCityHisValueSum) {
                        bigDecimals1 = com.tsintergy.lf.serviceimpl.common.util.BasePeriodUtils.listNotNullAdd(
                            bigDecimals1, bigDecimals);
                        // zeroList = bigDecimals1;
                    }
                    loadCityAreaHisValueSum.add(bigDecimals1);
                }
            }
            for (int i = 0; i < defaultCityIds.size(); i++) {
                LoadCityHisDO loadCityHisDO = new LoadCityHisDO();
                loadCityHisDO.setDate(new java.sql.Date(date.getTime()));
                loadCityHisDO.setCityId(defaultCityIds.get(i).getId());
                loadCityHisDO.setCaliberId("1");
                loadCityHisDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                if (!CollectionUtils.isEmpty(loadCityAreaHisValueSum)) {
                    BasePeriodUtils.setAllFiled(loadCityHisDO,
                        ColumnUtil.listToMap(loadCityAreaHisValueSum.get(i), Constants.LOAD_CURVE_START_WITH_ZERO));
                }
                this.doInsertOrUpdate(loadCityHisDO);
            }
        }
    }

    @Override
    public List<LoadCityHisDO> findLoadByCityIds(Date startDate, Date endDate, String caliberId, List<String> cityIds)
        throws Exception {
       return  loadCityHisDAO.getLoadCityHisDOS(cityIds, startDate, endDate, caliberId);
    }

    @Override
    public Date24LoadDTO getLoad(String cityId, String caliberId, Date date) throws Exception {
        LoadCityFcDO report = loadCityFcService.getReport(cityId, caliberId, date);
        LoadCityHisDO loadCityHisDO = loadCityHisDAO.getLoadCityHisDO(cityId, date, caliberId);

        Date24LoadDTO result = new Date24LoadDTO();
        result.setCityId(cityId);
        if (Objects.nonNull(report)){
            result.setFcLoads(BasePeriodUtils.toList(report, Constants.LOAD_CURVE_POINT_NUM,Constants.LOAD_CURVE_START_WITH_ZERO));
        }
        if (Objects.nonNull(loadCityHisDO)){
            result.setHisLoads(loadCityHisDO.getloadList());
        }
        return result;
    }

    @Override
    public void doCreatBatch(List<LoadCityHisDO> list) throws Exception {
        for (LoadCityHisDO loadCityHisDO : list) {
            this.doCreate(loadCityHisDO);
        }
    }

    @Override
    public LoadCityHisDO getLoadCityHisDO(String cityId, String caliberId, Date date) throws Exception {
        return loadCityHisDAO.getLoadCityHisDOByOneDate(cityId, date, caliberId);
    }

    @Override
    public List<CurveViewDTO> findCurveViewDTOS(Date date, String caliberId, String cityId) {

        List<CurveViewDTO> result = new ArrayList<>();
        try {
            LoadCityFcDO loadCityFcValue = loadCityFcService.find96LoadCityFcValue(date, caliberId, cityId);
            List<BigDecimal> todayFcLoads = BasePeriodUtils
                .toList(loadCityFcValue, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
            ArrayList<BigDecimal> todayFc = new ArrayList<>();
            if (todayFcLoads.size() > 0) {
                for (BigDecimal todayFcLoad : todayFcLoads) {
                    todayFcLoad = todayFcLoad.setScale(0, BigDecimal.ROUND_HALF_DOWN);
                    todayFc.add(todayFcLoad);
                }
            }
            result.add(new CurveViewDTO(CurveViewDTO.TODAY_FC, todayFc));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

        try {
            List<BigDecimal> todayHisLoads = this.find96LoadCityHisValue(date, caliberId, cityId);
            ArrayList<BigDecimal> todayHis = new ArrayList<>();
            if (todayHisLoads.size() > 0) {
                for (BigDecimal todayHisLoad : todayHisLoads) {
                    todayHisLoad = todayHisLoad.setScale(0, BigDecimal.ROUND_HALF_DOWN);
                    todayHis.add(todayHisLoad);
                }
            }
            result.add(new CurveViewDTO(CurveViewDTO.TODAY_HIS, todayHis));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

        try {
            List<BigDecimal> yesterHisLoads = this
                .find96LoadCityHisValue(DateUtils.addDays(date, -1), caliberId, cityId);
            ArrayList<BigDecimal> yesterHis = new ArrayList<>();
            if (yesterHisLoads.size() > 0) {
                for (BigDecimal yesterHisLoad : yesterHisLoads) {
                    yesterHisLoad = yesterHisLoad.setScale(0, BigDecimal.ROUND_HALF_DOWN);
                    yesterHis.add(yesterHisLoad);
                }
            }
            result.add(new CurveViewDTO(CurveViewDTO.YESTARDAY_HIS, yesterHis));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

        try {
            List<BigDecimal> temperatureFcs = weatherCityFcService
                .find96WeatherCityFcValue(date, cityId, WeatherEnum.TEMPERATURE.getType());
            result.add(new CurveViewDTO(CurveViewDTO.TEMPERATURE_FC, temperatureFcs));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

        try {
            List<BigDecimal> temperatureHis = weatherCityHisService
                .find96WeatherCityHisValue(date, cityId, WeatherEnum.TEMPERATURE.getType());
            result.add(new CurveViewDTO(CurveViewDTO.TEMPERATURE_HIS, temperatureHis));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }


        try {
            List<BigDecimal> temperatureHis = weatherCityHisService
                .find96WeatherCityHisValue(DateUtils.addDays(date, -1), cityId, WeatherEnum.TEMPERATURE.getType());
            result.add(new CurveViewDTO(CurveViewDTO.YESTARDAY_TEMPERATURE_HIS, temperatureHis));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

        return result;
    }

    private List<BigDecimal> find96LoadCityHisValue(Date date, String caliberId, String cityId) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create()
            .where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()))
            .where(QueryOp.StringEqualTo, "caliberId", caliberId)
            .where(QueryOp.StringEqualTo, "cityId", cityId);
        List<LoadCityHisDO> LoadCityHisDOS = getDAO(LoadCityHisDAO.class).query(dbQueryParamBuilder.build()).getDatas();
        if (!CollectionUtils.isEmpty(LoadCityHisDOS)) {
            return BasePeriodUtils
                .toList(LoadCityHisDOS.get(0), Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        }
        return null;
    }

    private void CalcAreaLoads(Map<String, String> cityMap, Map<String, List<BigDecimal>> areaLoadsMap,
        LoadCityHisDO LoadCityHisDO) {
        String areaName = cityMap.get(LoadCityHisDO.getCityId());
        if (StringUtils.isNotBlank(areaName)) {
            List<BigDecimal> loads = areaLoadsMap.get(areaName);
            if (CollectionUtils.isEmpty(loads)) {
                loads = BasePeriodUtils
                    .toList(LoadCityHisDO, Constants.LOAD_CURVE_POINT_NUM_24, Constants.LOAD_CURVE_START_WITH_ZERO);
            } else {
                loads = com.tsintergy.lf.serviceimpl.common.util.BasePeriodUtils.listAdd(loads, BasePeriodUtils
                    .toList(LoadCityHisDO, Constants.LOAD_CURVE_POINT_NUM_24, Constants.LOAD_CURVE_START_WITH_ZERO));
            }
            areaLoadsMap.put(areaName, loads);
        }
    }

    private void setTodayHisLoads(Map<String, LoadCityHisDO> todayHisLoadsMap, String cityId, CityLoadDTO cityLoadDTO) {
        if (todayHisLoadsMap != null && !todayHisLoadsMap.isEmpty()) {
            LoadCityHisDO todayHisLoad = todayHisLoadsMap.get(cityId);
            if (todayHisLoad != null) {
                List<BigDecimal> todayHisLoads = BasePeriodUtils
                    .toList(todayHisLoad, Constants.LOAD_CURVE_POINT_NUM_24, Constants.LOAD_CURVE_START_WITH_ZERO);
                cityLoadDTO.setTodayHisLoads(todayHisLoads);
            }
        }
    }

    private void setYesterdayHisLoads(Map<String, LoadCityHisDO> yesterdayHisLoadsMap, String cityId,
        CityLoadDTO cityLoadDTO) {
        if (yesterdayHisLoadsMap != null && !yesterdayHisLoadsMap.isEmpty()) {
            LoadCityHisDO yesterdayHisLoad = yesterdayHisLoadsMap.get(cityId);
            if (yesterdayHisLoad != null) {
                List<BigDecimal> todayHisLoads = BasePeriodUtils
                    .toList(yesterdayHisLoad, Constants.LOAD_CURVE_POINT_NUM_24, Constants.LOAD_CURVE_START_WITH_ZERO);
                cityLoadDTO.setYesterdayHisLoads(todayHisLoads);
            }
        }
    }

    private void setTodayFcLoads(Map<String, LoadCityFcDO> todayFcMap, String cityId, CityLoadDTO cityLoadDTO,
        Date today, String caliberId)
        throws Exception {
        if (todayFcMap != null && !todayFcMap.isEmpty()) {
            LoadCityFcDO todayFcLoad = todayFcMap.get(cityId);
            if (todayFcLoad != null) {
                List<BigDecimal> todayFcLoads = BasePeriodUtils
                    .toList(todayFcLoad, Constants.LOAD_CURVE_POINT_NUM_24, Constants.LOAD_CURVE_START_WITH_ZERO);
                cityLoadDTO.setTodayFcLoads(todayFcLoads);
            }
            //如果上报的数据没有
            else {
                List<LoadCityFcDO> loadCityFcDO = this.loadCityFcService
                    .listLoadCityFc(cityId, caliberId, today, today, null);
                if (!CollectionUtils.isEmpty(loadCityFcDO)) {
                    //step1：返回系统推荐算法的数据
                    List<LoadCityFcDO> collect = loadCityFcDO.stream().filter(LoadCityFcDO::getRecommend)
                        .collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(collect)) {
                        List<BigDecimal> todayFcLoads = BasePeriodUtils
                            .toList(collect.get(0), Constants.LOAD_CURVE_POINT_NUM_24,
                                Constants.LOAD_CURVE_START_WITH_ZERO);
                        cityLoadDTO.setTodayFcLoads(todayFcLoads);
                    }
                    //step2:随便返回一个预测的数据
                    else {
                        List<BigDecimal> todayFcLoads = BasePeriodUtils
                            .toList(loadCityFcDO.get(0), Constants.LOAD_CURVE_POINT_NUM_24,
                                Constants.LOAD_CURVE_START_WITH_ZERO);
                        cityLoadDTO.setTodayFcLoads(todayFcLoads);
                    }
                }
            }
        }
    }

    private void setTodayTemperatures(Map<String, WeatherCityHisDO> todayTemperaturesHisVOMap, String cityId,
        CityLoadDTO cityLoadDTO) {
        if (todayTemperaturesHisVOMap != null && !todayTemperaturesHisVOMap.isEmpty()) {
            WeatherCityHisDO todayTemperaturesHisVO = todayTemperaturesHisVOMap.get(cityId);
            if (todayTemperaturesHisVO != null) {
                List<BigDecimal> todayTemperatures = BasePeriodUtils
                    .toList(todayTemperaturesHisVO, Constants.LOAD_CURVE_POINT_NUM_24,
                        Constants.LOAD_CURVE_START_WITH_ZERO);
                cityLoadDTO.setTemperatures(todayTemperatures);
            }
        }
    }

    private Map<String, WeatherCityHisDO> getWeatherCityVOMap(Date date, List<String> cityIds) {
        Map<String, WeatherCityHisDO> todayWeatherMap = null;
        try {
            List<WeatherCityHisDO> todayWeathers = weatherCityHisService
                .findWeatherCityHisDOS(cityIds, WeatherEnum.TEMPERATURE.getType(), date);
            if (!CollectionUtils.isEmpty(todayWeathers)) {
                todayWeatherMap = todayWeathers.stream()
                    .collect(Collectors.toMap(WeatherCityHisDO::getCityId, Function.identity()));
            }
        } catch (Exception e) {
            logger.error("查询历史气象异常...", e);
        }
        return todayWeatherMap;
    }

    private Map<String, LoadCityFcDO> getLoadCityFcDOMap(Date today, List<String> cityIds, String caliberId)
        throws Exception {
        Map<String, LoadCityFcDO> loadFcMap = new HashMap<>(16);
        for (String cityId : cityIds) {
            LoadCityFcDO loadCityFcValue = this.loadCityFcService.find96LoadCityFcValue(today, caliberId, cityId);
            if (loadCityFcValue != null) {
                loadFcMap.put(cityId, loadCityFcValue);
            }
        }
        return loadFcMap;
    }

    private Map<String, LoadCityHisDO> getLoadCityHisDOMap(Date today, List<String> cityIds, String caliberId) {
        Map<String, LoadCityHisDO> hisLoadMap = null;
        try {
            List<LoadCityHisDO> todayHisLoads = findLoadCityHisDO(today, cityIds, caliberId);
            if (!CollectionUtils.isEmpty(todayHisLoads)) {
                hisLoadMap = todayHisLoads.stream()
                    .collect(Collectors.toMap(LoadCityHisDO::getCityId, Function.identity()));
            }
        } catch (Exception e) {
            logger.error("查询历史负荷异常...", e);
        }
        return hisLoadMap;
    }

    public List<LoadCityHisDO> findLoadCityHisDO(Date date, List<String> cityIds, String caliberId) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create()
            .where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()))
            .where(QueryOp.StringIsIn, "cityId", cityIds)
            .where(QueryOp.StringEqualTo, "caliberId", caliberId);
        return this.getDAO(LoadCityHisDAO.class).query(dbQueryParamBuilder.build()).getDatas();

    }

    public List<LoadCityHisDO> findLoadCityHisDO(Date date, String caliberId) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create()
            .where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()))
            .where(QueryOp.StringEqualTo, "caliberId", caliberId);
        return this.getDAO(LoadCityHisDAO.class).query(dbQueryParamBuilder.build()).getDatas();

    }


    private List<BigDecimal> getList(LoadCityFcDO LoadCityFcDO) throws Exception {
        if (LoadCityFcDO != null) {
            return BasePeriodUtils
                .toList(LoadCityFcDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        } else {
            return LoadCalUtil.getNullList(Constants.LOAD_CURVE_POINT_NUM);
        }
    }
}
