package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingBatchInitService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingBatchInitDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcLoadForecastService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcLoadForecastDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcLoadForecastDAO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2025-05-08
 * @since 1.0.0
 */
@Slf4j
@Service("weatherCityFcLoadForecastService")
public class WeatherCityFcLoadForecastServiceImpl implements WeatherCityFcLoadForecastService {

    @Autowired
    private SettingBatchInitService settingBatchInitService;

    @Autowired
    private WeatherCityFcLoadForecastDAO weatherCityFcLoadForecastDAO;

    @Autowired
    private CityService cityService;

    @Autowired
    WeatherCityFcService weatherCityFcService;

    @Autowired
    WeatherCityFcLoadForecastService weatherCityFcLoadForecastService;

    @Override
    public List<WeatherCityFcLoadForecastDO> findWeatherFcLoadForecastBatchByType(String cityId, String algorithmId,
        String caliberId, String batchId, Integer type, Date startDate, Date endDate) throws Exception {
        List<WeatherCityFcLoadForecastDO> tarList = new ArrayList<>();
        List<WeatherCityFcLoadForecastDO> resultList = new ArrayList<>();
        SettingBatchInitDO batchById = settingBatchInitService.getBatchById(batchId);
        List<WeatherCityFcLoadForecastDO> weather = weatherCityFcLoadForecastDAO
            .findWeatherInfoFcLoadForecastBatch(cityId, type, Arrays.asList(algorithmId), null,startDate, endDate, caliberId);
        for (WeatherCityFcLoadForecastDO one : weather) {
            if (DateUtil.isWithinTimeRange(one.getCreatetime(), batchById.getStartTime(), batchById.getEndTime())) {
                resultList.add(one);
            }
        }
        Map<String, List<WeatherCityFcLoadForecastDO>> map = resultList.stream().collect(Collectors.groupingBy(
            src->src.getDate()+"-"+src.getCityId(), TreeMap::new, Collectors.toList())
        );
        //获取批次时间段内最新的一条数据
        map.forEach((key, list)->{
            list.sort(Comparator.comparing(WeatherCityFcLoadForecastDO::getCreatetime).reversed());
            tarList.add(list.get(0));
        });
        return tarList;
    }

    @Override
    public void insertOrUpdateWeatherInfo(String cityId, Date date, String systemAlgorithmId,String caliberId){
        try {
            String weatherDataCityId = cityService.findWeatherCityId(cityId);
            List<WeatherCityFcDO> weatherCityFcDOS =  weatherCityFcService.findWeatherCityFcDOs(weatherDataCityId,date,date);
            for(WeatherCityFcDO weatherCityFcDO : weatherCityFcDOS){
                WeatherCityFcLoadForecastDO weatherCityFcLoadForecastDO = new WeatherCityFcLoadForecastDO(cityId, systemAlgorithmId, weatherCityFcDO.getType(), date,caliberId);
                BasePeriodUtils.setAllFiled(weatherCityFcLoadForecastDO, BasePeriodUtils.toMap(weatherCityFcDO, 96, Constants.LOAD_CURVE_START_WITH_ZERO));
                // 新增批次 防止出现相同批次号
                synchronized (this) {
                    List<WeatherCityFcLoadForecastDO> weatherInfoFcLoadForecast = weatherCityFcLoadForecastService.
                        findWeatherInfoFcLoadForecast(cityId, weatherCityFcDO.getType(), systemAlgorithmId, date,caliberId);
                    if(CollectionUtils.isEmpty(weatherInfoFcLoadForecast)){
                        weatherCityFcLoadForecastDO.setCreatetime(weatherCityFcDO.getCreatetime());
                        weatherCityFcLoadForecastDO.setBatchId(1);
                    }else{
                        Integer batchId = weatherInfoFcLoadForecast.stream()
                            .max(Comparator.comparing(WeatherCityFcLoadForecastDO::getBatchId)).get().getBatchId();
                        weatherCityFcLoadForecastDO.setBatchId(++batchId);
                    }
                    weatherCityFcLoadForecastDO.setCreatetime(weatherCityFcDO.getCreatetime());
                    weatherCityFcLoadForecastDO.setUpdatetime(weatherCityFcDO.getUpdatetime());
                    weatherCityFcLoadForecastService.doCreateAndFlush(weatherCityFcLoadForecastDO);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Override
    public List<WeatherCityFcLoadForecastDO> findWeatherInfoFcLoadForecast(String cityId, Integer type, String algorithmId, Date date,String caliberId) throws Exception {
        return weatherCityFcLoadForecastDAO.findWeatherInfoFcLoadForecast(cityId, type, algorithmId,date,caliberId);
    }

    @Override
    public void doCreateAndFlush(WeatherCityFcLoadForecastDO weatherCityFcLoadForecastDO) {
        weatherCityFcLoadForecastDAO.createAndFlush(weatherCityFcLoadForecastDO);
    }

}
