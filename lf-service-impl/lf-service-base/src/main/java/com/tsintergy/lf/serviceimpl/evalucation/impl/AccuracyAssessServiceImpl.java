package com.tsintergy.lf.serviceimpl.evalucation.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.evalucation.api.AccuracyAssessService;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyAssessDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyAssessUnitDTO;
import com.tsintergy.lf.serviceapi.base.recall.dto.StatisticsAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracyDayBatchService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAlgorithmAccuracyDayRecallService;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAccuracyDayBatchDO;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAlgorithmAccuracyDayDO;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAlgorithmAccuracyRecallDayDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingBatchInitService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingBatchInitDO;
import com.tsintergy.lf.serviceimpl.common.util.DateUtils;
import com.tsintergy.lf.serviceimpl.report.impl.ReportAlgorithmAccuracyDayServiceImpl;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2025-05-07
 * @since 1.0.0
 */
@Service("accuracyAssessService")
public class AccuracyAssessServiceImpl implements AccuracyAssessService {

    @Autowired
    private ReportAlgorithmAccuracyDayRecallService reportAlgorithmAccuracyDayRecallService;

    @Autowired
    private SettingBatchInitService settingBatchInitService;

    @Autowired
    private ReportAccuracyDayBatchService reportAccuracyDayBatchService;

    @Autowired
    private ReportAlgorithmAccuracyDayServiceImpl reportAlgorithmAccuracyDayService;

    @Override
    public List<String> findNameList(String cityId, String caliberId, String algorithmId,
        Date startDate, Date endDate) {
        //查询可用状态的考核点名称（暂时综合、日平均、最大负荷、最小负荷）
        return Arrays.asList("综合", "日平均", "最大负荷", "最小负荷");
    }

    @Override
    public List<AccuracyAssessDTO> findAccuracyAccessRecallList(String cityId, String caliberId, String algorithmId
        , Date startDate, Date endDate, String batchId, Integer day) throws Exception {
        List<AccuracyAssessDTO> accuracyAssessDTOS = new ArrayList<>();
        List<ReportAlgorithmAccuracyDayDO> fcAccuracy = new ArrayList<>();
        SettingBatchInitDO batchById = settingBatchInitService.getBatchById(batchId);
        // 预测综合准确率
        List<ReportAccuracyDayBatchDO> batchReportAccuracy = reportAccuracyDayBatchService.getBatchReportAccuracyByAlgorithm(
            null, cityId, caliberId, algorithmId, startDate, endDate, batchById.getStartTime(), batchById.getEndTime());
        // 如果是地市查询最终批次的数据
        if (!Constants.PROVINCE_ID.equals(cityId) && Arrays.asList("1", "4").contains(batchId)) {
            List<ReportAlgorithmAccuracyDayDO> reportAccuracy = reportAlgorithmAccuracyDayService.getReportAccuracy(
                cityId, caliberId, algorithmId, startDate, endDate, null);
            if (CollectionUtils.isNotEmpty(reportAccuracy)) {
                reportAccuracy.forEach(x -> {
                    ReportAccuracyDayBatchDO reportAccuracyDayBatchDO = new ReportAccuracyDayBatchDO();
                    BeanUtils.copyProperties(x, reportAccuracyDayBatchDO);
                    batchReportAccuracy.add(reportAccuracyDayBatchDO);
                });
            }
        }
        Map<Date, List<ReportAccuracyDayBatchDO>> batchMap = batchReportAccuracy.stream().collect(Collectors.groupingBy(ReportAccuracyDayBatchDO::getDate));
        batchMap.forEach((date,value)->{
            Map<String, List<ReportAccuracyDayBatchDO>> algorithmMap = value.stream().collect(Collectors.groupingBy(ReportAccuracyDayBatchDO::getAlgorithmId));
            algorithmMap.forEach((algorithmIds,lists)->{
                List<ReportAccuracyDayBatchDO> list = lists.stream().sorted(
                    Comparator.comparing(ReportAccuracyDayBatchDO::getCreateTime).reversed()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(list)){
                    ReportAccuracyDayBatchDO statisticsCityDayBatchFcDO = list.get(0);
                    ReportAlgorithmAccuracyDayDO reportAlgorithmAccuracyDayDO = new ReportAlgorithmAccuracyDayDO();
                    BeanUtils.copyProperties(statisticsCityDayBatchFcDO, reportAlgorithmAccuracyDayDO);
                    fcAccuracy.add(reportAlgorithmAccuracyDayDO);
                }
            });
        });
        // 回溯准确率
        List<ReportAlgorithmAccuracyRecallDayDO> recallAccuracy = reportAlgorithmAccuracyDayRecallService.getReportAccuracy(
            cityId, caliberId, algorithmId, startDate, endDate, null);
        if (CollectionUtil.isEmpty(fcAccuracy) && CollectionUtil.isEmpty(recallAccuracy)) {
            return Collections.emptyList();
        }
        Map<Date, List<ReportAlgorithmAccuracyDayDO>> fcAccuracyMap = fcAccuracy.stream()
          .collect(Collectors.groupingBy(ReportAlgorithmAccuracyDayDO::getDate));
        Map<Date, List<ReportAlgorithmAccuracyRecallDayDO>> recallAccuracyMap = recallAccuracy.stream()
            .collect(Collectors.groupingBy(ReportAlgorithmAccuracyRecallDayDO::getDate));
        DateUtils.getListBetweenDay(startDate, endDate).forEach(x -> {
            AccuracyAssessDTO accuracyAssessDTO = new AccuracyAssessDTO();
            accuracyAssessDTO.setDate(x);
            List<ReportAlgorithmAccuracyDayDO> fcAccuracyDayDOS = fcAccuracyMap.get(x);
            List<ReportAlgorithmAccuracyRecallDayDO> recallAccuracyDayDOS = recallAccuracyMap.get(x);
            if (CollectionUtil.isNotEmpty(recallAccuracyDayDOS) && CollectionUtil.isNotEmpty(fcAccuracyDayDOS)) {
                ReportAlgorithmAccuracyRecallDayDO recallAccuracyDayDO = recallAccuracyDayDOS.get(0);
                ReportAlgorithmAccuracyDayDO fcAccuracyDayDO = fcAccuracyDayDOS.get(0);
                statDayAccuracyRecallData(fcAccuracyDayDO, recallAccuracyDayDO, accuracyAssessDTO);
            } else if (CollectionUtil.isNotEmpty(recallAccuracyDayDOS) && CollectionUtil.isEmpty(fcAccuracyDayDOS)) {
                statDayAccuracyRecallData(new ReportAlgorithmAccuracyDayDO(), recallAccuracyDayDOS.get(0), accuracyAssessDTO);
            } else if (CollectionUtil.isEmpty(recallAccuracyDayDOS) && CollectionUtil.isNotEmpty(fcAccuracyDayDOS)) {
                statDayAccuracyRecallData(fcAccuracyDayDOS.get(0), new ReportAlgorithmAccuracyRecallDayDO(), accuracyAssessDTO);
            }
            accuracyAssessDTOS.add(accuracyAssessDTO);
        });
        return accuracyAssessDTOS;
    }

    public void statDayAccuracyRecallData(ReportAlgorithmAccuracyDayDO fcAccuracyDayDO,
        ReportAlgorithmAccuracyRecallDayDO recallAccuracyDayDO, AccuracyAssessDTO accuracyAssessDTO) {
        List<AccuracyAssessUnitDTO> assessUnitList = new ArrayList<>();
        // 综合
        assessUnitList.add(new AccuracyAssessUnitDTO("综合", fcAccuracyDayDO.getComprehensiveAccuracy(),
            recallAccuracyDayDO.getComprehensiveAccuracy(),
            (recallAccuracyDayDO.getComprehensiveAccuracy() != null && fcAccuracyDayDO.getComprehensiveAccuracy() != null) ?
                (recallAccuracyDayDO.getComprehensiveAccuracy().subtract(fcAccuracyDayDO.getComprehensiveAccuracy())) : null,
            fcAccuracyDayDO.getDate()));
        // 日平均
        assessUnitList.add(new AccuracyAssessUnitDTO("日平均", fcAccuracyDayDO.getPointAccuracy(),
            recallAccuracyDayDO.getPointAccuracy(),
            (recallAccuracyDayDO.getPointAccuracy() != null && fcAccuracyDayDO.getPointAccuracy() != null) ?
                (recallAccuracyDayDO.getPointAccuracy().subtract(fcAccuracyDayDO.getPointAccuracy())) : null,
            fcAccuracyDayDO.getDate()));
        // 最大负荷
        assessUnitList.add(new AccuracyAssessUnitDTO("最大负荷", fcAccuracyDayDO.getMaxAccuracy(),
            recallAccuracyDayDO.getMaxAccuracy(),
            (recallAccuracyDayDO.getMaxAccuracy() != null && fcAccuracyDayDO.getMaxAccuracy() != null) ?
                (recallAccuracyDayDO.getMaxAccuracy().subtract(fcAccuracyDayDO.getMaxAccuracy())) : null,
            fcAccuracyDayDO.getDate()));
        // 最小负荷
        assessUnitList.add(new AccuracyAssessUnitDTO("最小负荷", fcAccuracyDayDO.getMinAccuracy(),
            recallAccuracyDayDO.getMinAccuracy(),
            (recallAccuracyDayDO.getMinAccuracy() != null && fcAccuracyDayDO.getMinAccuracy() != null) ?
                (recallAccuracyDayDO.getMinAccuracy().subtract(fcAccuracyDayDO.getMinAccuracy())) : null,
            fcAccuracyDayDO.getDate()));
        accuracyAssessDTO.setAssessUnitList(assessUnitList);
    }

    @Override
    public StatisticsAccuracyDTO getStatisticsAccuracyAccessRecall(String cityId, String caliberId, Date startDate,
        Date endDate, String algorithmId, String batchId,
        String accuracyName, Integer day) throws Exception {
        StatisticsAccuracyDTO statisticsAccuracyDTO = new StatisticsAccuracyDTO();
        List<AccuracyAssessDTO> accuracyAccessRecallList = this.findAccuracyAccessRecallList(cityId, caliberId,
            algorithmId, startDate, endDate, batchId, day);
        if (CollectionUtil.isEmpty(accuracyAccessRecallList)){
            return null;
        }
        List<AccuracyAssessUnitDTO> accuracyAssessUnitDTOList = accuracyAccessRecallList.stream()
            .filter(x -> CollectionUtil.isNotEmpty(x.getAssessUnitList())) // 过滤掉null值
            .flatMap(x -> x.getAssessUnitList().stream())
            .collect(Collectors.toList());
        List<BigDecimal> accuracyFcList = accuracyAssessUnitDTOList.stream()
            .filter(x -> x.getAssessAccuracy() != null && accuracyName.equals(x.getAssessName())) // 过滤掉null值
            .map(AccuracyAssessUnitDTO::getAssessAccuracy)
            .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(accuracyFcList)) {
            BigDecimal accuracyFc = BigDecimalUtils.avgList(accuracyFcList, 4, false);
            if (accuracyFc != null) {
                statisticsAccuracyDTO.setFcAccuracy(accuracyFc);
            }
        }
        List<BigDecimal> accuracyRecallList = accuracyAssessUnitDTOList.stream()
            .filter(x -> x.getAssessAccuracyRecall() != null && accuracyName.equals(x.getAssessName())) // 过滤掉null值
            .map(AccuracyAssessUnitDTO::getAssessAccuracyRecall)
            .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(accuracyRecallList)) {
            BigDecimal accuracyRecall = BigDecimalUtils.avgList(accuracyRecallList, 4, false);
            if (accuracyRecall != null) {
                statisticsAccuracyDTO.setRecallAccuracy(accuracyRecall);
            }
        }
        int areaLhZeroCount = 0;
        int areaZeroToOneCount = 0;
        int areaGhOneCount = 0;
        int notEqualsNullNum = 0;
        ArrayList<BigDecimal> recallDeviationList = accuracyAssessUnitDTOList.stream()
            .filter(x -> x.getRecallDeviation() != null && accuracyName.equals(x.getAssessName()))
            .map(AccuracyAssessUnitDTO::getRecallDeviation)
            .collect(Collectors.toCollection(ArrayList::new));
        for (BigDecimal recallDeviation : recallDeviationList) {
            if (recallDeviation != null) {
                notEqualsNullNum++;
                if (recallDeviation.compareTo(new BigDecimal("0")) == -1) {
                    areaLhZeroCount++;
                } else if (recallDeviation.compareTo(new BigDecimal("0.01")) == 1) {
                    areaGhOneCount++;
                } else {
                    areaZeroToOneCount++;
                }
            }
        }
        if (notEqualsNullNum == 0) {
            areaLhZeroCount = 0;
            areaZeroToOneCount = 0;
            areaGhOneCount = 0;
            notEqualsNullNum = 1;
        }
        BigDecimal area1 = BigDecimal.valueOf((int) areaLhZeroCount).divide(BigDecimal.valueOf((int) notEqualsNullNum), 2, BigDecimal.ROUND_DOWN);
        BigDecimal area2 = BigDecimal.valueOf((int) areaZeroToOneCount).divide(BigDecimal.valueOf((int) notEqualsNullNum), 2, BigDecimal.ROUND_DOWN);
        BigDecimal area3 = BigDecimal.valueOf((int) areaGhOneCount).divide(BigDecimal.valueOf((int) notEqualsNullNum), 2, BigDecimal.ROUND_DOWN);
        if (area1 != null) {
            statisticsAccuracyDTO.setArea1(area1);
        }
        if (area2 != null) {
            statisticsAccuracyDTO.setArea2(area2);
        }
        if (area3 != null) {
            statisticsAccuracyDTO.setArea3(area3);
        }
        return statisticsAccuracyDTO;
    }
}
