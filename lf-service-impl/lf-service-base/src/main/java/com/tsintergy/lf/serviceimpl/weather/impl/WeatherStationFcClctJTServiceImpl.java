package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsintergy.lf.serviceapi.base.weather.api.WeatherStationFcClctJTService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationFcClctJTDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherStationFcClctJTDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service("weatherStationFcClctGDTService")
public class WeatherStationFcClctJTServiceImpl implements WeatherStationFcClctJTService {

    @Autowired
    WeatherStationFcClctJTDAO WeatherStationFcClctJTDAO;

    @Override
    public List<WeatherStationFcClctJTDO> findByDateAndstationId(Date startDate, Date endDate, String stationId,
                                                                 Integer type) throws Exception {
        List<WeatherStationFcClctJTDO> weatherCityFcDO = WeatherStationFcClctJTDAO
                .findWeatherCityFcDO(type,startDate,endDate, stationId);
        return weatherCityFcDO;
    }

    @Override
    public List<WeatherStationFcClctJTDO> findByDateAndstationIds(Date startDate, Date endDate, List<String> stationIds, Integer type) throws Exception {
        List<WeatherStationFcClctJTDO> weatherCityFcDO = WeatherStationFcClctJTDAO
                .findWeatherCityFcDOs(type,startDate,endDate, stationIds);
        return weatherCityFcDO;
    }
}