package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityStationFcBasicZYTService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityStationFcBasicZYTDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityStationFcBasicZYTDAO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.UUID;

@Service("weatherCityStationFcBasicZYTService")
public class WeatherCityStationFcBasicZYTServiceImpl implements WeatherCityStationFcBasicZYTService {

    @Autowired
    WeatherCityStationFcBasicZYTDAO WeatherCityStationFcBasicZYTDAO;

    @Override
    public void saveOrUpdate(WeatherCityStationFcBasicZYTDO weatherCityStationFcBasicZYTDO) throws Exception {
        List<WeatherCityStationFcBasicZYTDO> weatherCityFcDOS = WeatherCityStationFcBasicZYTDAO
                .findWeatherCityFcDO(weatherCityStationFcBasicZYTDO.getCityId(), weatherCityStationFcBasicZYTDO.getType(),
                        weatherCityStationFcBasicZYTDO.getDate(), weatherCityStationFcBasicZYTDO.getDate());
        if (CollectionUtils.isNotEmpty(weatherCityFcDOS)){
            WeatherCityStationFcBasicZYTDO cityFcClctGDTDO = weatherCityFcDOS.get(0);
            weatherCityStationFcBasicZYTDO.setId(cityFcClctGDTDO.getId());
        }else {
            weatherCityStationFcBasicZYTDO.setId(UUID.randomUUID().toString().toLowerCase().replace("-",""));
        }
        WeatherCityStationFcBasicZYTDAO.saveOrUpdateEntityByTemplate(weatherCityStationFcBasicZYTDO);
    }

    @Override
    public List<WeatherCityStationFcBasicZYTDO> findFcWeatherData(String cityId, Integer type, Date startDate, Date endDate) throws Exception {
        return WeatherCityStationFcBasicZYTDAO.findWeatherCityFcDO(cityId,type,startDate,endDate);
    }
}
