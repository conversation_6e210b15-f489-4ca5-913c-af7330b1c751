package com.tsintergy.lf.serviceimpl.weather.dao;

import cn.hutool.core.collection.CollectionUtil;
import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationHisClctDO;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
public class WeatherStationHisClctDAO extends BaseAbstractDAO<WeatherStationHisClctDO> {

    public List<WeatherStationHisClctDO> findWeatherCityFcDOs(Integer type, Date startDate, Date endDate,
            List<String> stationIds) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if(null != startDate){
            param.getQueryConditions().put("_dnl_date",startDate);
        }
        if (null != endDate){
            param.getQueryConditions().put("_dnm_date",endDate);
        }
        if (null != type){
            param.getQueryConditions().put("_ne_type", type);
        }

        if (CollectionUtil.isNotEmpty(stationIds)) {
            param.getQueryConditions().put("_sin_stationId", stationIds);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<WeatherStationHisClctDO> weatherCityFcVOs = this.query(param).getDatas();
        return weatherCityFcVOs;
    }

}