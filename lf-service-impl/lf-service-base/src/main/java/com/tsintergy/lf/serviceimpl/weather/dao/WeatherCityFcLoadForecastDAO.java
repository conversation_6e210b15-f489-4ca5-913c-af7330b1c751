package com.tsintergy.lf.serviceimpl.weather.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcLoadForecastDO;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2025-05-08
 * @since 1.0.0
 */
@Component
@Slf4j
public class WeatherCityFcLoadForecastDAO extends BaseAbstractDAO<WeatherCityFcLoadForecastDO> {

    public List<WeatherCityFcLoadForecastDO> findWeatherInfoFcLoadForecast(String cityId, Integer type, String algorithmId, Date date,String caliberId) throws Exception {

        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if(null != date){
            param.getQueryConditions().put("_de_date",new java.sql.Date(date.getTime()));
        }
        if (null != algorithmId){
            param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        }
        if (null != type){
            param.getQueryConditions().put("_ne_type", type);
        }
        if (null != cityId) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }


        if (null != caliberId) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }

        param.setOrderby("date");
        param.setDesc("0");
        List<WeatherCityFcLoadForecastDO> weatherCityFcLoadForecastDOS = this.query(param).getDatas();
        return weatherCityFcLoadForecastDOS;
    }

    public List<WeatherCityFcLoadForecastDO> findWeatherInfoFcLoadForecastBatch(String cityId, Integer type, List<String> algorithmId,List<Integer> batchIds,
        Date startDate,Date endDate,String caliberId) throws Exception {

        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if(null != startDate){
            dbQueryParamBuilder.where(QueryOp.DateNoLessThan,"date",startDate);
        }

        if(null != endDate){
            dbQueryParamBuilder.where(QueryOp.DateNoMoreThan,"date",endDate);
        }


        if (null != algorithmId){
            dbQueryParamBuilder.where(QueryOp.StringIsIn,"algorithmId", algorithmId);
        }
        if (null != algorithmId){
            dbQueryParamBuilder.where(QueryOp.NumberIsIn,"batchId", batchIds);
        }
        if (null != type){
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo,"type", type);
        }
        if (null != cityId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo,"cityId", cityId);
        }


        if (null != caliberId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo,"caliberId", caliberId);
        }
        List<WeatherCityFcLoadForecastDO> weatherCityFcLoadForecastDOS = this.query(dbQueryParamBuilder.build()).getDatas();
        return weatherCityFcLoadForecastDOS;
    }


    public List<WeatherCityFcLoadForecastDO> findWeatherInfoFcLoadForecast(String cityId, Integer type, String algorithmId,
        Date startDate,Date endDate,String caliberId, Integer batchId) throws Exception {

        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        if(null != startDate){
            dbQueryParamBuilder.where(QueryOp.DateNoLessThan,"date",startDate);
        }

        if(null != endDate){
            dbQueryParamBuilder.where(QueryOp.DateNoMoreThan,"date",endDate);
        }


        if (null != algorithmId){
            dbQueryParamBuilder.where(QueryOp.StringEqualTo,"algorithmId", algorithmId);
        }
        if (null != type){
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo,"type", type);
        }
        if (null != cityId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo,"cityId", cityId);
        }


        if (null != caliberId) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo,"caliberId", caliberId);
        }
        if (null != caliberId) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo,"batchId", batchId);
        }

        List<WeatherCityFcLoadForecastDO> weatherCityFcLoadForecastDOS = this.query(dbQueryParamBuilder.build()).getDatas();
        return weatherCityFcLoadForecastDOS;
    }

    public List<WeatherCityFcLoadForecastDO> findWeatherInfoFcLoadForecast(String cityId, Integer type, List<String> algorithmId, Date date,String caliberId) throws Exception {

        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if(null != date){
            param.getQueryConditions().put("_de_date",new java.sql.Date(date.getTime()));
        }
        if (null != algorithmId){
            param.getQueryConditions().put("_nin_algorithmId", algorithmId);
        }
        if (null != type){
            param.getQueryConditions().put("_ne_type", type);
        }
        if (null != cityId) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }

        if (null != caliberId) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }

        param.setOrderby("createtime");
        param.setDesc("0");
        List<WeatherCityFcLoadForecastDO> weatherCityFcLoadForecastDOS = this.query(param).getDatas();
        return weatherCityFcLoadForecastDOS;
    }

}
