/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司
 * Author:  wang<PERSON>
 * Date:  2020/1/7 9:13
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.report.dao;


import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAccuracyDayBatchDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/1/7
 * @since 1.0.0
 */
@Component
public class ReportAccuracyDayBatchDAO extends BaseAbstractDAO<ReportAccuracyDayBatchDO> {


    @Autowired
    ReportAccuracyDayBatchDAO reportAccuracyDayDAO;


    public List<ReportAccuracyDayBatchDO> findReportAccuracy(String cityId, String caliberId,Date startDate, Date endDate) throws Exception {
        DBQueryParamBuilder builder = DBQueryParamBuilder.create().queryDataOnly();
        if (StringUtils.isNotBlank(cityId) ) {
            builder.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (StringUtils.isNotBlank(caliberId) ) {
            builder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (endDate != null) {
            builder.where(QueryOp.DateNoMoreThan, "date", new java.sql.Date(endDate.getTime()));
        }
        if (startDate != null) {
            builder.where(QueryOp.DateNoLessThan, "date", new java.sql.Date(startDate.getTime()));
        }
        builder.addOrderByAsc("date");
        DBQueryParam dbQueryParam = builder.build();
        List<ReportAccuracyDayBatchDO> accuracyDayVOS = reportAccuracyDayDAO.query(dbQueryParam).getDatas();
        return accuracyDayVOS;
    }

    public List<ReportAccuracyDayBatchDO> findReportAccuracyByAlgorithm(String cityId, String caliberId,Date startDate,
        Date endDate, String algorithmId) throws Exception {
        DBQueryParamBuilder builder = DBQueryParamBuilder.create().queryDataOnly();
        if (StringUtils.isNotBlank(cityId) ) {
            builder.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (StringUtils.isNotBlank(caliberId) ) {
            builder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (StringUtils.isNotBlank(algorithmId) ) {
            builder.where(QueryOp.StringEqualTo, "algorithmId", algorithmId);
        }
        if (endDate != null) {
            builder.where(QueryOp.DateNoMoreThan, "date", new java.sql.Date(endDate.getTime()));
        }
        if (startDate != null) {
            builder.where(QueryOp.DateNoLessThan, "date", new java.sql.Date(startDate.getTime()));
        }
        builder.addOrderByAsc("date");
        DBQueryParam dbQueryParam = builder.build();
        List<ReportAccuracyDayBatchDO> accuracyDayVOS = reportAccuracyDayDAO.query(dbQueryParam).getDatas();
        return accuracyDayVOS;
    }

    public ReportAccuracyDayBatchDO findReportAccuracy(String cityId, String caliberId ,String algorithmId,Date date,Integer batchId) throws Exception {
        DBQueryParamBuilder builder = DBQueryParamBuilder.create().queryDataOnly();
        if (StringUtils.isNotBlank(cityId)) {
            builder.where(QueryOp.StringEqualTo, "cityId", cityId);
        }

        if (StringUtils.isNotBlank(caliberId)) {
            builder.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (StringUtils.isNotBlank(algorithmId)) {
            builder.where(QueryOp.StringEqualTo, "algorithmId", algorithmId);
        }
        if (date != null) {
            builder.where(QueryOp.DateEqualTo, "date", new java.sql.Date(date.getTime()));
        }
        if (batchId != null) {
            builder.where(QueryOp.StringEqualTo, "batchId", batchId);
        }
        builder.addOrderByAsc("date");
        DBQueryParam dbQueryParam = builder.build();
        List<ReportAccuracyDayBatchDO> accuracyDayVOS = reportAccuracyDayDAO.query(dbQueryParam).getDatas();
        if (accuracyDayVOS!=null&&accuracyDayVOS.size()>0)
        {
           return accuracyDayVOS.get(0);
        }
        return null;
    }

}