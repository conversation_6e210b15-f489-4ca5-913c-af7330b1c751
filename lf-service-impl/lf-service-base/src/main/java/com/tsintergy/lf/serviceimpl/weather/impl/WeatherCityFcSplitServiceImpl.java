package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.ColumnUtil;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.DateUtil;
import com.tsintergy.lf.serviceapi.base.common.enumeration.CityEnum;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcSplitService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.BaseWeatherDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcSplitDO;
import com.tsintergy.lf.serviceimpl.common.util.PeriodDataUtil;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcSplitDAO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/** @Description
 * <AUTHOR>
 * @Date 2025/2/7 10:33
 **/
@Service("weatherCityFcSplitService")
public class WeatherCityFcSplitServiceImpl implements WeatherCityFcSplitService {

    @Autowired
    WeatherCityFcSplitDAO weatherCityFcSplitDAO;

    @Autowired
    private WeatherCityFcService weatherCityFcService;
    @Override
    public List<WeatherCityFcSplitDO> findWeatherList(String cityId, Integer type, Date startDate, Date endDate) throws Exception {
        return weatherCityFcSplitDAO.findWeatherCityFcDO(cityId,type,startDate,endDate);
    }

    @Override
    public void insertOrUpdate(WeatherCityFcSplitDO weatherCityFcVO) throws Exception {
        if (weatherCityFcVO.getDate() == null || weatherCityFcVO.getCityId() == null
                || weatherCityFcVO.getType() == null) {
            throw new BusinessException("01C20180008", "");
        }
        List<WeatherCityFcSplitDO> weatherCityFcVOS = this.findWeatherList(weatherCityFcVO.getCityId(),weatherCityFcVO.getType(),weatherCityFcVO.getDate(),weatherCityFcVO.getDate());
        if (weatherCityFcVOS == null || weatherCityFcVOS.isEmpty()) {
            weatherCityFcVO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            weatherCityFcSplitDAO.save(weatherCityFcVO);
            return;
        }
        weatherCityFcSplitDAO.getSession().flush();
        weatherCityFcSplitDAO.getSession().clear();
        WeatherCityFcSplitDO old = weatherCityFcVOS.get(0);
        PeriodDataUtil.replaceValues(old, weatherCityFcVO);
        old.setUpdatetime(new Timestamp(System.currentTimeMillis()));
        weatherCityFcSplitDAO.update(old);
    }



    @Override
    public void doStatSplitFcWeather(Date startDate, Date endDate) throws Exception {
        List<WeatherCityFcDO> fcWeatherData = weatherCityFcService.findFcWeatherData(CityEnum.guangzhou.getId(), null, startDate, endDate);
        List<BaseWeatherDO> gdtWeather = weatherCityFcService.getWeatherDataFromWeatherTypes(CityEnum.guangzhou.getId(), null, startDate, endDate, "gdt");

        Map<String, BaseWeatherDO> gdtWeatherMap = gdtWeather.stream().collect(Collectors.toMap(t -> DateUtil.formateDate(t.getDate()) + "-" + t.getType(), Function.identity()));

        List<WeatherCityFcSplitDO> weatherCityFcSplitDOS = new ArrayList<>();
        for (WeatherCityFcDO fcWeatherDatum : fcWeatherData) {
            java.sql.Date date = fcWeatherDatum.getDate();
            String key = DateUtil.formateDate(date) + "-" + fcWeatherDatum.getType();
            BaseWeatherDO gdtbaseWeatherDO = gdtWeatherMap.get(key);
            WeatherCityFcSplitDO weatherCityFcSplitDO = new WeatherCityFcSplitDO();
            weatherCityFcSplitDO.setCityId(fcWeatherDatum.getCityId());
            weatherCityFcSplitDO.setType(fcWeatherDatum.getType());
            weatherCityFcSplitDO.setDate(date);
            weatherCityFcSplitDOS.add(weatherCityFcSplitDO);
            //不存在广东台气象直接用玖天
            if (Objects.isNull(gdtbaseWeatherDO)) {
                BeanUtils.copyProperties(fcWeatherDatum, weatherCityFcSplitDO);
                continue;
            }

            // 提取核心数据列表
            List<BigDecimal> jtList = fcWeatherDatum.getWeatherList();
            List<BigDecimal> gdtList = gdtbaseWeatherDO.getWeatherList();

            // 获取极值并检查差异
            boolean needAverage = checkExtremeDifference(jtList, gdtList, new BigDecimal("2"));

            // 构建结果列表
            List<BigDecimal> valueList = new ArrayList<>(Constants.WEATHER_CURVE_POINT_NUM);
            valueList.addAll(needAverage ?
                    com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.calculateAverage(jtList, gdtList) :
                    jtList);

            // 设置对象属性
            Map<String, BigDecimal> fieldMap = ColumnUtil.listToMap(
                    valueList,
                    Constants.WEATHER_CURVE_START_WITH_ZERO);
            BasePeriodUtils.setAllFiled(weatherCityFcSplitDO, fieldMap);
        }
        for (WeatherCityFcSplitDO weatherCityFcSplitDO : weatherCityFcSplitDOS) {
            insertOrUpdate(weatherCityFcSplitDO);
        }
    }

    //检查极值差异
    private boolean checkExtremeDifference(List<BigDecimal> list1, List<BigDecimal> list2, BigDecimal threshold) {
        Optional<BigDecimal> max1 = getMax(list1);
        Optional<BigDecimal> max2 = getMax(list2);
        Optional<BigDecimal> min1 = getMin(list1);
        Optional<BigDecimal> min2 = getMin(list2);

        return isDifferenceExceeds(max1, max2, threshold) ||
                isDifferenceExceeds(min1, min2, threshold);
    }


    private Optional<BigDecimal> getMax(List<BigDecimal> list) {
        return list.stream().filter(Objects::nonNull).max(BigDecimal::compareTo);
    }

    private Optional<BigDecimal> getMin(List<BigDecimal> list) {
        return list.stream().filter(Objects::nonNull).min(BigDecimal::compareTo);
    }

    // 判断差值是否超过阈值
    private boolean isDifferenceExceeds(Optional<BigDecimal> a, Optional<BigDecimal> b, BigDecimal threshold) {
        return a.isPresent() &&
                b.isPresent() &&
                a.get().subtract(b.get()).abs().compareTo(threshold) > 0;
    }

}
