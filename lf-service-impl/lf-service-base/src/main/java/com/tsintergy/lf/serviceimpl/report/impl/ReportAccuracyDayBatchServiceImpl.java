package com.tsintergy.lf.serviceimpl.report.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.tsintergy.lf.core.constants.DateUtil;
import com.tsintergy.lf.serviceapi.base.area.api.BasePlanService;
import com.tsintergy.lf.serviceapi.base.check.api.SettingCheckService;
import com.tsintergy.lf.serviceapi.base.check.pojo.SettingCheckPrecisionDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayBatchFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayFcBatchService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.load.api.RollingLoadService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayFcBatchDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracyDayBatchService;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAccuracyDayBatchDO;
import com.tsintergy.lf.serviceimpl.common.constants.LoadCalUtil;
import com.tsintergy.lf.serviceimpl.evalucation.dao.AccuracyLoadCityBatchFcDAO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.StatisticsCityDayBatchFcDAO;
import com.tsintergy.lf.serviceimpl.report.dao.ReportAccuracyDayBatchDAO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/** @Description
 * <AUTHOR>
 * @Date 2025/2/18 14:28
 **/
@Service("reportAccuracyDayBatchService")
public class ReportAccuracyDayBatchServiceImpl implements ReportAccuracyDayBatchService {

    @Autowired
    private LoadFeatureCityDayFcBatchService loadFeatureCityDayFcBatchService;

    @Autowired
    private LoadFeatureCityDayHisService loadFeatureCityDayHisService;


    @Autowired
    private BasePlanService basePlanService;

    @Autowired
    private StatisticsCityDayBatchFcDAO statisticsCityDayBatchFcDAO;

    @Autowired
    private RollingLoadService rollingLoadService;

    @Autowired
    private ReportAccuracyDayBatchDAO reportAccuracyDayBatchDAO;

    @Autowired
    private SettingCheckService settingCheckService;


    @Override
    public List<ReportAccuracyDayBatchDO> doStatSaveReportAccuracy(String cityId, String caliberId, Date startDate, Date endDate) throws Exception {
        List<ReportAccuracyDayBatchDO> reportAccuracyDayVOS = new ArrayList<>();
        //查询预测数据 其准确率来自  预测特性统计
        List<LoadFeatureCityDayFcBatchDO> reportLoads = loadFeatureCityDayFcBatchService
                .findLoadFeatureCityDayFcs(cityId, caliberId, null, new java.sql.Date(startDate.getTime()),
                        new java.sql.Date(endDate.getTime()), null);
        //查询真实的数据 只计算 口径为1的
        List<LoadFeatureCityDayHisDO> realLoads = loadFeatureCityDayHisService
                .findLoadFeatureCityDayHisDOS(cityId, startDate, endDate, caliberId);
        //查询上报的准确率
        List<StatisticsCityDayBatchFcDO> statisticsCityDayFcVOS = statisticsCityDayBatchFcDAO
                .getStatisticsCityDayFcDOs(null, cityId, caliberId, null, startDate, endDate, null);

        Map<String, StatisticsCityDayBatchFcDO> statisticsCityDayFcVOMap = statisticsCityDayFcVOS.stream().collect(Collectors.toMap(t -> t.getBatchId() + "-" + t.getCityId() + "-" + t.getCaliberId() +
                "-" + t.getAlgorithmId() + "-" + t.getDate().getTime(), Function.identity()));

        Map<String, LoadFeatureCityDayHisDO> loadMap = realLoads.stream().collect(Collectors.toMap(t -> t.getCityId() + "-" + t.getCaliberId() +
                "-" + t.getDate().getTime(), Function.identity()));

        Date date2025 = DateUtil.getDate("2025-01-01", "yyyy-MM-dd");
        //分区id
        List<String> defaultAreaIds = basePlanService.getDefaultAreaIds();

        Map<String, SettingCheckPrecisionDO> map = settingCheckService.findStandMap();


        for (LoadFeatureCityDayFcBatchDO featureDTO : reportLoads) {
            //特殊处理，澳门和地方电不做统计
            if (featureDTO.getCityId().equals("23") || featureDTO.getCityId().equals("24") || defaultAreaIds.contains(featureDTO.getCityId())) {
                continue;
            }

            String loadKey = featureDTO.getCityId() + "-" + featureDTO.getCaliberId() + "-"
                    + featureDTO.getDate().getTime();

            String batchKey = featureDTO.getBatchId() + "-" + featureDTO.getCityId() + "-" + featureDTO.getCaliberId() + "-"
                    + featureDTO.getAlgorithmId() + "-" + featureDTO.getDate().getTime();
            StatisticsCityDayBatchFcDO cityDayFcVO = statisticsCityDayFcVOMap.get(batchKey);
            ReportAccuracyDayBatchDO reportAccuracyDayVO = new ReportAccuracyDayBatchDO();
            BeanUtils.copyProperties(featureDTO, reportAccuracyDayVO);
            if (Objects.isNull(cityDayFcVO)) {
                continue;
            }
            reportAccuracyDayVO.setCreateTime(cityDayFcVO.getCreatetime());
            reportAccuracyDayVO.setPointAccuracy(cityDayFcVO.getAccuracy());
            LoadFeatureCityDayHisDO loadDayVO = loadMap.get(loadKey);
            if (loadDayVO != null) {
                BigDecimal hisMaxLoad = loadDayVO.getMaxLoad();
                BigDecimal hisMinLoad = loadDayVO.getMinLoad();
                reportAccuracyDayVO
                        .setMaxAccuracy(LoadCalUtil.calcMaxMinAccuracy(hisMaxLoad, featureDTO.getMaxLoad()));
                reportAccuracyDayVO
                        .setMinAccuracy(LoadCalUtil.calcMaxMinAccuracy(hisMinLoad, featureDTO.getMinLoad()));
                // TODO
                reportAccuracyDayVO
                        .setEneryAccuracy(LoadCalUtil.eneryGradient(loadDayVO.getEnergy(), featureDTO.getEnergy()));

                // TODO 修改 2025年日综合准确率为新版计算公式 之前则使用旧公式
                if (reportAccuracyDayVO.getDate().compareTo(date2025) >= 0) {
                    reportAccuracyDayVO
                            .setComprehensiveAccuracy(LoadCalUtil.compreGradient(
                                    reportAccuracyDayVO.getPointAccuracy(),
                                    reportAccuracyDayVO.getMaxAccuracy(),
                                    reportAccuracyDayVO.getMinAccuracy()));
                } else {
                    reportAccuracyDayVO
                            .setComprehensiveAccuracy(LoadCalUtil.compreGradient(
                                    reportAccuracyDayVO.getPointAccuracy(),
                                    reportAccuracyDayVO.getMaxAccuracy(),
                                    reportAccuracyDayVO.getMinAccuracy(), reportAccuracyDayVO.getEneryAccuracy()));
                }

            } else {
                reportAccuracyDayVO.setMinAccuracy(BigDecimal.ZERO);
                reportAccuracyDayVO.setMaxAccuracy(BigDecimal.ZERO);
                // TODO
                reportAccuracyDayVO.setEneryAccuracy(BigDecimal.ZERO);
                reportAccuracyDayVO.setComprehensiveAccuracy(BigDecimal.ZERO);
            }


            //计算合格率
            SettingCheckPrecisionDO settingCheckPrecisionDO = map.get(featureDTO.getCityId() + "-" + DateUtil.getYearByDate(featureDTO.getDate()));
            if (settingCheckPrecisionDO != null) {
                Map<String, BigDecimal> decimalMap = LoadCalUtil.toMap(settingCheckPrecisionDO);
                BigDecimal standard = decimalMap.get(DateUtil.getMonthDate(featureDTO.getDate()));
                BigDecimal comprehensiveAccuracy = reportAccuracyDayVO.getComprehensiveAccuracy().multiply(new BigDecimal("100"));
                reportAccuracyDayVO.setStandardAccuracy(standard);
                if (comprehensiveAccuracy.compareTo(standard) >= 0) {
                    reportAccuracyDayVO.setPass(BigDecimal.ONE);
                } else {
                    reportAccuracyDayVO.setPass(BigDecimal.ZERO);
                }
            }
            reportAccuracyDayVOS.add(reportAccuracyDayVO);
        }
        return reportAccuracyDayVOS;
    }

    @Override
    public void doSaveOrUpdate(List<ReportAccuracyDayBatchDO> reportAccuracyDayVOS) throws Exception {
        if (CollectionUtils.isEmpty(reportAccuracyDayVOS)) {
            return;
        }
        for (ReportAccuracyDayBatchDO accuracyDayVO : reportAccuracyDayVOS) {
            ReportAccuracyDayBatchDO ReportAccuracyDayDO = reportAccuracyDayBatchDAO
                    .findReportAccuracy(accuracyDayVO.getCityId(), accuracyDayVO.getCaliberId(), accuracyDayVO.getAlgorithmId(), accuracyDayVO.getDate(), accuracyDayVO.getBatchId());
            if (Objects.nonNull(ReportAccuracyDayDO)) {
                BeanUtils.copyProperties(accuracyDayVO, ReportAccuracyDayDO, "id");
                reportAccuracyDayBatchDAO.updateAndFlush(ReportAccuracyDayDO);
            } else {
                reportAccuracyDayBatchDAO.createAndFlush(accuracyDayVO);
            }
        }
    }

    @Override
    public List<ReportAccuracyDayBatchDO> getBatchReportAccuracy(Integer batchId, String cityId, String caliberId, String algorithmId, Date startDate, Date endDate, String startTime, String endTime) throws Exception {
        List<ReportAccuracyDayBatchDO> reportAccuracy = reportAccuracyDayBatchDAO.findReportAccuracy(cityId, caliberId, startDate, endDate);
        return reportAccuracy.stream().filter(t -> {
            Date createtime = t.getCreateTime();
            return AccuracyLoadCityBatchFcDAO.checkTime(startTime, endTime, createtime);
        }).collect(Collectors.toList());
    }

    @Override
    public List<ReportAccuracyDayBatchDO> getBatchReportAccuracyByAlgorithm(Integer batchId, String cityId,
        String caliberId, String algorithmId, Date startDate, Date endDate, String startTime, String endTime)
        throws Exception {
        List<ReportAccuracyDayBatchDO> reportAccuracy = reportAccuracyDayBatchDAO.findReportAccuracyByAlgorithm(cityId,
            caliberId, startDate, endDate, algorithmId);
        return reportAccuracy.stream().filter(t -> {
            Date createtime = t.getCreateTime();
            return AccuracyLoadCityBatchFcDAO.checkTime(startTime, endTime, createtime);
        }).collect(Collectors.toList());
    }


}
