package com.tsintergy.lf.web.base.weather.controller;


import com.tsieframework.cloud.security.serviceapi.system.enums.TokenType;
import com.tsieframework.cloud.security.web.businesslog.annotation.OperateLog;
import com.tsieframework.cloud.security.web.common.security.generator.annotations.MenuResource;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.core.constants.ColumnUtil;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.DateUtil;
import com.tsintergy.lf.core.constants.WeatherTypeEnum;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.api.WeatherTyphoonDefinitionService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.WeatherTyphoonDefinitionDO;
import com.tsintergy.lf.serviceapi.base.datamanage.api.WeatherPowerFcWarnInfoService;
import com.tsintergy.lf.serviceapi.base.datamanage.pojo.WeatherPowerFcWarnInfoDO;
import com.tsintergy.lf.serviceapi.base.forecast.dto.PlanDTO;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherNewEnum;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.power.api.PowerWeatherService;
import com.tsintergy.lf.serviceapi.base.power.api.WeatherPowerFcClctService;
import com.tsintergy.lf.serviceapi.base.power.api.WeatherPowerHisClctService;
import com.tsintergy.lf.serviceapi.base.power.dto.CityWeatherMissingDTO;
import com.tsintergy.lf.serviceapi.base.power.dto.PowerWeatherAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.power.dto.PowerWeatherCurveDTOList;
import com.tsintergy.lf.serviceapi.base.power.dto.StationWeatherRateDTO;
import com.tsintergy.lf.serviceapi.base.power.dto.WeatherTypeDTO;
import com.tsintergy.lf.serviceapi.base.power.pojo.BasePowerWeatherStationInfoDO;
import com.tsintergy.lf.serviceapi.base.power.pojo.WeatherPowerFcClctDO;
import com.tsintergy.lf.serviceapi.base.power.pojo.WeatherPowerHisClctDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingSystemDO;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.tsintergy.lf.serviceapi.base.weather.dto.*;
import com.tsintergy.lf.serviceapi.base.weather.pojo.BaseWeatherDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.RateVO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherTyphoonRateDO;
import com.tsintergy.lf.serviceimpl.common.util.DateUtils;
import com.tsintergy.lf.serviceimpl.power.dao.BasePowerWeatherStationInfoDAO;
import com.tsintergy.lf.serviceimpl.system.constant.SystemConstant;
import com.tsintergy.lf.web.base.common.ExcelUtil;
import com.tsintergy.lf.web.base.controller.BaseController;
import com.tsintergy.lf.web.base.forecast.thread.ForecastTask;
import com.tsintergy.lf.web.base.util.response.CommonResp;
import com.tsintergy.lf.web.base.weather.request.CityRateRequest;
import com.tsintergy.lf.web.base.weather.request.WeatherHisFcRequest;
import com.tsintergy.lf.web.base.weather.request.WeatherHisRequest;
import com.tsintergy.lf.web.base.weather.request.WeatherSensitivityRequest;
import com.tsintergy.lf.web.base.weather.request.WeatherSimilarRequest;
import com.tsintergy.lf.web.base.weather.request.WeatherSourceFcAccuracyRequest;
import com.tsintergy.lf.web.base.weather.request.WeatherSourceFcRequest;
import com.tsintergy.lf.web.base.weather.response.WeatherDataResp;
import com.tsintergy.lf.web.base.weather.response.WeatherOutsideVO;
import com.tsintergy.lf.web.base.weather.response.WeatherSimilar;
import com.tsintergy.lf.web.base.weather.response.WeatherSimilarResp;
import com.tsintergy.lf.web.base.weather.support.LoadCalUtil;
import com.tsintergy.lf.web.base.weather.support.MyMock;
import io.swagger.annotations.ApiOperation;
import java.io.BufferedOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Todo: 气象
 * @date:17-12-18 上午9:25
 * @author:taojingui
 **/

@RequestMapping("/weather")
@RestController
public class WeatherController extends BaseController {

    private final Logger logger = LogManager.getLogger(WeatherController.class);

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private WeatherCityFcService weatherCityFcService;

    @Autowired
    private WeatherSensitivityCityDayService weatherSensitivityDayService;

    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    private WeatherFeatureCityMonthHisService weatherFeatureCityMonthHisService;

    @Autowired
    private WeatherFeatureCityQuarterHisService weatherFeatureCityQuarterHisService;

    @Autowired
    private HolidayService holidayService;


    @Autowired
    private WeatherTyphoonDefinitionService weatherTyphoonDefinitionService;

    @Autowired
    private LoadFeatureCityDayHisService loadFeatureCityDayHisService;

    @Autowired
    private WeatherTyphoonRateService weatherTyphoonRateService;

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private CityService cityService;

    @Autowired
    WeatherSourceFcService weatherSourceFcService;
    @Autowired
    SettingSystemService settingSystemService;

    @Autowired
    private PowerWeatherService powerWeatherService;

    @Autowired
    WeatherPowerFcClctService weatherPowerFcClctService;

    @Autowired
    FcWeatherService fcWeatherService;

    @Autowired
    WeatherPowerHisClctService weatherPowerHisClctService;

    @Autowired
    WeatherPowerFcWarnInfoService weatherPowerFcWarnInfoService;


    /**
     * 获取气象的历史和预报数据
     */
    @RequestMapping("/his-fc")
    @MenuResource(name = "获取气象的历史和预报数据", type = TokenType.API, parentMenuPath = Constants.PREDICTION_EVALUATION_PARENT)
    public BaseResp getHisAndFcData(WeatherHisFcRequest weatherHisFcRequest) throws Exception {

        if (weatherHisFcRequest.getCityId() == null) {
            weatherHisFcRequest.setCityId(this.getLoginCityId());
        }
        if (weatherHisFcRequest.getDate() == null) {
            weatherHisFcRequest.setDate(this.getSystemDate());
        }
        if (weatherHisFcRequest.getType() == null) {
            weatherHisFcRequest.setType(WeatherEnum.TEMPERATURE.value());
        }
        SettingSystemDO byFieldId = settingSystemService.findByFieldId(SystemConstant.DEFAULT_FC_WEATHER_SOURCE);
        WeatherDataResp weatherDataResp = new WeatherDataResp();
        try {
            CityDO cityVO = cityService.findCityById(weatherHisFcRequest.getCityId());
           // String cityId = weatherHisFcRequest.getCityId();
            String  cityId = cityService.findWeatherCityId(cityVO.getId());
            weatherHisFcRequest.setCityId(cityId);

            // 历史数据
            List<WeatherCityHisDO> weatherCityHisVOs = weatherCityHisService
                    .findWeatherCityHisDOs(weatherHisFcRequest.getCityId(), weatherHisFcRequest.getType(),
                            weatherHisFcRequest.getDate(), weatherHisFcRequest.getDate());
            if (weatherCityHisVOs != null && weatherCityHisVOs.size() > 0) {
                weatherDataResp.setReal(BasePeriodUtils
                        .toList(weatherCityHisVOs.get(0), Constants.WEATHER_CURVE_POINT_NUM,
                                Constants.WEATHER_CURVE_START_WITH_ZERO));
            }
            // 预测数据
            List<BaseWeatherDO> weatherCityFcVOs = super
                .getWeatherDataFromSettings(cityId, weatherHisFcRequest.getType(), weatherHisFcRequest.getDate(),
                    weatherHisFcRequest.getDate());
            if (weatherCityFcVOs != null && weatherCityFcVOs.size() > 0) {
                weatherDataResp.setForecast(BasePeriodUtils
                        .toList(weatherCityFcVOs.get(0), Constants.WEATHER_CURVE_POINT_NUM,
                                Constants.WEATHER_CURVE_START_WITH_ZERO));
            }
        } catch (Exception e) {
            logger.error("获取气象的历史和预报数据失败", e);
            return new BaseResp("T706");
        }

        BaseResp resp = BaseResp.succResp();
        if (weatherDataResp.getForecast() == null && weatherDataResp.getReal() == null) {
            return new BaseResp("T706");
        }
        resp.setData(weatherDataResp);
        return resp;
    }

    /**
     * 获取气象历史数据
     */
    @RequestMapping(value = "/history", method = RequestMethod.GET)
    @MenuResource(name = "获取气象历史数据", type = TokenType.API, parentMenuPath = Constants.DATA_EDIT_PARENT)
    public Object getWeatherHistory(@Validated WeatherHisRequest weatherHisRequest, BindingResult result)
            throws Exception {
        validateFormValue(result);
        if (!StringUtils.isNotBlank(weatherHisRequest.getCityId())) {
            weatherHisRequest.setCityId(this.getLoginCityId());
        }
        String cityId = weatherHisRequest.getCityId();
        BaseResp<CommonResp<WeatherDTO>> response = BaseResp.succResp("查询成功");
        List<WeatherDTO> weatherDTOS = weatherCityHisService
                .findWeatherCityHisDTOs(cityId, weatherHisRequest.getType(), weatherHisRequest.getStartDate(),
                        weatherHisRequest.getEndDate());
        CommonResp<WeatherDTO> commonResp = new CommonResp<WeatherDTO>();
        commonResp.setDataList(weatherDTOS);
        response.setData(commonResp);
        return response;
    }


    /**
     * 获取气象预测数据
     */
    @RequestMapping(value = "/fc", method = RequestMethod.GET)
    @MenuResource(name = "获取气象预测数据", type = TokenType.API, parentMenuPath = Constants.DATA_EDIT_PARENT)
    public Object getWeatherFc(@Validated WeatherHisRequest weatherHisRequest, BindingResult result) throws Exception {
        validateFormValue(result);
        if (!StringUtils.isNotBlank(weatherHisRequest.getCityId())) {
            weatherHisRequest.setCityId(this.getLoginCityId());
        }
        String cityId = weatherHisRequest.getCityId();
        BaseResp<CommonResp<WeatherDTO>> response = BaseResp.succResp("查询成功");
        List<BaseWeatherDO> weatherDataFromSettings = super
            .getWeatherDataFromSettings(cityId, weatherHisRequest.getType(), weatherHisRequest.getStartDate(),
                weatherHisRequest.getEndDate());
        List<WeatherDTO> weatherDTOS = new ArrayList<WeatherDTO>(10);
        for (BaseWeatherDO weatherCityHisVO : weatherDataFromSettings) {
            WeatherDTO weatherDTO = new WeatherDTO();
            weatherDTO.setId(weatherCityHisVO.getId());
            weatherDTO.setDate(weatherCityHisVO.getDate());
            weatherDTO.setWeek(DateUtil.getWeek(weatherCityHisVO.getDate()));
            weatherDTO.setCity(this.cityService.findCityById(weatherCityHisVO.getCityId()).getCity());
            weatherDTO.setData(BasePeriodUtils.toList(weatherCityHisVO, Constants.WEATHER_CURVE_POINT_NUM,
                Constants.WEATHER_CURVE_START_WITH_ZERO));
            weatherDTOS.add(weatherDTO);
        }
        CommonResp<WeatherDTO> commonResp = new CommonResp<WeatherDTO>();
        commonResp.setDataList(weatherDTOS);
        response.setData(commonResp);
        return response;
    }

    /**
     * 导出气象历史数据
     */
    @RequestMapping(value = "/history/export", method = RequestMethod.GET)
    @OperateLog(operate = "导出气象历史数据")
    @MenuResource(name = "导出气象历史数据", type = TokenType.API, parentMenuPath = Constants.DATA_EDIT_PARENT)
    public Object exportWeatherHistory(@Validated WeatherHisRequest weatherHisRequest, BindingResult result)
            throws Exception {
        validateFormValue(result);
        if (!StringUtils.isNotBlank(weatherHisRequest.getCityId())) {
            weatherHisRequest.setCityId(this.getLoginCityId());
        }
        BaseResp response = BaseResp.succResp("导出成功");
        List<WeatherDTO> weatherDTOS = weatherCityHisService
                .findWeatherCityHisDTOs(weatherHisRequest.getCityId(), weatherHisRequest.getType(),
                        weatherHisRequest.getStartDate(), weatherHisRequest.getEndDate());

        //todo 可能要改成从配置文件读取
        String[] heads = com.tsintergy.lf.web.base.util.PropertiesUtil.getLabel("title").split(",");
        InputStream in = ExcelUtil.getExcel("气象", heads, weatherDTOS);
        this.getResponse().setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        //格式化文件名使用中文
        String fileName = URLEncoder.encode("数据管理", "UTF-8");
//        String fileName = new String("数据管理".getBytes(),"ISO-8859-1");
        this.getResponse().setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        OutputStream out = new BufferedOutputStream(this.getResponse().getOutputStream());
        byte[] data = new byte[1024];
        int len = 0;
        while (-1 != (len = in.read(data, 0, data.length))) {
            out.write(data, 0, len);
        }
        if (in != null) {
            in.close();
        }
        if (out != null) {
            out.close();
        }
        return response;
    }

    /**
     * 气温敏感度
     */
    @RequestMapping(value = "/sensitivity", method = RequestMethod.GET)
    @MenuResource(name = "获取气温敏感度", type = TokenType.API, parentMenuPath = Constants.METEOROLOGY_SENSITIVITY_ANALYSIS_PARENT)
    public Object getWeatherSensivity(@Validated WeatherSensitivityRequest weatherSensivityRequest,
                                      BindingResult result) throws Exception {
        validateFormValue(result);
        if (StringUtils.isEmpty(weatherSensivityRequest.getCityId())) {
            weatherSensivityRequest.setCityId(this.getLoginCityId());
        }
        if (StringUtils.isBlank(weatherSensivityRequest.getCaliberId())) {
            weatherSensivityRequest.setCaliberId(this.getCaliberId());
        }
        BaseResp<CommonResp<WeatherSensitivityDTO>> resp = BaseResp.succResp("查询成功");
        CommonResp<WeatherSensitivityDTO> commonResp = new CommonResp<WeatherSensitivityDTO>();
//        List<WeatherSensitivityDTO> weatherSensitivityDTOS=weatherSensitivityDayService.findWeatherSensitivity(weatherSensivityRequest.getCityId(), weatherSensivityRequest.getTargetDay(), weatherSensivityRequest.getEndDate(), weatherSensivityRequest.getLoadType(), weatherSensivityRequest.getWeatherEnum());
        /** 应该是通过算法得出结果，暂时跳过 **///todo wangchen   这里居然是生成随机数？？？
        List<WeatherSensitivityDTO> weatherSensitivityDTOS = new ArrayList<WeatherSensitivityDTO>(10);
        for (int i = 0; i < 10; i++) {
            weatherSensitivityDTOS.add(MyMock.mock(WeatherSensitivityDTO.class));
        }
        // 全部节假日
        List<Date> holidays = holidayService.getAllHolidays();
        if (weatherSensivityRequest.getHoliday()) {
            Iterator<WeatherSensitivityDTO> it = weatherSensitivityDTOS.iterator();
            while (it.hasNext()) {
                WeatherSensitivityDTO weatherSensitivityDTO = it.next();
                if (holidays.contains(weatherSensitivityDTO.getDate())) {
                    it.remove();
                }
            }
        }
        if (weatherSensivityRequest.getWeek()) {
            Iterator<WeatherSensitivityDTO> it = weatherSensitivityDTOS.iterator();
            while (it.hasNext()) {
                if (DateUtil.isWeekend(it.next().getDate())) {
                    it.remove();
                }
            }
        }
        commonResp.setDataList(weatherSensitivityDTOS);
        resp.setData(commonResp);
        return resp;
    }

    /**
     * 获取气象的特征和综合指标
     */
    @RequestMapping("/feature")
    @MenuResource(name = "获取主导气象因素辨识", type = TokenType.API, parentMenuPath = Constants.METEOROLOGY_SENSITIVITY_ANALYSIS_PARENT)
    public Object getWeatherFeature(@Validated WeatherSensitivityRequest weatherSensitivityRequest,
                                    BindingResult result) throws Exception {
        validateFormValue(result);
        if (StringUtils.isEmpty(weatherSensitivityRequest.getCityId())) {
            weatherSensitivityRequest.setCityId(this.getLoginCityId());
        }
        if (StringUtils.isBlank(weatherSensitivityRequest.getCaliberId())) {
            weatherSensitivityRequest.setCaliberId(this.getCaliberId());
        }
        BaseResp response = BaseResp.succResp("查询成功");
//        WeatherFeatureCityDayHisDO weatherFeatureCityDayHisVO = weatherFeatureCityDayHisService.findWeatherFeatureCityHisVOByDate(WeatherSensitivityRequest.getCityId(), DateUtils.string2Date(WeatherSensitivityRequest.getTargetDay(), DateFormatType.SIMPLE_DATE_FORMAT_STR));
        /** 应该是通过算法得出结果，暂时跳过 **/  //todo wangchen ???
        WeatherFeatureCityDayHisDO weatherFeatureCityDayHisVO = new WeatherFeatureCityDayHisDO();
        weatherFeatureCityDayHisVO.setMaxWinds(new BigDecimal(60));
        weatherFeatureCityDayHisVO.setLowestTemperature(new BigDecimal(23));
        weatherFeatureCityDayHisVO.setHighestTemperature(new BigDecimal(30));
        weatherFeatureCityDayHisVO.setAveTemperature(new BigDecimal(25));
        weatherFeatureCityDayHisVO.setRainfall(new BigDecimal(30));
        weatherFeatureCityDayHisVO.setHighestEffectiveTemperature(new BigDecimal(23.21));
        weatherFeatureCityDayHisVO.setHighestComfort(new BigDecimal(66.98));
        weatherFeatureCityDayHisVO.setMaxColdness(new BigDecimal(812.18));
        weatherFeatureCityDayHisVO.setHighestHumidity(new BigDecimal(98));
        weatherFeatureCityDayHisVO.setAveTemperatureHumidity(new BigDecimal(30));

        response.setData(weatherFeatureCityDayHisVO);

        if (response.getData() == null) {
            return new BaseResp("T706");
        } else {
            return response;
        }

    }


    /**
     * 极端天气数据更新
     */

    @RequestMapping(value = "/updateWeatherTyphoon", method = RequestMethod.POST)
    @OperateLog(operate = "更新极端天气数据更新")
    @MenuResource(name = "更新极端天气数据更新", type = TokenType.API, parentMenuPath = Constants.DATA_EDIT_PARENT)
    public BaseResp updateWeatherTyphoon(@Validated @RequestBody WeatherTyphoonDefinitionDO weatherTyphoonDefinitionVO,
                                         BindingResult result) throws Exception {
        validateFormValue(result);

        BaseResp resp = BaseResp.succResp();

        if (StringUtils.isEmpty(weatherTyphoonDefinitionVO.getId())) {
            WeatherTyphoonDefinitionDO vo;
            List<WeatherTyphoonDefinitionDO> vos = weatherTyphoonDefinitionService.getWeatherTyphoonVO();
            vo = vos.get(0);
            weatherTyphoonDefinitionVO.setId(vo.getId());
        }
        weatherTyphoonDefinitionVO.setUpdateAt(new Timestamp(System.currentTimeMillis()));

        weatherTyphoonDefinitionVO = weatherTyphoonDefinitionService.doUpdate(weatherTyphoonDefinitionVO);

        resp.setData(weatherTyphoonDefinitionVO);

        return resp;
    }

    /**
     * 极端天气数据查询
     */

    @RequestMapping(value = "/getWeatherTyphoon", method = RequestMethod.GET)
    @MenuResource(name = "获取极端天气数据", type = TokenType.API, parentMenuPath = Constants.DATA_EDIT_PARENT)
    public BaseResp getWeatherTyphoon() throws Exception {
        BaseResp resp = BaseResp.succResp();
        WeatherTyphoonDefinitionDO wvo;
        List<WeatherTyphoonDefinitionDO> list = weatherTyphoonDefinitionService.getWeatherTyphoonVO();
        if (CollectionUtils.isEmpty(list)) {
            return new BaseResp("T706");
        }
        wvo = list.get(0);
        resp.setData(wvo);
        return resp;
    }

//    public Object updateCityRate(@RequestBody CityRateRequest cityRateRequest){
//
//        return null;
//    }

    /**
     * 更新全省参照气象(地市占全省的比例)
     */

    @RequestMapping(value = "/updateTyphoonRate", method = RequestMethod.POST)
    @OperateLog(operate = "更新全省参照气象")
    @MenuResource(name = "更新全省参照气象(地市占全省的比例)", type = TokenType.API, parentMenuPath = Constants.DATA_EDIT_PARENT)
    public BaseResp updateTyphoonRate(@RequestBody CityRateRequest request) throws Exception {
        List<RateVO> list = request.getList();
        Map map = new HashMap<>();
        List<String> keyList = new ArrayList();
        List valueList = new ArrayList();
        //更新之前先清空数据库
        List<WeatherTyphoonRateDO> weatherList = weatherTyphoonRateService.getAllTyphoonRate();
        if (weatherList.size() != 0) {
            weatherTyphoonRateService.delete(weatherList);
        }
        Date date1 = DateUtil.getCureDate();
        for (RateVO rateVO : list) {
            WeatherTyphoonRateDO weatherTyphoonRate = new WeatherTyphoonRateDO();
            weatherTyphoonRate.setUpdateAt(new Timestamp(date1.getTime()));
            weatherTyphoonRate.setCityId(rateVO.getCityId());
            BigDecimal rateB = rateVO.getRate();
            BigDecimal rate = rateB.divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP);
            weatherTyphoonRate.setRate(rate);
            weatherTyphoonRate = weatherTyphoonRateService.creat(weatherTyphoonRate);

            keyList.add(weatherTyphoonRate.getCityId());
            String rateS = String.valueOf(rate);
            valueList.add(rateS);

        }
        map.put("cityId", keyList);
        map.put("rate", valueList);

        BaseResp resp = BaseResp.succResp();

        resp.setData(map);

        return resp;

    }


    /**
     * 查询全省参照气象(地市占全省的比例)
     */

    @RequestMapping(value = "/fingWeatherTyphoonRate", method = RequestMethod.GET)
    @MenuResource(name = "查询全省参照气象(地市占全省的比例)", type = TokenType.API, parentMenuPath = Constants.DATA_QUERY_PARENT)
    public BaseResp fingWeatherTyphoonRate() throws Exception {
        BaseResp resp = BaseResp.succResp();
        Map map = weatherTyphoonRateService.findTyphoonRateMap();
        if (map == null || CollectionUtils.isEmpty(map)) {
            return new BaseResp("T706");
        }
        resp.setData(map);
        return resp;
    }


    /**
     * 加载全省参照气象(地市占全省的比例) cityId为省的id 就是1
     */

    @RequestMapping(value = "/getWeatherTyphoonRate", method = RequestMethod.GET)
    @MenuResource(name = "加载加载全省参照气象(地市占全省的比例)", type = TokenType.API, parentMenuPath = Constants.DATA_EDIT_PARENT)
    public BaseResp getWeatherTyphoonRate() throws Exception {

        String caliberId = this.getCaliberId();
        //省份id恒为1
        String cityId = "1";
        //String caliberId = paramRequest.getCaliberId();
        BaseResp resp = BaseResp.succResp();
        //获取当前日期的前一天
        String date = DateUtil.getPreDay("yyyy-MM-dd");
        // String date = DateUtil.getCureDateStr("yyyy-MM-dd");

        //先查询查找前一日的日负荷特性最大值发生的时间
        DBQueryParam param = DBQueryParamBuilder.create()
                .where(QueryOp.NumberEqualTo, "cityId", cityId)
                .where(QueryOp.NumberEqualTo, "caliberId", caliberId)
                .where(QueryOp.DateEqualTo, "date", date)
                .build();

        List<LoadFeatureCityDayHisDO> list = loadFeatureCityDayHisService.queryLoadFeatureCityDayHisDO(param)
                .getDatas();
        if (list == null || list.size() < 1) {
            return BaseResp.failResp("日负荷特性数据异常");
        }
        LoadFeatureCityDayHisDO loadFeatureCityDayHisVO = list.get(0);
        //获得最大负荷出现的时间
        String maxTime = loadFeatureCityDayHisVO.getMaxTime();

        //获得最大负荷数值
        BigDecimal maxLoad = new BigDecimal(0);

        //根据时间查找地市历史负荷数据

        String str1 = maxTime.substring(0, 2);
        String str2 = maxTime.substring(3);

        maxTime = "t" + str1 + str2;

        //转换成Int
        //Integer type = Integer.parseInt(maxTime);

        //查询同一时间 各地市的历史负荷对象
        DBQueryParam param1 = DBQueryParamBuilder.create()
                //.queryDataOnly()
                // .selectFields(maxTime)
                .where(QueryOp.DateEqualTo, "date", date)
                .where(QueryOp.NumberEqualTo, "caliberId", caliberId)
                .build();
        List<LoadCityHisDO> list1 = loadCityHisService.queryLoadCityHisDO(param1).getDatas();

        Map loadMap = new HashMap();
        for (LoadCityHisDO loadCityHisVO : list1) {
            if (!loadCityHisVO.getCityId().equals("1")) {
                System.out.println(loadCityHisVO);
                Map map = BasePeriodUtils.toMap(loadCityHisVO, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
                Set keySet = map.keySet();
                Iterator it = keySet.iterator();
                while (it.hasNext()) {
                    String time = (String) it.next();
                    if (time.equals(maxTime)) {
                        BigDecimal load = (BigDecimal) map.get(time);
                        maxLoad = maxLoad.add(load);
                        loadMap.put(loadCityHisVO.getCityId(), load);
                    }
                }
            }


        }
        Map result = weatherTyphoonRateService.addTyphoonRateMap(loadMap, maxLoad);
        resp.setData(result);
        return resp;

    }


    @RequestMapping(value = "/similar/weather", method = RequestMethod.GET)
    @OperateLog(operate = "相似日实际气象")
    @MenuResource(name = "相似日实际气象", type = TokenType.API, parentMenuPath = Constants.MANUAL_PARENT)
    public BaseResp queryWeather(WeatherSimilarRequest weatherSimilarRequest) throws Exception {
        String cityId = weatherSimilarRequest.getCityId();
        Integer type = weatherSimilarRequest.getType();
        List<Date> dateList = weatherSimilarRequest.getDateList();
        BaseResp baseResp = BaseResp.succResp();
        WeatherSimilarResp weatherSimilarResp = new WeatherSimilarResp();
        List<BaseWeatherDO> weatherCityFcVOS = weatherCityFcService
                .findWeatherCityFcDOs(cityId, type, weatherSimilarRequest.getTargetDate(),
                        weatherSimilarRequest.getTargetDate());
        if (!CollectionUtils.isEmpty(weatherCityFcVOS)) {
            BaseWeatherDO weatherCityFcVO = weatherCityFcVOS.get(0);
            weatherSimilarResp.setFcWeatherLoad(BasePeriodUtils
                    .toList(weatherCityFcVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
        }
        if (CollectionUtils.isEmpty(dateList) && dateList.size() < 1) {
            if (weatherSimilarResp.getFcWeatherLoad() == null) {
                return new BaseResp("T706");
            }
            return baseResp;
        }
        //如果是省 则用省会城市的气象
        cityId  = cityService.findWeatherCityId(cityId);
//        cityId = cityVO.getId();
        List<WeatherCityHisDO> weatherCityHisVOS = weatherCityHisService.findWeatherByDates(cityId, type, dateList);
        if (CollectionUtils.isEmpty(weatherCityHisVOS)) {
            return new BaseResp("T706");
        }
        List<WeatherSimilar> similarArrayList = new ArrayList<>();
        for (WeatherCityHisDO weatherCityHisVO : weatherCityHisVOS) {
            WeatherSimilar weatherSimilar = new WeatherSimilar();
            weatherSimilar.setDate(weatherCityHisVO.getDate());
            weatherSimilar.setWeatherLoads(BasePeriodUtils.toList(weatherCityHisVO, Constants.LOAD_CURVE_POINT_NUM,
                    Constants.LOAD_CURVE_START_WITH_ZERO));
            similarArrayList.add(weatherSimilar);
        }
        weatherSimilarResp.setWeatherSimilars(similarArrayList);
        baseResp.setData(weatherSimilarResp);
        return baseResp;
    }

    /**
     * @param cityId
     * @param startDate
     * @param endDate
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/days")
    public BaseResp findWeather(String cityId, Date startDate, Date endDate, Integer type) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        cityId = cityService.findWeatherCityId(cityId);
        List<WeatherCityHisDO> weatherCityHisVOS = weatherCityHisService.findWeatherCityHisDOs(cityId, type, startDate, endDate);
        List<BaseWeatherDO> fcVOS = super.getWeatherDataFromSettings(cityId, type, startDate, endDate);;
        if (weatherCityHisVOS.size() < 1 && fcVOS.size() < 1) {
            return new BaseResp("T706");
        }
        //转map
        Map<Date, WeatherCityHisDO> realMap = weatherCityHisVOS.stream().collect(Collectors.toMap(WeatherCityHisDO::getDate, Function.identity(), (key1, key2) -> key2));
        Map<Date, BaseWeatherDO> fcMap = fcVOS.stream().collect(Collectors.toMap(BaseWeatherDO::getDate, Function.identity(), (key1, key2) -> key2));
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        List<BigDecimal> his = new ArrayList<>();
        List<BigDecimal> fc = new ArrayList<>();
        WeatherDataResp dataResp = new WeatherDataResp();
        for (Date date : dateList) {
            WeatherCityHisDO hisVO = realMap.get(date);
            if (hisVO != null) {
                his.addAll(BasePeriodUtils.toList(hisVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            } else {
                his.addAll(LoadCalUtil.getNullList(Constants.LOAD_CURVE_POINT_NUM));
            }
            BaseWeatherDO fcVO = fcMap.get(date);
            if (fcVO != null) {
                fc.addAll(BasePeriodUtils.toList(fcVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            } else {
                fc.addAll(LoadCalUtil.getNullList(Constants.LOAD_CURVE_POINT_NUM));
            }
        }
        dataResp.setForecast(fc);
        dataResp.setReal(his);
        baseResp.setData(dataResp);
        return baseResp;
    }



    @PostMapping(value = "/source/fc")
    @ApiOperation(value = "多日不同数据源气象曲线")
    public BaseResp<List<WeatherFcDTO>>findSourceWeatherPoint(@RequestBody WeatherSourceFcRequest weatherSourceFcRequest)
        throws Exception {
        List<WeatherFcDTO> sourceWeatherPoint = weatherSourceFcService
            .findSourceWeatherPoint(weatherSourceFcRequest.getCityId(), weatherSourceFcRequest.getSource(),
                weatherSourceFcRequest.getStartDate(), weatherSourceFcRequest.getEndDate(),Constants.HIS);
        return this.baseResp(sourceWeatherPoint);
    }


    @PostMapping(value = "/source/fcFeature")
    @ApiOperation(value = "多日不同数据源气象特性")
    public BaseResp<List<WeatherFcFeatureDTO>> findSourceWeatherFeature(@RequestBody WeatherSourceFcRequest weatherSourceFcRequest)
        throws Exception {
        List<WeatherFcFeatureDTO> sourceWeatherFeature = weatherSourceFcService
            .findSourceWeatherFeature(weatherSourceFcRequest.getCityId(), weatherSourceFcRequest.getSource(),
                weatherSourceFcRequest.getStartDate(), weatherSourceFcRequest.getEndDate());
        return this.baseResp(sourceWeatherFeature);
    }


    @PostMapping (value = "/source/fcAccuracy")
    @ApiOperation(value = "多日不同数据源气象准确率")
    public BaseResp findSourceWeatherAccuracy(@RequestBody WeatherSourceFcAccuracyRequest weatherSourceFcAccuracyRequest)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<WeatherFcAccuracyDTO> sourceWeatherAccuracy = weatherSourceFcService
            .findSourceWeatherAccuracy(weatherSourceFcAccuracyRequest.getCityId(),weatherSourceFcAccuracyRequest.getSource()
                , weatherSourceFcAccuracyRequest.getStartDate(), weatherSourceFcAccuracyRequest.getEndDate());
        baseResp.setData(sourceWeatherAccuracy);
        return baseResp;
    }

    @PostMapping (value = "/source/fcAccuracyAvg")
    @ApiOperation(value = "多日不同数据源气象平均准确率")
    public BaseResp findSourceWeatherAccuracyAvg(@RequestBody WeatherSourceFcAccuracyRequest weatherSourceFcAccuracyRequest)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<WeatherFcAccuracyDTO> sourceWeatherAccuracy = weatherSourceFcService
            .findSourceWeatherAccuracy(weatherSourceFcAccuracyRequest.getCityId(),weatherSourceFcAccuracyRequest.getSource()
                , weatherSourceFcAccuracyRequest.getStartDate(), weatherSourceFcAccuracyRequest.getEndDate());
        List<WeatherFcAccuracyDTO> WeatherAccuracyList = new ArrayList<>();
        Map<String, List<WeatherFcAccuracyDTO>> map = sourceWeatherAccuracy.stream()
            .collect(Collectors.groupingBy(WeatherFcAccuracyDTO::getType));

        for (WeatherNewEnum weatherEnum : WeatherNewEnum.values()) {
            List<WeatherFcAccuracyDTO> weatherFcAccuracyDTOS = map.get(String.valueOf(weatherEnum.getType()));
            if (weatherFcAccuracyDTOS !=  null && weatherFcAccuracyDTOS.size()>0){
                Map<String, List<WeatherFcAccuracyDTO>> sourceMap = weatherFcAccuracyDTOS.stream()
                    .collect(Collectors.groupingBy(WeatherFcAccuracyDTO::getSource));
                for (String source : weatherSourceFcAccuracyRequest.getSource()) {
                    List<WeatherFcAccuracyDTO> fcAccuracyDTOS = sourceMap.get(source);
                    List<BigDecimal> list = new ArrayList<>();
                    WeatherFcAccuracyDTO weatherFcAccuracyDTO = new WeatherFcAccuracyDTO();
                    if (fcAccuracyDTOS != null && fcAccuracyDTOS.size()>0){
                        for (WeatherFcAccuracyDTO fcAccuracyDTO : fcAccuracyDTOS) {
                            weatherFcAccuracyDTO.setCityId(fcAccuracyDTO.getCityId());
                            weatherFcAccuracyDTO.setCityName(fcAccuracyDTO.getCityName());
                            list.add(fcAccuracyDTO.getAccuracy());
                        }
                        Map<String, BigDecimal> maxMinAvg = BasePeriodUtils.getMaxMinAvg(list, 4);
                        if (maxMinAvg != null && maxMinAvg.size()>0){
                            BigDecimal accuracy = maxMinAvg.get("avg");
                            weatherFcAccuracyDTO.setAccuracy(accuracy);
                        }
                    }
                    weatherFcAccuracyDTO.setSource(source);
                    weatherFcAccuracyDTO.setType(String.valueOf(weatherEnum.getType()));
                    WeatherAccuracyList.add(weatherFcAccuracyDTO);
                }
            }

        }
        baseResp.setData(WeatherAccuracyList);
        return baseResp;
    }


    /**
     * 功能描述: <br>
     * 导出全部城市的气象数据
     *
     * @return
     * <AUTHOR>
     * @since 1.0.0
     */
    @GetMapping(value = "/exportAllPoint")
    @ApiOperation(value = "导出全部城市的气象数据")
    public BaseResp exportAllPoint(HttpServletResponse response,Date startDate,Date endDate) throws Exception {
        OutputStream outputStream = response.getOutputStream();
        List<String> cityS = cityService.findCityIdsByBelongId("1");
        List<String> cityIds = cityS.stream().filter(s -> !(s.equals("24"))).collect(Collectors.toList());
        List<String> sources= Arrays.asList(new String[]{"1", "2", "3", "4"});
        List<String> list = new ArrayList<>(sources);
        List<WeatherFcDTO> pointS = weatherSourceFcService
            .findSourceWeatherPoint(cityIds, list, startDate, endDate, Constants.HIS);
        //写数据到excel
        XSSFWorkbook wk = new XSSFWorkbook();
        XSSFSheet sheet = wk.createSheet();
        sheet.setDefaultColumnWidth(20);
        XSSFCellStyle cellStyle = wk.createCellStyle();
        XSSFRow row = sheet.createRow(0);
        row.createCell(0).setCellValue("单位");
        row.createCell(1).setCellValue("日期");
        row.createCell(2).setCellValue("气象源");
        row.createCell(3).setCellValue("气象类型");
        List<String> columns = ColumnUtil
            .getColumns(Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO, true);
        for (int j=0;j<96;j++){
            row.createCell(j+4).setCellValue(columns.get(j));
        }
        int i =1;
        if (pointS != null && pointS.size()>0){
            for (WeatherFcDTO weatherFcDTO : pointS) {
                XSSFRow row1 = sheet.createRow(i);
                if (weatherFcDTO.getPoint()!=null && weatherFcDTO.getPoint().size()>0){
                    row1.createCell(0).setCellValue(weatherFcDTO.getCityName());
                    row1.createCell(1).setCellValue(DateUtils.getDateToStr(weatherFcDTO.getDate()));
                    row1.createCell(2).setCellValue(weatherFcDTO.getSourceName());
                    row1.createCell(3).setCellValue(WeatherNewEnum.getValueByName(
                        Integer.valueOf(weatherFcDTO.getType())));
                    List<BigDecimal> points = weatherFcDTO.getPoint();
                    for (int n =0;n<points.size();n++){
                        BigDecimal point = points.get(n);
                        if (point != null){
                            row1.createCell(n+4).setCellValue(String.valueOf(point));
                        }else {
                            row1.createCell(n+4).setCellValue("");
                        }

                    }
                    i++;
                }
            }
        }
        String file_name="城市气象数据.xlsx";
        response.setContentType("application/x-download");
        file_name = new String(file_name.getBytes(), "ISO-8859-1");
        response.setHeader("Content-disposition", "attachment; filename="+file_name);
        //response.setContentType("application/msexcel");
        wk.write(outputStream);
        outputStream.close();
        return BaseResp.succResp();
    }

    /**
     * 功能描述: <br>
     * 导出全部城市的气象数据
     *
     * @return
     * <AUTHOR>
     * @since 1.0.0
     */
    @GetMapping(value = "/exportWeatherFeature")
    @ApiOperation(value = "导出全部城市的气象特性")
    public BaseResp exportWeatherFeature(HttpServletResponse response,Date startDate,Date endDate) throws Exception {
        OutputStream outputStream = response.getOutputStream();
        List<String> cityS = cityService.findCityIdsByBelongId("1");
        List<String> cityIds = cityS.stream().filter(s -> !(s.equals("24"))).collect(Collectors.toList());
        List<String> sources= Arrays.asList(new String[]{"1", "2", "3", "4"});
        List<String> list = new ArrayList<>(sources);
        List<WeatherFcFeatureDTO> weatherFcFeatureDTOS = weatherSourceFcService
            .findSourceWeatherFeature(cityIds, list, startDate, endDate);
        //写数据到excel
        XSSFWorkbook wk = new XSSFWorkbook();
        XSSFSheet sheet = wk.createSheet();
        sheet.setDefaultColumnWidth(20);
        XSSFCellStyle cellStyle = wk.createCellStyle();
        XSSFRow row = sheet.createRow(0);
        row.createCell(0).setCellValue("单位");
        row.createCell(1).setCellValue("日期");
        row.createCell(2).setCellValue("气象源");
        row.createCell(3).setCellValue("最高温度");
        row.createCell(4).setCellValue("最低温度");
        row.createCell(5).setCellValue("平均温度");
        row.createCell(6).setCellValue("最大湿度");
        row.createCell(7).setCellValue("最小湿度");
        row.createCell(8).setCellValue("平均湿度");
        row.createCell(9).setCellValue("累积降水量");
        row.createCell(10).setCellValue("最大风速");
        row.createCell(11).setCellValue("最小风速");
        row.createCell(12).setCellValue("平均风速");
        int i =1;
        if (weatherFcFeatureDTOS != null && weatherFcFeatureDTOS.size()>0){
            for (WeatherFcFeatureDTO weatherFcFeatureDTO : weatherFcFeatureDTOS) {
                XSSFRow row1 = sheet.createRow(i);
                if (weatherFcFeatureDTO.getAccumulatePrecipitation()!= null || weatherFcFeatureDTO.getHumidityAvg() != null || weatherFcFeatureDTO.getSpeedMax()!= null
                    || weatherFcFeatureDTO.getTemperatureMax()!= null){
                    row1.createCell(0).setCellValue(weatherFcFeatureDTO.getCityName());
                    row1.createCell(1).setCellValue(DateUtils.getDateToStr(weatherFcFeatureDTO.getDate()));
                    row1.createCell(2).setCellValue(weatherFcFeatureDTO.getSourceName());
                    if (weatherFcFeatureDTO.getTemperatureMax()!= null){
                        row1.createCell(3).setCellValue(String.valueOf(weatherFcFeatureDTO.getTemperatureMax()));
                    }else {
                        row1.createCell(3).setCellValue("");
                    }
                    if (weatherFcFeatureDTO.getTemperatureMin() != null){
                        row1.createCell(4).setCellValue(String.valueOf(weatherFcFeatureDTO.getTemperatureMin()));
                    }else {
                        row1.createCell(4).setCellValue("");
                    }
                    if (weatherFcFeatureDTO.getTemperatureAvg() != null){
                        row1.createCell(5).setCellValue(String.valueOf(weatherFcFeatureDTO.getTemperatureAvg()));
                    }else {
                        row1.createCell(5).setCellValue("");
                    }

                    if (weatherFcFeatureDTO.getHumidityMax()!= null){
                        row1.createCell(6).setCellValue(String.valueOf(weatherFcFeatureDTO.getHumidityMax()));
                    }else {
                        row1.createCell(6).setCellValue("");
                    }
                    if (weatherFcFeatureDTO.getHumidityMin()!= null){
                        row1.createCell(7).setCellValue(String.valueOf(weatherFcFeatureDTO.getHumidityMin()));
                    }else {
                        row1.createCell(7).setCellValue("");
                    }
                    if (weatherFcFeatureDTO.getHumidityAvg() != null){
                        row1.createCell(8).setCellValue(String.valueOf(weatherFcFeatureDTO.getHumidityAvg()));
                    }else {
                        row1.createCell(8).setCellValue("");
                    }
                    if (weatherFcFeatureDTO.getAccumulatePrecipitation()!= null){
                        row1.createCell(9).setCellValue(String.valueOf(weatherFcFeatureDTO.getAccumulatePrecipitation()));
                    }else {
                        row1.createCell(9).setCellValue("");
                    }
                    if (weatherFcFeatureDTO.getSpeedMax()!= null){
                        row1.createCell(10).setCellValue(String.valueOf(weatherFcFeatureDTO.getSpeedMax()));
                    }else {
                        row1.createCell(10).setCellValue("");
                    }
                    if (weatherFcFeatureDTO.getSpeedMin()!= null){
                        row1.createCell(11).setCellValue(String.valueOf(weatherFcFeatureDTO.getSpeedMin()));
                    }else {
                        row1.createCell(11).setCellValue("");
                    }
                    if (weatherFcFeatureDTO.getSpeedAvg() != null){
                        row1.createCell(12).setCellValue(String.valueOf(weatherFcFeatureDTO.getSpeedAvg()));
                    }
                    i++;
                }
            }
        }
        String file_name="城市气象特性数据.xlsx";
        response.setContentType("application/x-download");
        file_name = new String(file_name.getBytes(), "ISO-8859-1");
        response.setHeader("Content-disposition", "attachment; filename="+file_name);
        wk.write(outputStream);
        outputStream.close();
        return BaseResp.succResp();
    }

    @GetMapping(value = "/getWeatherList")
    @ApiOperation(value = "查询站点气象")
    public BaseResp<List<WeatherCurveDTOList>> getWeatherList(Date startDate ,Date endDate,String stationId)  {
        BaseResp baseResp = BaseResp.succResp();
        List<PowerWeatherCurveDTOList> weatherCurveDTOS = powerWeatherService.getWeatherList(stationId,startDate,endDate);
        baseResp.setData(weatherCurveDTOS);
        return baseResp;
    }

    @GetMapping(value = "/getStationList")
    @ApiOperation(value = "查询站点列表")
    public BaseResp<List<BasePowerWeatherStationInfoDO>> getCountryList(String cityId){
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(powerWeatherService.getStationByCityId(cityId));
        return baseResp;
    }

    @GetMapping(value = "/getWeatherTypeList")
    @ApiOperation(value = "查询站点气象指标")
    public BaseResp<List<WeatherTypeDTO>> getWeatherTypeList(){
        BaseResp baseResp = BaseResp.succResp();
        List<WeatherTypeDTO> result = new ArrayList<>(7);
        for (WeatherTypeEnum value : WeatherTypeEnum.values()) {
            WeatherTypeDTO weatherTypeDTO = new WeatherTypeDTO(value.getType(),value.getName());
            if (WeatherTypeEnum.RADI.getType() == value.getType()){
                weatherTypeDTO.setName("辐照度");
            }
            result.add(weatherTypeDTO);
        }
        baseResp.setData(result);
        return baseResp;
    }

    @GetMapping(value = "/getWeatherAccuracy")
    @ApiOperation(value = "查询站点准确率")
    public BaseResp<PowerWeatherAccuracyDTO> getWeatherAccuracy(Date date,String stationId,Integer type){
        BaseResp baseResp = BaseResp.succResp();
        PowerWeatherAccuracyDTO powerWeatherAccuracyDTO = powerWeatherService.getWeatherAccuracy(date,stationId,type);
        baseResp.setData(powerWeatherAccuracyDTO);
        return baseResp;
    }

    @GetMapping(value = "/getWeatherAvgAccuracy")
    @ApiOperation(value = "查询站点平均准确率")
    public BaseResp getWeatherAvgAccuracy(Date startDate,Date endDate ,String stationId,Integer type){
        BaseResp baseResp = BaseResp.succResp();
        BigDecimal result = powerWeatherService.getWeatherAvgAccuracy(startDate,endDate,stationId,type);
        baseResp.setData(result!=null?result.toString():"");
        return baseResp;
    }


    @GetMapping(value = "/getCityWeatherMissing")
    @ApiOperation(value = "各地市站点气象缺失概况")
    public BaseResp getCityWeatherMissing(Date startDate,Date endDate,Integer weatherType,String type){
        BaseResp baseResp = BaseResp.succResp();
        List<CityWeatherMissingDTO> result = powerWeatherService.getCityWeatherMissing(startDate,endDate,weatherType,type);
        baseResp.setData(result);
        return baseResp;
    }

    @GetMapping(value = "/getStationWeatherRate")
    @ApiOperation(value = "所有站点气象缺失概况")
    public BaseResp getStationWeatherRate(Date startDate,Date endDate,Integer weatherType,String type)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        StationWeatherRateDTO result = powerWeatherService.getStationWeatherRate(startDate,endDate,weatherType,type);
        baseResp.setData(result);
        return baseResp;
    }

    @GetMapping(value = "/getCityWeatherRate")
    @ApiOperation(value = "各地市气象缺失比例")
    public BaseResp getCityWeatherRate(Date startDate,Date endDate,Integer weatherType,String type)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<StationWeatherRateDTO> result = powerWeatherService.getCityWeatherRate(startDate,endDate,weatherType,type);
        baseResp.setData(result);
        return baseResp;
    }



    @GetMapping(value = "/getPointWeatherMap")
    @ApiOperation(value = "各地市气象地图")
    public BaseResp<WeatherMapDTO> getPointWeatherMap(Date date,Integer type,String column) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        WeatherMapDTO result = powerWeatherService.getPointWeatherMap(date,type,column);
        baseResp.setData(result);
        return baseResp;
    }


    @GetMapping(value = "/outside/weatherData")
    @ApiOperation(value = "给外部系统提供气象数据接口")
    public BaseResp<List<WeatherOutsideVO>> getWeatherData(String source,Date startDate,Date endDate,String cityId,Integer weatherType,String hisFcType) throws Exception{

        List<WeatherOutsideVO> vos = new ArrayList<>();
        switch (source){
            //1是新能源站点数据
            case "1":
                List<BasePowerWeatherStationInfoDO> stationByCityId = powerWeatherService.getStationByCityId(cityId);
                Map<String, List<BasePowerWeatherStationInfoDO>> map = stationByCityId.stream()
                    .collect(Collectors.groupingBy(t -> t.getId()));
                //1是实际
                if(hisFcType.equals("1")){
                    List<WeatherPowerHisClctDO> powerWeatherCityHisClctByCity = weatherPowerHisClctService.findPowerWeatherCityHisClctByCity(
                        cityId, startDate, endDate, weatherType);
                    for(WeatherPowerHisClctDO weatherPowerHisClctDO:powerWeatherCityHisClctByCity){
                        WeatherOutsideVO weatherOutsideVO = new WeatherOutsideVO();
                        weatherOutsideVO.setHisFcTypeName("实际");
                        weatherOutsideVO.setDate(DateUtils.getDateToStr(weatherPowerHisClctDO.getDate()));
                        weatherOutsideVO.setDataSource("新能源站点气象");
                        weatherOutsideVO.setWeatherType(weatherPowerHisClctDO.getType());
                        weatherOutsideVO.setWeatherTypeName(
                            WeatherTypeEnum.getWeatherTypeEnumByType(weatherPowerHisClctDO.getType()).getName());
                        weatherOutsideVO.setCityId(cityId);
                        weatherOutsideVO.setCityName(cityService.findCityById(cityId).getCity());
                        weatherOutsideVO.setCountryId(weatherPowerHisClctDO.getCountryId());
                        List<BasePowerWeatherStationInfoDO> basePowerWeatherStationInfoDOS = map.get(
                            weatherPowerHisClctDO.getCountryId());
                        if(!CollectionUtils.isEmpty(basePowerWeatherStationInfoDOS)) {
                            weatherOutsideVO.setCountryName(basePowerWeatherStationInfoDOS.get(0).getStationName());
                        }
                        List<BigDecimal> bigDecimals = BasePeriodUtils.toList(weatherPowerHisClctDO,
                            Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
                        weatherOutsideVO.setWeather(bigDecimals);
                        vos.add(weatherOutsideVO);
                    }
                    //2是预测
                }else if(hisFcType.equals("2")){
                    List<WeatherPowerFcClctDO> powerWeatherCityFcByCity = weatherPowerFcClctService.findPowerWeatherCityFcByCity(
                        cityId, startDate, endDate, weatherType);
                    for(WeatherPowerFcClctDO weatherPowerFcClctDO:powerWeatherCityFcByCity){
                        WeatherOutsideVO weatherOutsideVO = new WeatherOutsideVO();
                        weatherOutsideVO.setDate(DateUtils.getDateToStr(weatherPowerFcClctDO.getDate()));
                        weatherOutsideVO.setHisFcTypeName("预测");
                        weatherOutsideVO.setDataSource("新能源站点气象");
                        weatherOutsideVO.setWeatherType(weatherPowerFcClctDO.getType());
                        weatherOutsideVO.setWeatherTypeName(
                            WeatherTypeEnum.getWeatherTypeEnumByType(weatherPowerFcClctDO.getType()).getName());
                        weatherOutsideVO.setCityId(cityId);
                        weatherOutsideVO.setCityName(cityService.findCityById(cityId).getCity());
                        weatherOutsideVO.setCountryId(weatherPowerFcClctDO.getCountryId());
                        List<BasePowerWeatherStationInfoDO> basePowerWeatherStationInfoDOS = map.get(
                            weatherPowerFcClctDO.getCountryId());
                        if(!CollectionUtils.isEmpty(basePowerWeatherStationInfoDOS)) {
                            weatherOutsideVO.setCountryName(basePowerWeatherStationInfoDOS.get(0).getStationName());
                        }
                        List<BigDecimal> bigDecimals = BasePeriodUtils.toList(weatherPowerFcClctDO,
                            Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
                        weatherOutsideVO.setWeather(bigDecimals);
                        vos.add(weatherOutsideVO);
                    }
                }
                break;
            //2是广东台气象数据
            case "2":
                //1是实际
                if(hisFcType.equals("1")){
                    List<WeatherCityHisDO> weatherCityHisDOs = weatherCityHisService.findWeatherCityHisDOs(cityId,
                        weatherType, startDate, endDate);
                    for(WeatherCityHisDO weatherCityHisDO:weatherCityHisDOs){
                        WeatherOutsideVO weatherOutsideVO = new WeatherOutsideVO();
                        weatherOutsideVO.setDate(DateUtils.getDateToStr(weatherCityHisDO.getDate()));
                        weatherOutsideVO.setWeatherType(weatherCityHisDO.getType());
                        weatherOutsideVO.setHisFcTypeName("实际");
                        weatherOutsideVO.setDataSource("实际");
                        weatherOutsideVO.setWeatherTypeName(
                            WeatherTypeEnum.getWeatherTypeEnumByType(weatherCityHisDO.getType()).getName());
                        weatherOutsideVO.setCityId(cityId);
                        weatherOutsideVO.setCityName(cityService.findCityById(cityId).getCity());
                        List<BigDecimal> bigDecimals = BasePeriodUtils.toList(weatherCityHisDO,
                            Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
                        weatherOutsideVO.setWeather(bigDecimals);
                        vos.add(weatherOutsideVO);
                    }
                    //2是预测
                }else if(hisFcType.equals("2")){
                    List<BaseWeatherDO> gdt = fcWeatherService.findFcWeatherData(cityId, weatherType, startDate,
                        endDate, "gdt");

                    for(BaseWeatherDO weatherCityFcDO:gdt){
                        WeatherOutsideVO weatherOutsideVO = new WeatherOutsideVO();
                        weatherOutsideVO.setDate(DateUtils.getDateToStr(weatherCityFcDO.getDate()));
                        weatherOutsideVO.setWeatherType(weatherCityFcDO.getType());
                        weatherOutsideVO.setWeatherTypeName(
                            WeatherTypeEnum.getWeatherTypeEnumByType(weatherCityFcDO.getType()).getName());
                        weatherOutsideVO.setCityId(cityId);
                        weatherOutsideVO.setCityName(cityService.findCityById(cityId).getCity());
                        weatherOutsideVO.setHisFcTypeName("预测");
                        weatherOutsideVO.setDataSource("广东台");
                        List<BigDecimal> bigDecimals = BasePeriodUtils.toList(weatherCityFcDO,
                            Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
                        weatherOutsideVO.setWeather(bigDecimals);
                        vos.add(weatherOutsideVO);
                    }


                }
                break;
            default:;
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(vos);
        return baseResp;
    }
    @RequestMapping(value = "/doStatWeatherPowerFcWarnInfoDO", method = RequestMethod.GET)
    public void doStatWeatherPowerFcWarnInfoDOS(String s) throws Exception{
        weatherPowerFcWarnInfoService.doStatWeatherPowerFcWarnInfoDOS(s);
    }



    private static final String BATCH_ID_ONE = "0200";

    private static final String BATCH_ID_TWO = "0800";

    private static final String BATCH_ID_THREE = "1400";

    private static final String BATCH_ID_FOUR = "2000";

    @RequestMapping(value = "/getWeatherPowerFcWarnInfo", method = RequestMethod.GET)
    public BaseResp<List<String>> getWeatherPowerFcWarnInfo(Date date) throws Exception{
        if(date == null){
            date = getSystemDate();
        }
        String yyyyMMddHHmmss = com.tsieframework.core.base.format.datetime.DateUtils.date2String(date,
            DateFormatType.SIMPLE_DATE_TIME_FORMAT_STR);
        String HHmm = yyyyMMddHHmmss.substring(8, 12);
        String batchId = "0";
        if (HHmm.compareTo(BATCH_ID_ONE) >= 0 && HHmm.compareTo(BATCH_ID_TWO) < 0) {
            batchId = "1";
        } else if (HHmm.compareTo(BATCH_ID_TWO) >= 0 && HHmm.compareTo(BATCH_ID_THREE) < 0) {
            batchId = "2";
        } else if (HHmm.compareTo(BATCH_ID_THREE) >= 0 && HHmm.compareTo(BATCH_ID_FOUR) < 0) {
            batchId = "3";
        } else if (HHmm.compareTo(BATCH_ID_FOUR) >= 0) {
            batchId = "4";
        }
        BaseResp baseResp = BaseResp.succResp();
        List<WeatherPowerFcWarnInfoDO> weatherPowerFcWarnInfo = weatherPowerFcWarnInfoService.getWeatherPowerFcWarnInfo(
            null, batchId, date);
        if(!CollectionUtils.isEmpty(weatherPowerFcWarnInfo)){
            List<String> list = weatherPowerFcWarnInfo.stream().map(WeatherPowerFcWarnInfoDO::getWarningInfo)
                .collect(Collectors.toList());
            baseResp.setData(list);
        }
        return baseResp;
    }

    @RequestMapping(value = "/algorithm/days")
    public BaseResp findWeather(String cityId, Date startDate, Date endDate, Integer type, String algorithmId, String caliberId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        cityId = cityService.findWeatherCityId(cityId);
        List<WeatherCityHisDO> weatherCityHisVOS = weatherCityHisService.findWeatherCityHisDOs(cityId, type, startDate, endDate);
        List<WeatherCityFcDO> fcVOS = weatherCityFcService.findWeatherCityHisDOs(Arrays.asList(cityId), type, startDate, endDate);
        if (weatherCityHisVOS.size() < 1 && fcVOS.size() < 1) {
            return new BaseResp("T706");
        }
        Map<Date, WeatherCityHisDO> realMap = weatherCityHisVOS.stream().collect(Collectors.toMap(WeatherCityHisDO::getDate, Function.identity(), (key1, key2) -> key2));
        Map<Date, List<WeatherCityFcDO>> fcMap = fcVOS.stream()
            .collect(Collectors.groupingBy(WeatherCityFcDO::getDate));
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        List<BigDecimal> his = new ArrayList<>();
        List<BigDecimal> fc = new ArrayList<>();
        WeatherDataResp dataResp = new WeatherDataResp();
        for (Date date : dateList) {
            WeatherCityHisDO hisVO = realMap.get(date);
            if (hisVO != null) {
                his.addAll(BasePeriodUtils.toList(hisVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            } else {
                his.addAll(LoadCalUtil.getNullList(Constants.LOAD_CURVE_POINT_NUM));
            }
            List<WeatherCityFcDO> fcVO = fcMap.get(date);
            if (fcVO != null) {
                fc.addAll(BasePeriodUtils.toList(fcVO.get(0), Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            } else {
                fc.addAll(LoadCalUtil.getNullList(Constants.LOAD_CURVE_POINT_NUM));
            }
        }
        dataResp.setForecast(fc);
        dataResp.setReal(his);
        baseResp.setData(dataResp);
        return baseResp;
    }

    /*@Autowired
    WeatherStationCityAveDayService weatherStationCityAveDayService;

    @RequestMapping(value = "/count")
    public BaseResp countAveWeather(Date startDate, Date endDate) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        weatherStationCityAveDayService.calculateAveFcDay(Constants.GUANGZHOU_ID, startDate, endDate);
        weatherStationCityAveDayService.calculateAveHisDay(Constants.GUANGZHOU_ID, startDate, endDate);
        return baseResp;
    }*/

}
