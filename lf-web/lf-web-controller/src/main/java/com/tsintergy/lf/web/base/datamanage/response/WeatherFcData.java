package com.tsintergy.lf.web.base.datamanage.response;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;
import lombok.Data;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2025-05-07
 * @since 1.0.0
 */
@Data
public class WeatherFcData {

    private Date date;

    /**
     * 目标预测日期使用的最新的一次实际气象日期
     */
    private Date weatherDate;

    /**
     * 城市ID
     */
    private String cityId;

    /**
     * 对应城市气象
     */
    private String weatherCityId;

    /**
     * 算法id
     */
    private String algorithmId;


    /**
     * 口径id
     */
    private String caliberId;

    /**
     * 气象类型
     */
    private Integer type;

    /**
     * 气象类型
     */
    private Integer batchId;

    /**
     * 创建时间
     */
    private Timestamp createtime;

    /**
     * 更新时间
     */
    private Timestamp updatetime;


    /**
     * 创建时间
     */
    private String weatherCreateTime;

    /**
     * 预测时使用的当日气象数据是否正常； 确为当日的实际气象 true   使用的t-n气象 false
     */
    private String normal;


    private List<BigDecimal> weatherList;

}
