package com.tsintergy.lf.web.base.load.controller;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.junrar.exception.RarException;
import com.tsieframework.cloud.security.serviceapi.system.enums.TokenType;
import com.tsieframework.cloud.security.web.businesslog.annotation.OperateLog;
import com.tsieframework.cloud.security.web.common.security.generator.annotations.MenuResource;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.ColumnUtil;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.api.PrecisionService;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.DateScopeType;
import com.tsintergy.lf.serviceapi.base.evalucation.api.DeviationLoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService;
import com.tsintergy.lf.serviceapi.base.forecast.api.ForecastService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.dto.BatchForecastDTO;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.*;
import com.tsintergy.lf.serviceapi.base.load.dto.*;
import com.tsintergy.lf.serviceapi.base.load.pojo.*;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemData;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.bean.Result;
import com.tsintergy.lf.serviceapi.base.weather.pojo.BaseWeatherDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceimpl.common.impl.CoreConfigInfo;
import com.tsintergy.lf.serviceimpl.common.util.ExcelUtil;
import com.tsintergy.lf.web.base.check.response.CommonResp;
import com.tsintergy.lf.web.base.controller.BaseController;
import com.tsintergy.lf.web.base.forecast.controller.AlgorithmController;
import com.tsintergy.lf.web.base.load.request.*;
import com.tsintergy.lf.web.base.load.response.FcAndRealLoadResp;
import com.tsintergy.lf.web.base.load.response.LoadFeatureDayResp;
import com.tsintergy.lf.web.base.load.response.MonitorResp;
import com.tsintergy.lf.web.base.load.response.StabilityResp;
import com.tsintergy.lf.web.base.util.CheckoutFileTypeSecurity;
import com.tsintergy.lf.web.base.util.CompressFileUtils;
import com.tsintergy.lf.web.base.util.MapFormatUtil;
import com.tsintergy.lf.web.base.util.PropertiesUtil;
import com.tsintergy.lf.web.base.util.request.CommonRequest;
import com.tsintergy.lf.web.base.weather.support.TxtWeatherResolver;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 实际负荷控制类
 *
 * @date: 18-2-6 下午12:03
 * @author: angel
 **/
@RequestMapping("/load")
@RestController
public class LoadHisController extends BaseController {

    private Logger logger = LogManager.getLogger(AlgorithmController.class);

    @Autowired
    private CoreConfigInfo coreConfigInfo;

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    private ForecastService forecastService;

    @Autowired
    private LoadFeatureCityDayHisService loadFeatureCityDayHisService;

    @Autowired
    private LoadFeatureCityWeekHisService loadFeatureCityWeekHisService;

    @Autowired
    private LoadFeatureCityMonthHisService loadFeatureCityMonthHisService;

    @Autowired
    private LoadFeatureCityQuarterHisService loadFeatureCityQuarterHisService;

    @Autowired
    private LoadFeatureCityYearHisService loadFeatureCityYearHisService;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private WeatherCityFcService weatherCityFcService;

    @Autowired
    private CityService cityService;

    @Autowired
    private LoadWeatherService loadWeatherService;

    @Autowired
    private LoadIndustryService loadIndustryService;

    @Autowired
    private DeviationLoadCityFcService deviationLoadCityFcService;

    @Autowired
    private AlgorithmService algorithmService;

    @Autowired
    private LoadDecomposeCityWeekService loadDecomposeCityWeekService;

    @Autowired
    private PrecisionService precisionService;

    @Autowired
    private LoadDecomposeCityWeekStabilityService loadDecomposeCityWeekStabilityService;

    @Autowired
    private LoadCompareService loadCompareService;

    @Autowired
    private LoadFeatureCityDayFcService loadFeatureCityDayFcService;

    @Autowired
    SettingSystemService settingSystemService;

    @Autowired
    SendLoadFcBasicService sendLoadFcBasicService;



    /**
     * 负荷
     */
    private final Integer LOAD = 1;

    /**
     * 温度
     */
    private final Integer TEMPERATURE = 2;

    /**
     * 误差
     */
    private final Integer DEVIATION = 3;

    /**
     * 降水
     */
    private final Integer RAIN = 3;

    /**
     * 全省24小时实时监控
     */
    @RequestMapping(value = "/monitor", method = RequestMethod.GET)
    @MenuResource(name = "全省24小时实时监控", type = TokenType.API, parentMenuPath = Constants.INDEX_PARENT)
    public Object monitor(Integer type, String caliberId) throws Exception {
        //获取session中的日期和登陆用户的cityId
        Date today = this.getSystemDate();
        String cityId = this.getLoginCityId();
        if (StringUtils.isEmpty(caliberId)) {
            caliberId = this.getCaliberId();
        }
        BaseResp<MonitorResp> response = BaseResp.succResp();
        MonitorResp monitorResp = new MonitorResp();
        CityDO cityVO = cityService.findCityById(cityId);
        String belongId = cityId;
        if (cityVO.getType() == 2) {
            belongId = cityVO.getBelongId();
        }
        List<String> cityIds = cityService.findCityIdsByBelongId(belongId);
        if (LOAD.equals(type)) {
            List<CityValueDTO> cityValueDTOS = loadCityHisService
                .find24LoadCityByDateAndCityIds(today, cityIds, caliberId);
            monitorResp.setTimeLine(MapFormatUtil.format2map(cityValueDTOS));
        } else if (TEMPERATURE.equals(type)) {
            List<CityValueDTO> cityValueDTOS = weatherCityHisService.find24WeahterCityByDateAndCityIds(today, cityIds, WeatherEnum.TEMPERATURE.value());
            monitorResp.setTimeLine(MapFormatUtil.format2map(cityValueDTOS));
        } else if (DEVIATION.equals(type)) {
            List<CityValueDTO> cityValueDTOS = deviationLoadCityFcService
                .find24DeviationLoadCityFcByDateAndCityIds(today, caliberId, cityIds);
            monitorResp.setTimeLine(MapFormatUtil.format2map(cityValueDTOS));
        } else {
            throw TsieExceptionUtils.newBusinessException("T701");
        }
        response.setData(monitorResp);
        return response;
    }


    /**
     * 全省24小时实时监控
     */
    @RequestMapping(value = "/monitorV2", method = RequestMethod.GET)
    @MenuResource(name = "全省24小时实时监控", type = TokenType.API, parentMenuPath = Constants.INDEX_PARENT)
    public Object monitorV2(String caliberId) throws Exception {
        //获取session中的日期和登陆用户的cityId
        Date today = this.getSystemDate();
        String cityId = this.getLoginCityId();
        if (StringUtils.isEmpty(caliberId)) {
            caliberId = this.getCaliberId();
        }
        BaseResp<MonitorResp> response = BaseResp.succResp();
        MonitorResp monitorResp = new MonitorResp();
        CityDO cityVO = cityService.findCityById(cityId);
        String belongId = cityId;
        if (cityVO.getType() == 2) {
            belongId = cityVO.getBelongId();
        }
        List<String> cityIds = cityService.findCityIdsByBelongId(belongId);
        List<CityValueDTO> cityValueDTOS = loadCityHisService.find24LoadCityByDateAndCityIds(today, cityIds, caliberId);
        cityValueDTOS.forEach(t -> {
            t.setCity(t.getCity() + "市");
        });
        monitorResp.setTimeLine(MapFormatUtil.format2mapSortByCitys(cityValueDTOS));
        response.setData(monitorResp);
        return response;
    }


    /**
     * 全省24小时实时监控
     */
    @RequestMapping("/monitor_v3")
    public BaseResp monitor_v3(Integer type) throws Exception {

        type = type == null ? LOAD : type;
        Date today = super.getSystemDate();
        String caliberId = super.getCaliberId();
        List<String> cityIds = cityService.findCityIdsByBelongId(Constants.PROVINCE);

        List<CityValueDTO> cityValueDTOS = null;
        if (LOAD.equals(type)) {
            cityValueDTOS = loadCityHisService
                    .find24LoadCityByDateAndCityIds(today, cityIds, caliberId);
        } else if (TEMPERATURE.equals(type)) {
            cityValueDTOS = weatherCityHisService.find24WeahterCityByDateAndCityIds(today, cityIds,WeatherEnum.TEMPERATURE.value());
        } else if (RAIN.equals(type)) {
            cityValueDTOS = weatherCityHisService.find24WeahterCityByDateAndCityIds(today, cityIds,WeatherEnum.RAINFALL.value());
        }

        List<CityLoadDTO> cityLoadDTOS = loadCityHisService.find24CityLoadDTO(today,cityIds,caliberId);

        List<AreaLoadRateDTO> areaLoadRateDTOS = loadCityHisService.find24AreaLoadRateDTO(today,caliberId);

        Date24LoadDTO date24LoadDTOS = loadCityHisService.findDate24LoadDTOS(today,Constants.PROVINCE,caliberId);

        List<CityMonitorDTO> timeLine = wrapCityMonitorDTOS(cityLoadDTOS,areaLoadRateDTOS,cityValueDTOS,date24LoadDTOS);
        for(int i=timeLine.size()-1;i>0;i--){
            CityMonitorDTO cityMonitorDTO = timeLine.get(i);
            if(cityMonitorDTO == null ){
                continue;
            }else {
                List<CityPointLoadDTO> cityList = cityMonitorDTO.getCityList();
                if(CollectionUtils.isEmpty(cityList)){
                    continue;
                }else {
                    long count = cityList.stream().filter(t -> t.getTodayHisLoad() == null).count();
                    if(count == cityList.size()){
                        continue;
                    }else {
                        timeLine = timeLine.subList(0,i+1);
                        break;
                    }
                }
            }

        }
        CityMonitorDTOV3 cityMonitorDTOV3 = new CityMonitorDTOV3(timeLine);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(cityMonitorDTOV3);
        return baseResp;
    }

    private List<CityMonitorDTO> wrapCityMonitorDTOS(List<CityLoadDTO> cityLoadDTOS, List<AreaLoadRateDTO> areaLoadRateDTOS, List<CityValueDTO> cityValueDTOS,Date24LoadDTO date24LoadDTOS) {

        List<CityMonitorDTO> result = new ArrayList<>();
        int hour = Integer.valueOf(new SimpleDateFormat("HH").format(new Date()));
        Map<String, List<BigDecimal>> cityValueMap = cityValueDTOS.stream().collect(Collectors.toMap(CityValueDTO::getCity, CityValueDTO::getValue));


        BigDecimal maxFc = null;
        if (!CollectionUtils.isEmpty(date24LoadDTOS.getFcLoads())) {
            maxFc = BasePeriodUtils.getMaxMinAvg(date24LoadDTOS.getFcLoads(), 4).get(BasePeriodUtils.LOAD_MAX);
        }

        for (int i = 0; i < hour + 1; i++) {
            List<CityPointLoadDTO> cityList = new ArrayList<>();
            List<AreaPointLoadDTO> areaList = new ArrayList<>();
            for (CityLoadDTO cityLoadDTO : cityLoadDTOS) {
                String cityName = cityLoadDTO.getCityName();

                CityPointLoadDTO cityPointLoadDTO = new CityPointLoadDTO(cityLoadDTO.getCityName() +  "市",
                        cityValueMap.get(cityName) == null ? null : cityValueMap.get(cityName).get(i),
                        cityLoadDTO.getTodayHisLoads() == null ? null : cityLoadDTO.getTodayHisLoads().get(i),
                        cityLoadDTO.getYesterdayHisLoads() == null ? null : cityLoadDTO.getYesterdayHisLoads().get(i),
                        cityLoadDTO.getTodayFcLoads() == null ? null : cityLoadDTO.getTodayFcLoads().get(i),
                        cityLoadDTO.getTemperatures() == null ? null : cityLoadDTO.getTemperatures().get(i));
                cityList.add(cityPointLoadDTO);
            }

            for (AreaLoadRateDTO areaLoadRateDTO : areaLoadRateDTOS) {
                List<BigDecimal> loadRates = areaLoadRateDTO.getLoadRates();
                BigDecimal bigDecimal = loadRates.get(i);
                AreaPointLoadDTO areaPointLoadDTO = new AreaPointLoadDTO(areaLoadRateDTO.getAreaName(),
                    bigDecimal == null ? null:bigDecimal.multiply(new BigDecimal(100)));
                areaList.add(areaPointLoadDTO);
            }

            BigDecimal provinceLoad = date24LoadDTOS.getHisLoads().get(i);
            BigDecimal loadRate = null;
            if (provinceLoad != null && maxFc != null) {
                if(maxFc.compareTo(BigDecimal.ZERO) != 0){
                    loadRate = provinceLoad.divide(maxFc,4,BigDecimal.ROUND_HALF_UP);
                }
            }
            CityLoadPointRateDTO cityLoadPointRateDTO = new CityLoadPointRateDTO();
            if (loadRate != null) {
                cityLoadPointRateDTO = new CityLoadPointRateDTO(provinceLoad, maxFc,
                    loadRate.multiply(new BigDecimal(100)));
            } else {
                cityLoadPointRateDTO = new CityLoadPointRateDTO(BigDecimal.ZERO,BigDecimal.ZERO,BigDecimal.ZERO);
            }

            CityMonitorDTO cityMonitorDTO = new CityMonitorDTO(cityList,areaList,cityLoadPointRateDTO);
            result.add(cityMonitorDTO);
        }
        return result;
    }

    /**
     * 预测监控
     * @param type
     * @return
     */
    @RequestMapping(value = "/fc_monitor",method = RequestMethod.GET)
    public BaseResp fcMonitor(Integer type) throws Exception {
        //今日预测
        Date date = super.getSystemDate();
        if (type != null && type.equals(2)) {
            //明日预测
            date = DateUtils.addDays(date,1);
        }
        List<CurveViewDTO> curveViewDTOS = loadCityHisService.findCurveViewDTOS(date,super.getCaliberId(),Constants.PROVINCE);
        List<FeatureStatisDTO> featureStatisDTOS = loadFeatureCityDayFcService.findFeatureStatisDTOS(date,super.getCaliberId(),Constants.PROVINCE);
        FcMonitorDTO fcMonitorDTO = new FcMonitorDTO();
        fcMonitorDTO.setCurveView(curveViewDTOS);
        fcMonitorDTO.setFcFeatureStatis(featureStatisDTOS);

        BaseResp baseResp = BaseResp.succResp();

        baseResp.setData(fcMonitorDTO);
        return baseResp;
    }

    /**
     * get the load of forecast and real
     */
    @RequestMapping(value = "/monitor/monitorCity", method = RequestMethod.GET)
    @MenuResource(name = "获取实际负荷和预测负荷", type = TokenType.API, parentMenuPath = {Constants.INDEX_PARENT,
        Constants.MANUAL_PARENT, Constants.PREDICTION_EVALUATION_PARENT})
    public Object monitorCity(MonitorRequest monitorRequest) throws Exception {
        if (!StringUtils.isNotBlank(monitorRequest.getCityId())) {
            monitorRequest.setCityId(this.getLoginCityId());
        }
        if (monitorRequest.getDate() == null) {
            Date today = this.getSystemDate();
            monitorRequest.setDate(today);
        }
        if (StringUtils.isBlank(monitorRequest.getCaliberId())) {
            monitorRequest.setCaliberId(this.getCaliberId());
        }
        FcAndRealLoadResp fcAndRealLoadResp = new FcAndRealLoadResp();
        BaseResp<FcAndRealLoadResp> response = BaseResp.succResp();
        if (!StringUtils.isNotBlank(monitorRequest.getAlgorithmId())) {
            //人工修正上报算法id
            String reportAlgorithmId = loadCityFcService.getReportAlgorithmId(monitorRequest.getCityId(), monitorRequest.getDate(), monitorRequest.getCaliberId());
            if(null != reportAlgorithmId){
                monitorRequest.setAlgorithmId(reportAlgorithmId);
                fcAndRealLoadResp.setForecast(loadCityFcService.findReportLoadCityFcDO(monitorRequest.getDate(), monitorRequest.getCityId(), monitorRequest.getCaliberId(), null,true ));
            }else{
                //推荐算法id
                SystemData systemSetting = settingSystemService.getSystemSetting();
                monitorRequest.setAlgorithmId(systemSetting.getProvinceNormalAlgorithm());
                fcAndRealLoadResp.setForecast(loadCityFcService.findLoadCityFcDO(monitorRequest.getDate(), monitorRequest.getCityId(), monitorRequest.getCaliberId(), monitorRequest.getAlgorithmId()));
            }
            //以上两个测试值都为null，则查随机有值的算法
            if(!StringUtils.isNotBlank(monitorRequest.getAlgorithmId())){
                List<LoadCityFcDO> loadCityFcDOS = loadCityFcService.listLoadCityFc(monitorRequest.getCityId(), monitorRequest.getCaliberId(), monitorRequest.getDate(), monitorRequest.getDate(), null);
                monitorRequest.setAlgorithmId(loadCityFcDOS.get(0).getAlgorithmId());
                fcAndRealLoadResp.setForecast(BasePeriodUtils.toList(loadCityFcDOS.get(0), Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            }

        }else {
            //若查询系统负荷预测或人工修正，则将口径设置为10
            if (AlgorithmEnum.DP_FORECAST_2.getId().equals(monitorRequest.getAlgorithmId()) || AlgorithmEnum.DP_FORECAST_2_MODIFY.getId().equals(monitorRequest.getAlgorithmId())){
                monitorRequest.setCaliberId(Constants.SYSTEM_LOAD_CALIBER);
                if (AlgorithmEnum.DP_FORECAST_2_MODIFY.getId().equals(monitorRequest.getAlgorithmId())){
                    monitorRequest.setAlgorithmId(AlgorithmEnum.FORECAST_MODIFY.getId());
                }
            }
            fcAndRealLoadResp.setForecast(loadCityFcService.findLoadCityFcDO(monitorRequest.getDate(), monitorRequest.getCityId(), monitorRequest.getCaliberId(), monitorRequest.getAlgorithmId()));
        }

        if (Objects.nonNull(monitorRequest.getStartTime())){
            List<BatchForecastDTO> batchForecast = forecastService.getBatchForecast(monitorRequest.getCityId(), monitorRequest.getCaliberId(), monitorRequest.getAlgorithmId(), monitorRequest.getDate(), monitorRequest.getStartTime(), monitorRequest.getEndTime());
            if(!CollectionUtils.isEmpty(batchForecast)){
                //最后一批预测
                BatchForecastDTO batchForecastDTO = batchForecast.get(0);
                fcAndRealLoadResp.setForecast(batchForecastDTO.getData());
            }
        }
        fcAndRealLoadResp.setReal(loadCityHisService
            .findLoadCityHisDO(monitorRequest.getDate(), monitorRequest.getCityId(), monitorRequest.getCaliberId()));

//        fcAndRealLoadResp.setAlgorithm(algorithmService.getAlgorithmDOById(monitorRequest.getAlgorithmId()).getAlgorithmCn());
//
//        if (AlgorithmEnum.DP_FORECAST_2.getId().equals(monitorRequest.getAlgorithmId())){
//            fcAndRealLoadResp.setAlgorithm(AlgorithmEnum.DP_FORECAST_2.getDescription());
//        }else
        if (AlgorithmEnum.DP_FORECAST_2_MODIFY.getId().equals(monitorRequest.getAlgorithmId())){
            fcAndRealLoadResp.setAlgorithm(AlgorithmEnum.DP_FORECAST_2_MODIFY.getDescription());
        } else{
            fcAndRealLoadResp.setAlgorithm(algorithmService.getAlgorithmDOById(monitorRequest.getAlgorithmId()).getAlgorithmCn());
        }
        response.setData(fcAndRealLoadResp);
        return response;
    }


    /**
     * 查询历史负荷特性
     */
    @RequestMapping(value = "/feature", method = RequestMethod.GET)
    @MenuResource(name = "获取负荷特性", type = TokenType.API, parentMenuPath = Constants.LOAD_ANALYSIS_PARENT)
    public Object loadFeature(LoadFeatureRequest loadFeatureRequest) throws Exception {
        Date today = this.getSystemDate();
        int count = 30; // 默认数据量

        if (!StringUtils.isNotBlank(loadFeatureRequest.getCityId()) || loadFeatureRequest.getCityId()
            .equals("undefined")) {
            loadFeatureRequest.setCityId(this.getLoginCityId());
        }
        if (StringUtils.isBlank(loadFeatureRequest.getCaliberId())) {
            loadFeatureRequest.setCaliberId(this.getCaliberId());
        }

        List<LoadFeatureExtendDTO> loadFeatureExtendDTOS = null;
        //日维度的计算
        if (DateScopeType.DAY.value().equals(loadFeatureRequest.getType())) {
            if (loadFeatureRequest.getStartDate() == null) {
                loadFeatureRequest.setStartDate(DateUtil.getMoveDay(today, -1 * count)); // 默认前30天
            }
            if (loadFeatureRequest.getEndDate() == null) {
                loadFeatureRequest.setEndDate(today);
            }
            loadFeatureExtendDTOS = loadFeatureCityDayHisService
                .findLoadFeatureExtendDTOS(loadFeatureRequest.getCityId(), loadFeatureRequest.getStartDate(),
                    loadFeatureRequest.getEndDate(), loadFeatureRequest.getCaliberId());
        }
        //周维度
        if (DateScopeType.WEEK.value().equals(loadFeatureRequest.getType())) {
            if (loadFeatureRequest.getStartDate() == null) {
                loadFeatureRequest.setStartDate(DateUtil.getMoveDay(today, -7 * count)); // 默认前30周
            }
            if (loadFeatureRequest.getEndDate() == null) {
                loadFeatureRequest.setEndDate(today);
            }
            loadFeatureExtendDTOS = loadFeatureCityWeekHisService
                .findLoadFeatureExtendDTOS(loadFeatureRequest.getCityId(), loadFeatureRequest.getStartDate(),
                    loadFeatureRequest.getEndDate(), loadFeatureRequest.getCaliberId());
        }
        //月维度
        if (DateScopeType.MONTH.value().equals(loadFeatureRequest.getType())) {
            if (loadFeatureRequest.getStartDate() == null) {
                loadFeatureRequest.setStartDate(DateUtil.getMoveDay(today, -30 * count)); // 默认前30月
            }
            if (loadFeatureRequest.getEndDate() == null) {
                loadFeatureRequest.setEndDate(today);
            }
            loadFeatureExtendDTOS = loadFeatureCityMonthHisService
                .findLoadFeatureExtendDTOS(loadFeatureRequest.getCityId(), loadFeatureRequest.getStartDate(),
                    loadFeatureRequest.getEndDate(), loadFeatureRequest.getCaliberId());
        }
        //季维度
        if (DateScopeType.QUARTER.value().equals(loadFeatureRequest.getType())) {
            if (loadFeatureRequest.getStartDate() == null) {
                loadFeatureRequest.setStartDate(DateUtil.getMoveDay(today, -90 * count)); // 默认前30季
            }
            if (loadFeatureRequest.getEndDate() == null) {
                loadFeatureRequest.setEndDate(today);
            }
            loadFeatureExtendDTOS = loadFeatureCityQuarterHisService
                .findLoadFeatureExtendDTOS(loadFeatureRequest.getCityId(), loadFeatureRequest.getStartDate(),
                    loadFeatureRequest.getEndDate(), loadFeatureRequest.getCaliberId());
        }
        //年维度
        if (DateScopeType.YEAR.value().equals(loadFeatureRequest.getType())) {
            if (loadFeatureRequest.getStartDate() == null) {
                loadFeatureRequest.setStartDate(DateUtil.getMoveDay(today, -365 * count)); // 默认前30年
            }
            if (loadFeatureRequest.getEndDate() == null) {
                loadFeatureRequest.setEndDate(today);
            }
            loadFeatureExtendDTOS = loadFeatureCityYearHisService
                .findLoadFeatureExtendDTOS(loadFeatureRequest.getCityId(), loadFeatureRequest.getStartDate(),
                    loadFeatureRequest.getEndDate(), loadFeatureRequest.getCaliberId());
        }

        if (loadFeatureExtendDTOS == null || loadFeatureExtendDTOS.size() == 0) {
            return new BaseResp("T706");
        } else {
            BaseResp<LoadFeatureDayResp> response = BaseResp.succResp("查询成功");
            LoadFeatureDayResp loadFeatureDayResp = new LoadFeatureDayResp();
            loadFeatureDayResp.setFeatureList(loadFeatureExtendDTOS);
            response.setData(loadFeatureDayResp);
            return response;
        }

    }

   
    /**
     * query load history
     */
    @RequestMapping(value = "/history", method = RequestMethod.GET)
    @MenuResource(name = "获取负荷历史数据", type = TokenType.API, parentMenuPath = Constants.LOAD_ANALYSIS_PARENT)
    public Object getLoadHistory(@Validated CommonRequest commonRequest, BindingResult result) throws Exception {
        validateFormValue(result);
        if (StringUtils.isEmpty(commonRequest.getCityId()) || commonRequest.getCityId().equals("undefined")) {
            commonRequest.setCityId(this.getLoginCityId());
        }
        if (StringUtils.isBlank(commonRequest.getCaliberId())) {
            commonRequest.setCaliberId(this.getCaliberId());
        }
        BaseResp<CommonResp<LoadHisDataDTO>> response = BaseResp.succResp("查询成功");
        CommonResp<LoadHisDataDTO> commonResp = new CommonResp<>();
        commonResp.setDataList(loadCityHisService
            .findLoadCityVOsByCityId(commonRequest.getStartDate(), commonRequest.getEndDate(),
                commonRequest.getCityId(), commonRequest.getCaliberId()));
        response.setData(commonResp);
        return response;
    }

    /**
     * 数据管理----数据修复
     */
    @RequestMapping(value = "/historyAndErrorInfo", method = RequestMethod.GET)
    public Object getLoadHistoryAndErrorInfo(@Validated CommonRequest commonRequest, BindingResult result) {
        validateFormValue(result);
        if (StringUtils.isEmpty(commonRequest.getCityId()) || commonRequest.getCityId().equals("undefined")) {
            commonRequest.setCityId(this.getLoginCityId());
        }
        if (StringUtils.isBlank(commonRequest.getCaliberId())) {
            commonRequest.setCaliberId(this.getCaliberId());
        }
        BaseResp<List<LoadRepairInfoDTO>> baseResp = BaseResp.succResp();
        try {
            List<LoadRepairInfoDTO> loadRepairInfoDTOS = loadCompareService
                .findLoadRepairInfoDTOS(commonRequest.getCityId(), commonRequest.getCaliberId(),
                    commonRequest.getStartDate(), commonRequest.getEndDate());
            if (loadRepairInfoDTOS == null || loadRepairInfoDTOS.size() < 1) {
                throw TsieExceptionUtils.newBusinessException("T706");
            }
            baseResp.setData(loadRepairInfoDTOS);
            return baseResp;
        } catch (BusinessException b) {
            logger.error(b);
            throw b;
        } catch (Exception e) {
            logger.error("查询修复数据和异常数据信息错误", e);
            throw TsieExceptionUtils.newBusinessException("02I20180001");
        }
    }

    /**
     * 数据管理----数据修复前后比对
     */
    @RequestMapping(value = "/repairCompare", method = RequestMethod.GET)
    public Object getLoadRepair(@RequestParam(value = "cityId") String cityId,
        @RequestParam(value = "caliberId") String caliberId,
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8") Date date) {
        if (StringUtils.isEmpty(cityId) || cityId.equals("undefined")) {
            cityId = this.getLoginCityId();
        }
        if (StringUtils.isBlank(caliberId) || caliberId.equals("undefined")) {
            caliberId = this.getCaliberId();
        }
        if (date == null) {
            date = DateUtils.addDays(new Date(), -1);
        }
        BaseResp<LoadCompareDTO> baseResp = BaseResp.succResp("查询成功");
        LoadCompareDTO loadCompareDTO = null;
        try {
            loadCompareDTO = loadCompareService.findLoadCompareDTO(date, cityId, caliberId);
            if (loadCompareDTO == null) {
                baseResp = BaseResp.failResp();
                baseResp.setRetCode("T706");
                return baseResp;
            }
        } catch (BusinessException e) {
            logger.error(e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            logger.error("获取修正前后数据异常", e);
            throw TsieExceptionUtils.newBusinessException("02I20180001", e);
        }
        baseResp.setData(loadCompareDTO);
        return baseResp;
    }

    /**
     * 数据修复
     */
    @RequestMapping(value = "/error", method = RequestMethod.GET)
    public Object getLoadError(@Validated CommonRequest commonRequest, BindingResult result) {
        validateFormValue(result);
        if (StringUtils.isEmpty(commonRequest.getCityId()) || commonRequest.getCityId().equals("undefined")) {
            commonRequest.setCityId(this.getLoginCityId());
        }
        if (StringUtils.isBlank(commonRequest.getCaliberId())) {
            commonRequest.setCaliberId(this.getCaliberId());
        }
        BaseResp<LoadErrorDTO> baseResp = BaseResp.succResp();
        try {
            LoadErrorDTO loadErrorDTO = loadCompareService
                .findLoadErroDTO(commonRequest.getStartDate(), commonRequest.getEndDate(), commonRequest.getCityId(),
                    commonRequest.getCaliberId());
            if (loadErrorDTO == null) {
                throw TsieExceptionUtils.newBusinessException("T706");
            }
            baseResp.setData(loadErrorDTO);
        } catch (BusinessException e) {
            logger.error(e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            logger.error("查询异常数据错误...", e);
            throw TsieExceptionUtils.newBusinessException("02I20180001", e);
        }
        return baseResp;
    }


    /**
     * 导出负荷历史数据
     */
    @RequestMapping(value = "/history/export", method = RequestMethod.GET)
    @OperateLog(operate = "导出历史负荷")
    @MenuResource(name = "导出历史负荷", type = TokenType.API, parentMenuPath = Constants.DATA_MANAGEMENT_PARENT)
    public Object getLoadHistoryExcel(@Validated CommonRequest commonRequest, BindingResult result) throws Exception {
        validateFormValue(result);
        if (StringUtils.isEmpty(commonRequest.getCityId()) || commonRequest.getCityId().equals("undefined")) {
            commonRequest.setCityId(this.getLoginCityId());
        }
        if (StringUtils.isEmpty(commonRequest.getCaliberId())) {
            commonRequest.setCaliberId(this.getCaliberId());
        }
        BaseResp response = BaseResp.succResp("查询成功");
        List<LoadHisDataDTO> loads = loadCityHisService
            .findLoadCityVOsByCityId(commonRequest.getStartDate(), commonRequest.getEndDate(),
                commonRequest.getCityId(), commonRequest.getCaliberId());
        String[] heads = PropertiesUtil.getLabel("title").split(",");
        InputStream in = ExcelUtil.getExcel("实际负荷", heads, loads);
        this.getResponse().setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        //格式化文件名使用中文
        String fileName = URLEncoder.encode("数据管理", "UTF-8");
//        String fileName = new String("数据管理".getBytes(),"ISO-8859-1");
        this.getResponse().setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        OutputStream out = new BufferedOutputStream(this.getResponse().getOutputStream());
        byte[] data = new byte[1024];
        int len = 0;
        while (-1 != (len = in.read(data, 0, data.length))) {
            out.write(data, 0, len);
        }
        if (in != null) {
            in.close();
        }
        if (out != null) {
            out.close();
        }
        return response;
    }

    /**
     * query load-weather at a day
     */
    @RequestMapping(value = "/weather", method = RequestMethod.GET)
    @MenuResource(name = "获取气象敏感负荷", type = TokenType.API, parentMenuPath = Constants.LOAD_ANALYSIS_PARENT)
    public Object getLoadWeather(@Validated SingleLoadRequest singleLoadRequest, BindingResult result)
        throws Exception {
        /* todo wangchen 暂时注释   该功能尚未开放
        validateFormValue(result);
        if (StringUtils.isEmpty(singleLoadRequest.getCityId()) || singleLoadRequest.getCityId().equals("undefined")) {
            singleLoadRequest.setCityId(this.getLoginCityId());
        }
        if (StringUtils.isEmpty(singleLoadRequest.getCaliberId()) || singleLoadRequest.getCaliberId().equals("undefined")) {
            singleLoadRequest.setCaliberId(this.getCaliberId());
        }
        BaseResp<LoadWeatherDTO> response = BaseResp.succResp("查询成功");
        response.setData(loadWeatherService.findLoadWetherVOSbyDate(singleLoadRequest.getCityId(), singleLoadRequest.getDate(), singleLoadRequest.getCaliberId()));
        return response;*/
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setRetCode("T708");
        baseResp.setRetMsg("该功能暂未开放");
        return baseResp;
    }

    /**
     * query load-industry at a day
     */
    @RequestMapping(value = "/industry", method = RequestMethod.GET)
    @MenuResource(name = "获取行业负荷", type = TokenType.API, parentMenuPath = Constants.LOAD_ANALYSIS_PARENT)
    public Object getLoadIndustry(@Validated SingleLoadRequest singleLoadRequest, BindingResult result)
        throws Exception {
        validateFormValue(result);
        if (StringUtils.isEmpty(singleLoadRequest.getCityId()) || singleLoadRequest.getCityId().equals("undefined")) {
            singleLoadRequest.setCityId(this.getLoginCityId());
        }
        if (StringUtils.isEmpty(singleLoadRequest.getCaliberId())) {
            singleLoadRequest.setCaliberId(this.getCaliberId());
        }
        BaseResp<CommonResp<LoadCommonDTO<String>>> response = BaseResp.succResp("查询成功");
        CommonResp<LoadCommonDTO<String>> commonResp = new CommonResp<LoadCommonDTO<String>>();
        commonResp.setDataList(loadIndustryService
            .findLoadIndustryVOSbyDate(singleLoadRequest.getCityId(), singleLoadRequest.getDate(),
                singleLoadRequest.getCaliberId()));
        response.setData(commonResp);
        return response;
    }

    /**
     * 查询最大负荷日曲线
     */
    @RequestMapping(value = "/max", method = RequestMethod.GET)
    @MenuResource(name = "查询最大负荷日曲线", type = TokenType.API, parentMenuPath = Constants.LOAD_ANALYSIS_PARENT)
    public Object getMaxLoad(@Validated MaxLoadRequest maxLoadRequest, BindingResult result) throws Exception {
        validateFormValue(result);
        if (StringUtils.isEmpty(maxLoadRequest.getCityId()) || maxLoadRequest.getCityId().equals("undefined")) {
            maxLoadRequest.setCityId(this.getLoginCityId());
        }
        if (StringUtils.isEmpty(maxLoadRequest.getCaliberId())) {
            maxLoadRequest.setCaliberId(this.getCaliberId());
        }

        // 最大负荷日
        Date maxDate = null;

        if (DateScopeType.WEEK.value().equals(maxLoadRequest.getType())) {
            Date date = DateUtils.string2Date(maxLoadRequest.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR);
            LoadFeatureCityWeekHisDO loadFeatureCityWeekHisVO = loadFeatureCityWeekHisService
                .getLoadFeatureCityWeekHisDO(maxLoadRequest.getCityId(), date, maxLoadRequest.getCaliberId());
            if (loadFeatureCityWeekHisVO != null) {
                maxDate = loadFeatureCityWeekHisVO.getMaxDate();
            }
        } else if (DateScopeType.MONTH.value().equals(maxLoadRequest.getType())) {
            LoadFeatureCityMonthHisDO loadFeatureCityMonthHisVO = loadFeatureCityMonthHisService
                .getLoadFeatureCityMonthHisDO(maxLoadRequest.getCityId(), maxLoadRequest.getDate().substring(0, 4),
                    maxLoadRequest.getDate().substring(5, 7), maxLoadRequest.getCaliberId());
            if (loadFeatureCityMonthHisVO != null) {
                maxDate = loadFeatureCityMonthHisVO.getMaxDate();
            }
        } else if (DateScopeType.QUARTER.value().equals(maxLoadRequest.getType())) {
            LoadFeatureCityQuarterHisDO loadFeatureCityQuarterHisVO = loadFeatureCityQuarterHisService
                .getLoadFeatureCityQuarterHisVO(maxLoadRequest.getCityId(), maxLoadRequest.getDate().substring(0, 4),
                    maxLoadRequest.getDate().substring(5, 6), maxLoadRequest.getCaliberId());
            if (loadFeatureCityQuarterHisVO != null) {
                maxDate = loadFeatureCityQuarterHisVO.getMaxDate();
            }
        } else if (DateScopeType.YEAR.value().equals(maxLoadRequest.getType())) {
            LoadFeatureCityYearHisDO loadFeatureCityYearHisVO = loadFeatureCityYearHisService
                .getLoadFeatureCityYearHisVO(maxLoadRequest.getCityId(), maxLoadRequest.getDate(),
                    maxLoadRequest.getCaliberId());
            if (loadFeatureCityYearHisVO != null) {
                maxDate = loadFeatureCityYearHisVO.getMaxDate();
            }
        } else {
            return BaseResp.failResp("日期类型有误");
        }

        if (maxDate != null) {
            String name = DateUtils.date2String(maxDate, DateFormatType.SIMPLE_DATE_FORMAT_STR);
            List<BigDecimal> datas = loadCityHisService
                .findLoadCityHisDO(maxDate, maxLoadRequest.getCityId(), maxLoadRequest.getCaliberId());
            BaseResp<LoadCommonDTO> response = BaseResp.succResp("查询成功");
            LoadCommonDTO<String> commonResp = new LoadCommonDTO<String>();
            commonResp.setName(name);
            commonResp.setData(datas);
            response.setData(commonResp);
            return response;
        } else {
            return new BaseResp("T706");
        }

    }

    /**
     * 星期特性
     */
    @RequestMapping(value = "/feature/week", method = RequestMethod.GET)
    @MenuResource(name = "查询星期特性", type = TokenType.API, parentMenuPath = Constants.LOAD_ANALYSIS_PARENT)
    public Object getLoadFeatureWeek(@Validated FeatureWeekRequest featureWeekRequest, BindingResult result)
        throws Exception {
        validateFormValue(result);
        if (StringUtils.isEmpty(featureWeekRequest.getCityId()) || featureWeekRequest.getCityId().equals("undefined")) {
            featureWeekRequest.setCityId(this.getLoginCityId());
        }
        if (StringUtils.isEmpty(featureWeekRequest.getCaliberId())) {
            featureWeekRequest.setCaliberId(this.getCaliberId());
        }

        // 日期列表
        List<Date> dates = new ArrayList<Date>();

        for (Integer week = -(featureWeekRequest.getWeeks() - 1) / 2; week <= (featureWeekRequest.getWeeks() - 1) / 2;
            week++) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(featureWeekRequest.getDate());
            calendar.add(Calendar.WEEK_OF_YEAR, week);
            calendar.add(Calendar.DAY_OF_WEEK, featureWeekRequest.getWeekType() - 1);
            dates.add(calendar.getTime());
            logger.info(DateUtils.date2String(calendar.getTime(), DateFormatType.SIMPLE_DATE_FORMAT_STR));
        }
        List<LoadCommonDTO<Date>> datas = loadCityHisService
            .findLoadCityVOsByCityIdAndDates(featureWeekRequest.getCityId(), dates, featureWeekRequest.getCaliberId());

        BaseResp<CommonResp> response = BaseResp.succResp("查询成功");
        CommonResp<LoadCommonDTO<Date>> commonResp = new CommonResp<LoadCommonDTO<Date>>();
        commonResp.setDataList(datas);
        response.setData(commonResp);
        return response;
    }

    /**
     * 周分解曲线
     */
    @RequestMapping(value = "/resolution/week", method = RequestMethod.GET)
    @MenuResource(name = "查询周分解曲线", type = TokenType.API, parentMenuPath = Constants.LOAD_ANALYSIS_PARENT)
    public Object getLoadResolutionWeek(@Validated SingleLoadRequest singleLoadRequest, BindingResult result)
        throws Exception {
        validateFormValue(result);
        if (StringUtils.isEmpty(singleLoadRequest.getCityId()) || singleLoadRequest.getCityId().equals("undefined")) {
            singleLoadRequest.setCityId(this.getLoginCityId());
        }
        LoadDecomposeDTO loadDecomposeDTO = loadDecomposeCityWeekService
            .findLoadDecomposeDTO(singleLoadRequest.getCityId(), singleLoadRequest.getDate(),
                singleLoadRequest.getCaliberId());
        BaseResp<LoadDecomposeDTO> response = BaseResp.succResp("查询成功");
        response.setData(loadDecomposeDTO);
        return response;
    }

    /**
     * 特性曲线
     */
    @RequestMapping(value = "/feature/curve", method = RequestMethod.GET)
    @MenuResource(name = "查询特性曲线", type = TokenType.API, parentMenuPath = Constants.LOAD_ANALYSIS_PARENT)
    public Object getLoadFeatureCurve(@Validated LoadFeatureRequest req, BindingResult result) throws Exception {
        validateFormValue(result);
        if (StringUtils.isEmpty(req.getCityId()) || req.getCityId().equals("undefined")) {
            req.setCityId(this.getLoginCityId());
        }
        if (StringUtils.isBlank(req.getCaliberId())) {
            req.setCaliberId(this.getCaliberId());
        }

        // 负荷特性列表
        List<LoadFeatureDTO> loadFeatureDTOS = null;

        if (DateScopeType.DAY.value().equals(req.getType())) {
            loadFeatureDTOS = loadFeatureCityDayHisService
                .findDayLoadFeatureDTOS(req.getCityId(), req.getStartDate(), req.getEndDate(), req.getCaliberId());
        } else if (DateScopeType.WEEK.value().equals(req.getType())) {
            loadFeatureDTOS = loadFeatureCityWeekHisService
                .findWeekLoadFeatureDTOS(req.getCityId(), req.getStartDate(), req.getEndDate(), req.getCaliberId());
        } else if (DateScopeType.MONTH.value().equals(req.getType())) {
            String startYM = DateUtils.date2String(req.getStartDate(), DateFormatType.YEAR_MONTH_STR);
            String endYM = DateUtils.date2String(req.getEndDate(), DateFormatType.YEAR_MONTH_STR);
            loadFeatureDTOS = loadFeatureCityMonthHisService
                .findMonthLoadFeatureDTOS(req.getCityId(), startYM, endYM, req.getCaliberId());
        } else {
            return BaseResp.failResp("日期类型有误");
        }

        if (loadFeatureDTOS == null || loadFeatureDTOS.size() < 1) {
            return new BaseResp("T706");
        } else {
            BaseResp resp = BaseResp.succResp();
            CommonResp<LoadFeatureDTO> commonResp = new CommonResp<LoadFeatureDTO>();
            commonResp.setDataList(loadFeatureDTOS);
            resp.setData(commonResp);
            return resp;
        }

    }

    /**
     * 24点持续负荷曲线
     */
    @RequestMapping(value = "/curve", method = RequestMethod.GET)
    @MenuResource(name = "24点持续负荷曲线", type = TokenType.API, parentMenuPath = Constants.LOAD_ANALYSIS_PARENT)
    public Object curve(@Validated CommonRequest req, BindingResult result) throws Exception {
        validateFormValue(result);
        if (StringUtils.isEmpty(req.getCityId())) {
            req.setCityId(this.getLoginCityId());
        }
        if (StringUtils.isEmpty(req.getCaliberId())) {
            req.setCaliberId(this.getCaliberId());
        }
        CityValueDTO cityValueDTOS = loadCityHisService
            .find24LoadCityBetweenDate(req.getCityId(), req.getStartDate(), req.getEndDate(), req.getCaliberId());
        if (cityValueDTOS != null && cityValueDTOS.getValue() != null && cityValueDTOS.getValue().size() > 0) {
            BaseResp response = BaseResp.succResp();
            CommonResp<BigDecimal> commonResp = new CommonResp<BigDecimal>();
            commonResp.setDataList(cityValueDTOS.getValue());
            response.setData(commonResp);
            return response;
        } else {
            return new BaseResp("T706");
        }
    }

    /**
     * 稳定度分析
     */
    @RequestMapping(value = "/stability", method = RequestMethod.GET)
    @MenuResource(name = "稳定度分析", type = TokenType.API, parentMenuPath = Constants.STABILITY_ANALYSIS_PARENT)
    public Object getLoadStabilityMonth(@Validated StabilityRequest req, BindingResult result) throws Exception {
        validateFormValue(result);
        if (StringUtils.isEmpty(req.getCityId())) {
            req.setCityId(this.getLoginCityId());
        }
        if (StringUtils.isBlank(req.getCaliberId())) {
            req.setCaliberId(this.getCaliberId());
        }
        //执行算法
        Date date = DateUtils.string2Date(req.getStartDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR);
        if (precisionService == null) {
            logger.error("precisionService对象为空。。。");
        }
        LoadDecomposeCityWeekStabilityDO vo = null;
        try {
            vo = precisionService.doPrecisionAnalysize(req.getCaliberId(), req.getCityId(), date, req.getWeeks());
        } catch (Exception e) {
            logger.error("稳定度算法分析异常，查看算法文件。。。", e);
            throw TsieExceptionUtils.newBusinessException("T707");
        }
        Date beginDate = DateUtils.addDays(date, -req.getWeeks() * 7);
        Date endDate = DateUtils.addDays(date, -1);
        List<LoadDecomposeDTO> componentsList = loadDecomposeCityWeekService
            .findLoadDecomposeDTO(req.getCityId(), beginDate, endDate, req.getCaliberId());
        if (!CollectionUtils.isEmpty(componentsList)) {
            StabilityResp stabilityResp = new StabilityResp();
            List<LoadDecomposeDTO> collect = componentsList.stream().sorted(Comparator.comparing(LoadDecomposeDTO::getDate).reversed()).collect(Collectors.toList());
            stabilityResp.setComponentsList(collect);
            if (vo != null) {
                stabilityResp.setMax(vo.getUpperStability());
                stabilityResp.setMin(vo.getLowerStability());
            }
            BaseResp<StabilityResp> response = BaseResp.succResp("查询成功");
            response.setData(stabilityResp);
            return response;
        } else {
            return new BaseResp("T706");
        }
    }


    /**
     * 更新历史负荷
     */
    @RequestMapping(value = "/history", method = RequestMethod.POST)
    @OperateLog(operate = "更新历史负荷")
    @MenuResource(name = "更新历史负荷", type = TokenType.API, parentMenuPath = Constants.DATA_EDIT_PARENT)
    public Object updateLoadHis(@Validated UpdateLoadRequest dataList, BindingResult result) throws Exception {
        validateFormValue(result);
        loadCityHisService.doUpdateLoadCityHisDO(dataList.getDataList(), this.getCaliberId());
        return BaseResp.succResp("修改成功");
    }

    /**
     * 发送节调数据
     */
    @GetMapping(value = "/sendLoad")
    @OperateLog(operate = "发送节调数据")
    public BaseResp<String> sendFcLoad(Date date) throws Exception {
        String fileName = sendLoadFcBasicService.sendForecastLoadToJd(date);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(fileName);
        return baseResp;
    }

    /**
     * 导入历史负荷
     */
    @RequestMapping(value = "/history/import", method = RequestMethod.POST)
    @OperateLog(operate = "导入历史负荷")
    @MenuResource(name = "导入历史负荷", type = TokenType.API, parentMenuPath = Constants.DATA_EDIT_PARENT)
    public Object updateLoadHisFromExcel(
        @RequestParam(value = "uploadFile", required = false) MultipartFile uploadFile) throws Exception {
        if (null == uploadFile) {
            return BaseResp.failResp("uploadFile为空");
        }
        boolean check = CheckoutFileTypeSecurity.checkoutFileTypeSecurity(uploadFile);
        if (!check){
            return BaseResp.failResp("文件安全性存在问题");
        }
        String fileExtension = uploadFile.getOriginalFilename()
            .substring(uploadFile.getOriginalFilename().indexOf("."), uploadFile.getOriginalFilename().length());
        if (".xlsx".equals(fileExtension) || ".xls".equals(fileExtension)) {
            boolean isExcel2003 = true;
            if (".xlsx".equals(fileExtension)) {
                isExcel2003 = false;
            }
            InputStream is = uploadFile.getInputStream();
            List<List<List<String>>> dataList = ExcelUtil.read(is, isExcel2003);
            loadCityHisService.doUpdateLoadCityHisDOFromExcel(dataList);
        } else {
//            //导入气象数据  todo wangchen  节省时间临时加在了这里
//            try {
//                List<Result> results = this.doUploadWeatherDataCollect(uploadFile);
//                if (results == null || results.size() < 1) {
//                    throw new IOException("解析气象数据异常...");
//                }
//                this.doCreateOrUpdateWeatherData(results);
//            } catch (Exception e) {
//                logger.error("气象数据导入异常",e);
//                throw new BusinessException("气象数据导入异常",e);
//            }
        }
        return BaseResp.succResp("导入修改成功");
    }

    /**
     * 导入气象数据
     */
    @RequestMapping(value = "/weather/import", method = RequestMethod.POST)
    public BaseResp doUploadWeatherDataCollect(
        @RequestParam(value = "uploadFile", required = false) MultipartFile weatherFile)
        throws IOException, RarException {
        boolean check = CheckoutFileTypeSecurity.checkoutFileTypeSecurity(weatherFile);
        if (!check){
            return BaseResp.failResp("文件安全性存在问题");
        }
        String fileName = weatherFile.getOriginalFilename();
        String weatherPath =
                coreConfigInfo.getRuntimeParam("weather.path").replace("/", File.separator).replace("\\", File.separator)
                + DateUtils.date2String(new Date(), DateFormatType.SIMPLE_DATE_TIME_MICROSECONDS_FORMAT_STR)
                + File.separator;
        if (fileName.endsWith(CompressFileUtils.ZIP_SUFFIX)) {
            CompressFileUtils.unZipFiles(weatherPath, weatherFile);
        } else if (fileName.endsWith(CompressFileUtils.RAR_SUFFIX)) {
            CompressFileUtils.unRar(weatherFile, weatherPath);
        } else {
            logger.error("上传的压缩文件类型有误，请检查...");
            throw new IOException("上传的压缩文件类型有误，请检查...");
        }
        TxtWeatherResolver txtWeatherResolver = new TxtWeatherResolver();
        List<Result> results = null;
        try {
            results = txtWeatherResolver.resolveWeather(weatherPath);
            if (results == null || results.size() < 1) {
                throw new IOException("解析气象数据异常...");
            }
            this.doCreateOrUpdateWeatherData(results);
        } catch (Exception e) {
            logger.error("解析气象数据异常...", e);
            throw TsieExceptionUtils.newBusinessException("气象数据导入异常", e);
        }
        return BaseResp.succResp("导入修改成功");
    }


    private void doCreateOrUpdateWeatherData(List<Result> results) throws Exception {
        if (results == null) {
            return;
        }
        //1.降水累加  其他.降水不累加
        String rainAdd = coreConfigInfo.getRuntimeParam("weather.rain.add");
        for (Result result : results) {
            Integer type = this.getWeatherEnum(result.getType());
            if (type == null) {
                continue;
            }
            CityDO cityVO = cityService.findCityByName(coreConfigInfo.getRuntimeParam(result.getCityName()));
            if (cityVO == null) {
                logger.error("城市名称异常，请查看qs文件中的城市名称");
                continue;
            }
            List<BigDecimal> weatherData = this.string2BigDecimal(result.getWeatherData());

            //如果类型是降水，并且是累加降水，分割每个点的值
            if (WeatherEnum.RAINFALL.getType().equals(type) && "1".equals(rainAdd)) {
                weatherData = splitWeatherData(weatherData);
            } else {
                weatherData.remove(0);
            }
            Map<String, BigDecimal> weatherMap = ColumnUtil
                .listToMap(weatherData, Constants.WEATHER_CURVE_START_WITH_ZERO);

            if (result.isHis()) {
                WeatherCityHisDO weatherCityHisVO = new WeatherCityHisDO();
                weatherCityHisVO.setCityId(cityVO.getId());
                weatherCityHisVO.setDate(new java.sql.Date(result.getDate().getTime()));
                weatherCityHisVO.setType(type);
                BasePeriodUtils.setAllFiled(weatherCityHisVO, weatherMap);
                List<WeatherCityHisDO> weatherCityHisVOList = weatherCityHisService
                    .findWeatherCityHisDOS(weatherCityHisVO.getCityId(), weatherCityHisVO.getType(),
                        weatherCityHisVO.getDate(), weatherCityHisVO.getDate());
                if (weatherCityHisVOList == null || weatherCityHisVOList.size() < 1) {
                    weatherCityHisVO.setCreatetime(new Timestamp(System.currentTimeMillis()));
                    weatherCityHisService.doCreate(weatherCityHisVO);
                    continue;
                }
                weatherCityHisVO.setId(weatherCityHisVOList.get(0).getId());
                weatherCityHisVO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                weatherCityHisService.doUpdateWeatherCityHisDO(weatherCityHisVO);
            } else {
                WeatherCityFcDO weatherCityFcVO = new WeatherCityFcDO();
                weatherCityFcVO.setCityId(cityVO.getId());
                weatherCityFcVO.setDate(new java.sql.Date(result.getDate().getTime()));
                weatherCityFcVO.setType(type);
                BasePeriodUtils.setAllFiled(weatherCityFcVO, weatherMap);
                List<BaseWeatherDO> weatherCityFcVOS = weatherCityFcService
                    .findWeatherCityFcDOs(weatherCityFcVO.getCityId(), weatherCityFcVO.getType(),
                        weatherCityFcVO.getDate(), weatherCityFcVO.getDate());
                if (weatherCityFcVOS == null || weatherCityFcVOS.size() < 1) {
                    weatherCityFcVO.setCreatetime(new Timestamp(System.currentTimeMillis()));
                    weatherCityFcService.doCreate(weatherCityFcVO);
                    continue;
                }
                weatherCityFcVO.setId(weatherCityFcVOS.get(0).getId());
                weatherCityFcVO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                weatherCityFcService.doUpdateWeatherCityFcDO(weatherCityFcVO);
            }
        }
    }

    private List<BigDecimal> splitWeatherData(List<BigDecimal> rainData) {
        List<BigDecimal> result = new ArrayList<>();
        for (int i = 1; i < rainData.size(); i++) {
            if (rainData.get(i) == null || rainData.get(i - 1) == null) {
                result.add(rainData.get(i));
                continue;
            }
            result.add(rainData.get(i).subtract(rainData.get(i - 1)));
        }
        return result;
    }

    private Integer getWeatherEnum(String type) {
        if ("Wind".equals(type)) {
            return WeatherEnum.WINDSPEED.value();
        }
        if ("Temperature".equals(type)) {
            return WeatherEnum.TEMPERATURE.value();
        }
        if ("Precipitation".equals(type)) {
            return WeatherEnum.RAINFALL.value();
        }
        if ("Humidity".equals(type)) {
            return WeatherEnum.HUMIDITY.value();
        }
        return null;
    }

    private List<BigDecimal> string2BigDecimal(List<String> weatherData) {
        List<BigDecimal> list = new ArrayList<>();
        for (String weather : weatherData) {
            try {
                if ("null".equals(weather)) {
                    list.add(null);
                    continue;
                }
                list.add(new BigDecimal(weather));
            } catch (NumberFormatException e) {
                System.out.println(weather);
                e.printStackTrace();
            }

        }
        return list;
    }


    /**
     * 批量导入气象数据
     *
     * @author: byw
     * @Date:2019/4/20
     */
    @RequestMapping(value = "/createOrUpdateWeatherForBatch", method = RequestMethod.POST)
    public BaseResp createOrUpdateWeatherForBatch() throws Exception {
        String weatherPath = "E:\\loadForecastdata\\loadForecast";
        TxtWeatherResolver txtWeatherResolver = new TxtWeatherResolver();
        List<Result> results = txtWeatherResolver.resolveWeather(weatherPath);
        doCreateOrUpdateWeatherData(results);
        BaseResp resp = BaseResp.succResp();
        return resp;
    }

}
