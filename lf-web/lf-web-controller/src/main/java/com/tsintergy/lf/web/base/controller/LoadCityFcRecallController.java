package com.tsintergy.lf.web.base.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.recall.api.LoadCityFcRecallService;
import com.tsintergy.lf.serviceapi.base.recall.dto.LoadFcQueryDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2025-05-07
 * @since 1.0.0
 */
@Api(tags = "预测回溯")
@RequestMapping("/recall")
@RestController
@Slf4j
public class LoadCityFcRecallController extends BaseController {

    @Autowired
    LoadCityFcRecallService loadCityFcRecallService;

    @ApiOperation("负荷预测对比（批次负荷）")
    @GetMapping(value = "/loadFcBatchCompare")
    public BaseResp<LoadFcQueryDTO> findBatchLoad(String cityId, Date date, String algorithmId,
        String batchIds, Integer day, String caliberId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        if (day == null) {
            day = 1;
        }
        if (caliberId == null) {
            caliberId = this.getCaliberId();
        }
        if (Constants.SYSTEM_LOAD_CALIBER.equals(caliberId) && Constants.PROVINCE_ID.equals(cityId)){
            algorithmId = com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum.DP_FORECAST_2.getId();
        }
        LoadFcQueryDTO fcQueryDTO = loadCityFcRecallService.getLoadBatchFcQueryDTO(cityId, caliberId, date, algorithmId
            , batchIds, day);
        if (fcQueryDTO == null) {
            return new BaseResp("T706");
        }
        baseResp.setData(fcQueryDTO);
        return baseResp;
    }

}
