package com.tsintergy.lf.web.base.evalucation.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.evalucation.api.AccuracyAssessService;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyAssessDTO;
import com.tsintergy.lf.serviceapi.base.recall.dto.StatisticsAccuracyDTO;
import com.tsintergy.lf.web.base.controller.BaseController;
import io.swagger.annotations.ApiOperation;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2025-05-07
 * @since 1.0.0
 */
@RequestMapping("/assess")
@RestController
public class AccuracyAssessController extends BaseController {

    @Autowired
    private AccuracyAssessService accuracyAssessService;

    @ApiOperation("名称列表")
    @RequestMapping(value = "/findNameList", method = RequestMethod.GET)
    public BaseResp findNameList(String cityId, String caliberId, String algorithmId, Date startDate,
        Date endDate) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<String> nameList = accuracyAssessService.findNameList(cityId, caliberId, algorithmId, startDate, endDate);
        baseResp.setData(nameList);
        return baseResp;
    }

    @ApiOperation("回溯准确率结果")
    @RequestMapping(value = "/accuracyEstimationRecall", method = RequestMethod.GET)
    public BaseResp findAccuracyRecallList(String cityId, String caliberId, String algorithmId, Date startDate,
        Date endDate,Integer batchIds, Integer day) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        if (Constants.SYSTEM_LOAD_CALIBER.equals(caliberId) && Constants.PROVINCE_ID.equals(cityId)){
            algorithmId = com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum.DP_FORECAST_2.getId();
        }
        List<AccuracyAssessDTO> recallList = accuracyAssessService
            .findAccuracyAccessRecallList(cityId, caliberId, algorithmId, startDate, endDate, String.valueOf(batchIds), day);
        baseResp.setData(recallList);
        return baseResp;
    }

    @ApiOperation("回溯准确率统计")
    @RequestMapping(value = "/statistics/accuracy", method = RequestMethod.GET)
    public BaseResp<StatisticsAccuracyDTO> statisticsAccuracy(String cityId, Date startDate, Date endDate, String algorithmId
        , String batchIds, String filterName, Integer day, String caliberId) {
        BaseResp baseResp = null;
        try {
            if (caliberId == null) {
                caliberId = getCaliberId();
            }
            if (Constants.SYSTEM_LOAD_CALIBER.equals(caliberId) && Constants.PROVINCE_ID.equals(cityId)){
                algorithmId = com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum.DP_FORECAST_2.getId();
            }
            StatisticsAccuracyDTO accuracyLoadFcRecallDTOS = accuracyAssessService
                .getStatisticsAccuracyAccessRecall(cityId, caliberId, startDate, endDate, algorithmId, batchIds,
                    filterName, day);
            baseResp = BaseResp.succResp();
            baseResp.setData(accuracyLoadFcRecallDTOS);
        } catch (Exception e) {
            e.printStackTrace();
            baseResp = BaseResp.failResp("准确率统计失败");
        }
        return baseResp;
    }

}
