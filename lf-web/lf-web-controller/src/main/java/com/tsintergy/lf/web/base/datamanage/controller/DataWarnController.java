package com.tsintergy.lf.web.base.datamanage.controller;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.AlgorithmEnum;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.api.GurNetworkAlgorithmService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAlgorithmAccuracyDayRecallService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAlgorithmAccuracyDayService;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAlgorithmAccuracyDayDO;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAlgorithmAccuracyRecallDayDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcLoadForecastService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcLoadForecastDO;
import com.tsintergy.lf.web.base.controller.BaseController;
import com.tsintergy.lf.web.base.datamanage.response.WeatherFcData;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2025-05-07
 * @since 1.0.0
 */
@RestController
@RequestMapping("/warn")
public class DataWarnController extends BaseController {

    @Autowired
    private WeatherCityFcLoadForecastService weatherCityFcLoadForecastService;

    @Autowired
    private GurNetworkAlgorithmService gurNetworkAlgorithmService;

    @RequestMapping(value = "/weather/fc", method = RequestMethod.GET)
    public BaseResp<List<WeatherFcData>> getWeatherFc(String cityId, Date startDate ,Date endDate, String algorithmId,
        String caliberId,String batchId, Integer type)
        throws Exception {
        if (Constants.SYSTEM_LOAD_CALIBER.equals(caliberId) && Constants.PROVINCE_ID.equals(cityId)){
            algorithmId = com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum.DP_FORECAST_2.getId();
        }
        List<WeatherFcData> resultList = new ArrayList<>();
        List<WeatherCityFcLoadForecastDO> fc = weatherCityFcLoadForecastService
            .findWeatherFcLoadForecastBatchByType(cityId, algorithmId, caliberId, batchId, type, startDate, endDate);
        for (WeatherCityFcLoadForecastDO one : fc) {
            WeatherFcData result = new WeatherFcData();
            BeanUtils.copyProperties(one, result);
            result.setWeatherList(one.getWeatherList());
            if (one.getCreatetime() != null) {
                result.setWeatherCreateTime(
                    DateUtils.date2String(new Date(one.getCreatetime().getTime()), DateFormatType.DATE_FORMAT_STR));
            }
            resultList.add(result);
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(resultList);
        return baseResp;
    }

    /*@RequestMapping(value = "/test", method = RequestMethod.GET)
    public BaseResp getWeatherFc(Date startDate,Date endDate)
        throws Exception {
        List<AlgorithmEnum> algorithmEnums = Collections.singletonList(AlgorithmEnum.GUR_NETWORK);
        gurNetworkAlgorithmService.doGurNetworkForecast("1",startDate,endDate,"10",algorithmEnums);
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

    @RequestMapping(value = "/test/old", method = RequestMethod.GET)
    public BaseResp getWeatherHis(Date startDate,Date endDate) throws Exception {
        ExecutorService executor = Executors.newFixedThreadPool(4);
        List<AlgorithmEnum> algorithmEnums = Collections.singletonList(AlgorithmEnum.GUR_NETWORK);
        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);
        for (Date date : listBetweenDay) {
            gurNetworkAlgorithmService.doGurNetworkRecallForecast("1",date,date,"10",
                algorithmEnums, 59, 1, 2, true);
        }
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

    @Autowired
    private ReportAlgorithmAccuracyDayService reportAccuracyDayService;

    @Autowired
    private ReportAlgorithmAccuracyDayRecallService reportAlgorithmAccuracyDayRecallService;

    @RequestMapping(value = "/test/accuracy", method = RequestMethod.GET)
    public BaseResp getAccuracy(Date startDate,Date endDate)
        throws Exception {
        List<ReportAlgorithmAccuracyDayDO> reportAccuracyDayVOS = reportAccuracyDayService
            .doStatSaveReportAccuracy(null,null, startDate, endDate);
        reportAccuracyDayService.doSaveOrUpdate(reportAccuracyDayVOS);
        // 统计日回溯准确率
        List<ReportAlgorithmAccuracyRecallDayDO> reportAlgorithmAccuracyRecallDayDOS = reportAlgorithmAccuracyDayRecallService.doStatSaveReportAccuracy(
            null, null, startDate, endDate);
        reportAlgorithmAccuracyDayRecallService.doSaveOrUpdate(reportAlgorithmAccuracyRecallDayDOS);
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }*/
}
