/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司
 * Author:  wangchen
 * Date:  2020/6/11 9:52
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.forecast.controller;


import com.tsintergy.lf.core.constants.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.dto.SkipHolidayDTO;
import com.tsintergy.lf.serviceapi.base.forecast.api.ForecastService;
import com.tsintergy.lf.serviceapi.base.forecast.bean.DuplicateForecastException;
import java.util.HashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/6/11 
 * @since 1.0.0
 */
@Component
public class ForecastManager {

    private static final Logger logger = LoggerFactory.getLogger(ForecastManager.class);

    /**
     * 是否可以预测 Map<String, Boolean> isForecast ->(true:可以预测；false:不能预测）
     * isSuccess ->(true:上次预测成功；false:上次预测失败）
     */
    private static final Map<String, Map<String, Boolean>> forecastMap = new ConcurrentHashMap<String, Map<String, Boolean>>();

    @Autowired
    private ForecastService forecastService;
    @Autowired
    private HolidayService holidayService;

    /**
     * 查看是否可以预测
     * 0不能预测、1初次预测、2上次预测成功、3上次预测失败
     * @return
     */
    public static String isForecast(String userId) {

        Map<String, Boolean> result = forecastMap.get(userId + DateUtil.formateDate(new Date()));
        if (result == null) {
            return "1";
        }else if(result.get("isForecast")){
            if(result.get("isSuccess")){
                return "2";
            }
            return "3";
        }
        return "0";
    }

    /**
     * 修改用户对应为预测状态
     * @param userId
     */
    public void forecast(String userId, String cityId, String caliberId, Date startDate, Date endDate, List<AlgorithmEnum> algorithmEnums,String weatherCode) throws Exception {
        String date = DateUtil.formateDate(new Date());
        if (!"0".equals(isForecast(userId + date))) {
            Map<String, Boolean> result = new HashMap<>();
            result.put("isForecast", false);
            forecastMap.put(userId + date,result);
            Thread thread = new Thread(new Runnable() {
                Map<String, Boolean> result1 = new HashMap<>();
                @Override
                public void run() {
                    try {
                        SkipHolidayDTO skipHolidayDTO = holidayService.skipHolidays(startDate,endDate);
                        forecastService.doForecastNormal(cityId, caliberId, skipHolidayDTO.getNormalDays(), algorithmEnums, weatherCode);
                        result1.put("isSuccess", true);
                    } catch (Exception e) {
                        result1.put("isSuccess", false);
                        logger.error("预测失败",e);
                    } finally {
                        result1.put("isForecast", true);
                        forecastMap.put(userId + date,result1);
                    }
                }
            });
            thread.start();
        } else {
            throw new DuplicateForecastException();
        }
    }



}
