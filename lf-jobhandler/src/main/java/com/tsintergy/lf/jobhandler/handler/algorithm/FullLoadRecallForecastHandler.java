package com.tsintergy.lf.jobhandler.handler.algorithm;

import com.cyx.jobcore.biz.model.ReturnT;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.AlgorithmEnum;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.jobhandler.common.Job;
import com.tsintergy.lf.jobhandler.common.ParamDate;
import com.tsintergy.lf.serviceapi.algorithm.api.GurNetworkAlgorithmService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.CityEnum;
import com.tsintergy.lf.serviceapi.base.forecast.api.AutoForecastService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceimpl.common.impl.CoreConfigInfo;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description
 * <AUTHOR>
 * 全负荷预测算法回溯： ( 统调负荷+分布式光伏实际负荷 -》新神经网络预测 ) - 分布式光伏预测负荷
 * @Date 2025/04/30 10:33
 **/
@Component
@Job("fullLoadRecallForecastHandler")
@Slf4j
public class FullLoadRecallForecastHandler extends AbstractBaseHandler {

    @Autowired
    private GurNetworkAlgorithmService gurNetworkAlgorithmService;

    @Autowired
    private SettingSystemService settingSystemService;

    @Autowired
    private CityService cityService;

    @Autowired
    private ScheduledThreadPoolExecutor scheduledThreadPoolExecutor;

    @Autowired
    private AutoForecastService autoForecastService;

    @Autowired
    private CoreConfigInfo coreConfigInfo;



    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Date startDate = DateUtils.addDays(new Date(), -1);
        Date endDate = DateUtils.addDays(new Date(), -1);
        if(StringUtils.isNotEmpty(s)){
            ParamDate paramDate= resolveJobParam(s,null,null);
            startDate=paramDate.getStartDate();
            endDate=paramDate.getEndDate();
        }
        List<AlgorithmEnum> algorithmEnums = Collections.singletonList(AlgorithmEnum.GUR_NETWORK);
        List<com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum> enums = Collections.singletonList(com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum.FORECAST_XGBOOST);
        for (CityDO cityVO : cityService.findAllCitys()) {
            if (Constants.WEST_ID.equals(cityVO.getId())||Constants.EAST_ID.equals(cityVO.getId()) || "24".equals(cityVO.getId())){
                continue;
            }
            if (Constants.PROVINCE_ID.equals(cityVO.getId()) || cityVO.getId().equals(CityEnum.guangzhou.getId())){
                //gurNetworkAlgorithmService.doGurNetworkRecallForecast(cityVO.getId(),startDate,endDate,Constants.SYSTEM_LOAD_CALIBER,algorithmEnums);
            }
        }
        return ReturnT.SUCCESS;
    }
}
