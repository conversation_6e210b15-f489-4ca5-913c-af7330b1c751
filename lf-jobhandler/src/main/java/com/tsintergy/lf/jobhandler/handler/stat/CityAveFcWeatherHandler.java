package com.tsintergy.lf.jobhandler.handler.stat;

import com.cyx.jobcore.biz.model.ReturnT;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.jobhandler.common.Job;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherStationCityAveDayService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * Description: 广州地市站点平均气象 <br>
 *
 */
@Component
@Job("cityAveFcWeatherHandler")
@Slf4j
public class CityAveFcWeatherHandler extends AbstractBaseHandler {

    @Autowired
    WeatherStationCityAveDayService weatherStationCityAveDayService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        Date startDate = null;
        Date endDate = null;
        if(StringUtils.isEmpty(param)){
            startDate = new Date();
            endDate = DateUtils.addDays(new Date(),7);
        }else {
            String[] split = param.split(",");
            String startDateStr = split[0];
            String endDateStr = split[1];
            startDate = DateUtils.string2Date(startDateStr, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
            endDate = DateUtils.string2Date(endDateStr, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
        }
        weatherStationCityAveDayService.calculateAveFcDay(Constants.GUANGZHOU_ID, startDate, endDate);
        return ReturnT.SUCCESS;
    }

}
