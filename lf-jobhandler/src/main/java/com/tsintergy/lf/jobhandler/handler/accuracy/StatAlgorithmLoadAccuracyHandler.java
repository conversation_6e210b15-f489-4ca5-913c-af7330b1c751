/**
 * Copyright (C), 2015‐2021, 北京清能互联科技有限公司 Author:  wangfeng Date:  2021/2/3 11:49 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.jobhandler.handler.accuracy;

import com.cyx.jobcore.biz.model.ReturnT;
import com.cyx.jobcore.log.XxlJobLogger;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.jobhandler.common.Job;
import com.tsintergy.lf.jobhandler.common.ParamDate;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.report.api.*;
import com.tsintergy.lf.serviceapi.base.report.pojo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 * 统计算法综合准确率
 * 0 5 4 * * ?
 * <AUTHOR>
 * @create 2021/2/3
 * @since 2.0.0
 */
@Component
@Job("statAlgorithmLoadAccuracyHandler")
@Slf4j
public class StatAlgorithmLoadAccuracyHandler extends AbstractBaseHandler {


    @Autowired
    CaliberService caliberService;
    @Autowired
    private ReportAlgorithmAccuracyDayService reportAccuracyDayService;

    @Autowired
    private ReportAlgorithmAccuracyDayRecallService reportAlgorithmAccuracyDayRecallService;

    /**
     * 如果有传参数：
     * 日：传的开始日期到结束日期
     * 周：开始日期的周的第一天及结束日期周的最后一天所有的周准确率
     * 月: 开始日期的第一天所在月及最后一天所在月的准确率
     * 如果没有传参数：
     * 日：统计昨日填报准确率
     * 周：统计昨日所在周的准确率
     * 月：统计昨天所在月的准确率
     *
     * @param s
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        //默认为昨日
        Date date = DateUtils.addDays(new Date(), -1);
        ParamDate paramDate = resolveJobParam(s, date, 0);
        //统计填报的日准确率(昨日准确率)
        statDayAccuracy(paramDate);
        return ReturnT.SUCCESS;
    }
    /**
     * 日准确率
     * @param paramDate
     */
    public void statDayAccuracy(ParamDate paramDate) {
        try {
            XxlJobLogger.log("统计日负荷填报准确率开始,开始时间为:{},结束时间为:{}", paramDate.getStartDate(), paramDate.getEndDate());
            //统计日负荷填报准确率
            List<ReportAlgorithmAccuracyDayDO> reportAccuracyDayVOS = reportAccuracyDayService
                .doStatSaveReportAccuracy(null,null, paramDate.getStartDate(), paramDate.getEndDate());
            reportAccuracyDayService.doSaveOrUpdate(reportAccuracyDayVOS);
            XxlJobLogger.log("统计日负荷填报准确率执行成功啦");
            // 统计日回溯准确率
            List<ReportAlgorithmAccuracyRecallDayDO> reportAlgorithmAccuracyRecallDayDOS = reportAlgorithmAccuracyDayRecallService.doStatSaveReportAccuracy(
                null, null, paramDate.getStartDate(), paramDate.getEndDate());
            reportAlgorithmAccuracyDayRecallService.doSaveOrUpdate(reportAlgorithmAccuracyRecallDayDOS);
            XxlJobLogger.log("统计日负荷回溯准确率执行成功啦");
        } catch (Exception e) {
            XxlJobLogger.log("执行异常，异常为{}", e);
        }
    }

}