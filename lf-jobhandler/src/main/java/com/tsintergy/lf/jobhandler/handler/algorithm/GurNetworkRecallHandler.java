package com.tsintergy.lf.jobhandler.handler.algorithm;

import com.cyx.jobcore.biz.model.ReturnT;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.AlgorithmEnum;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.jobhandler.common.Job;
import com.tsintergy.lf.jobhandler.common.ParamDate;
import com.tsintergy.lf.serviceapi.algorithm.api.GurNetworkAlgorithmService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.CityEnum;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceimpl.common.impl.CoreConfigInfo;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2025-05-08
 * @since 1.0.0
 */
@Component
@Job("gurNetworkRecallHandler")
@Slf4j
public class GurNetworkRecallHandler extends AbstractBaseHandler {

    @Autowired
    private GurNetworkAlgorithmService gurNetworkAlgorithmService;

    @Autowired
    SettingSystemService settingSystemService;

    @Autowired
    private CityService cityService;

    @Autowired
    private CoreConfigInfo coreConfigInfo;

    public Map<String, String> initPointMap() {
        Map<String, String> map = new HashMap<>();
        map.put("1-1", "task.forecast.pointGdTd");
        map.put("1-10", "task.forecast.pointGdXt");
        map.put("2-1", "task.forecast.pointGzTd");
        map.put("2-10", "task.forecast.pointGzXt");
        return map;
    }

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Date startDate = DateUtils.addDays(new Date(), -1);
        Date endDate = startDate;
        if(StringUtils.isNotEmpty(s)){
            ParamDate paramDate= resolveJobParam(s,null,null);
            startDate=paramDate.getStartDate();
            endDate=paramDate.getEndDate();
        }
        List<CityDO> cityVOS = cityService.findAllCitys();
        List<AlgorithmEnum> algorithmEnums = Collections.singletonList(AlgorithmEnum.GUR_NETWORK);
        for (CityDO cityVO : cityVOS) {
            try {
                //只跑广东和广州
                if (Constants.PROVINCE_ID.equals(cityVO.getId()) || CityEnum.guangzhou.getId().equals(cityVO.getId())){
                    for (String string : Arrays.asList(Constants.TONGDIAO, Constants.SYSTEM_LOAD_CALIBER)) {
                        String key = cityVO.getId() + Constants.PUNCTUATION + string;
                        if (initPointMap().get(key) != null) {
                            Integer point = Integer.parseInt(coreConfigInfo.getRuntimeParam(initPointMap().get(key)));
                            gurNetworkAlgorithmService.doGurNetworkRecallForecast(cityVO.getId(),startDate,endDate,string,
                                algorithmEnums, point, 1, 2, true);
                        }
                    }
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        return ReturnT.SUCCESS;
    }

}
