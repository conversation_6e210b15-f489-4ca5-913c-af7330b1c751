INSERT INTO `tsie_menu`(`id`, `name`, `type`, `menupath`, `parentId`, `description`,`targetType`,`rel`,`sort`) VALUES (14, '节假日信息管理', 1, 'HolidaySetting', '4028d0816cb8644d016cb86f0585010e','节假日信息管理','navTap','节假日信息管理','1000');
-- 2021-02-05 增加网状及亮度的配置
INSERT INTO `setting_system_init` (`id`, `field`, `value`, `name`, `description`) VALUES ('40', 'color_str', '#5B5B5B', '网格的亮度颜色', '网格的亮度颜色');
INSERT INTO `setting_system_init` (`id`, `field`, `value`, `name`, `description`) VALUES ('41', 'open_reseau', '1', '是否开启页面网格', '是否开启页面网格（0，关闭 1开启）');

﻿

--20210222 增加菜单 批次预测表

INSERT INTO `tsie_menu`(`id`, `name`, `type`, `menupath`, `parentId`, `description`,`targetType`,`rel`,`sort`) VALUES ('4028fa816c8b208b016c93da97f90254','上报结果查询','1','ReportResult','4028fa816c8b208b016c93da97f90a9a','上报结果查询','navTab','上报结果查询','9999');

CREATE TABLE "load_city_fc_batch" (
  "id" varchar(32) NOT NULL COMMENT '96点负荷表id',
  "date" date NOT NULL COMMENT '日期',
  "city_id" varchar(32) NOT NULL COMMENT '城市表id',
  "caliber_id" varchar(32) NOT NULL COMMENT '口径id',
  "algorithm_id" varchar(32) NOT NULL COMMENT '预测算法id',
  "batch_id" varchar(32) NOT NULL COMMENT '批次号',
  "t0000" decimal(32,4) DEFAULT NULL,
  "t0015" decimal(32,4) DEFAULT NULL,
  "t0030" decimal(32,4) DEFAULT NULL,
  "t0045" decimal(32,4) DEFAULT NULL,
  "t0100" decimal(32,4) DEFAULT NULL,
  "t0115" decimal(32,4) DEFAULT NULL,
  "t0130" decimal(32,4) DEFAULT NULL,
  "t0145" decimal(32,4) DEFAULT NULL,
  "t0200" decimal(32,4) DEFAULT NULL,
  "t0215" decimal(32,4) DEFAULT NULL,
  "t0230" decimal(32,4) DEFAULT NULL,
  "t0245" decimal(32,4) DEFAULT NULL,
  "t0300" decimal(32,4) DEFAULT NULL,
  "t0315" decimal(32,4) DEFAULT NULL,
  "t0330" decimal(32,4) DEFAULT NULL,
  "t0345" decimal(32,4) DEFAULT NULL,
  "t0400" decimal(32,4) DEFAULT NULL,
  "t0415" decimal(32,4) DEFAULT NULL,
  "t0430" decimal(32,4) DEFAULT NULL,
  "t0445" decimal(32,4) DEFAULT NULL,
  "t0500" decimal(32,4) DEFAULT NULL,
  "t0515" decimal(32,4) DEFAULT NULL,
  "t0530" decimal(32,4) DEFAULT NULL,
  "t0545" decimal(32,4) DEFAULT NULL,
  "t0600" decimal(32,4) DEFAULT NULL,
  "t0615" decimal(32,4) DEFAULT NULL,
  "t0630" decimal(32,4) DEFAULT NULL,
  "t0645" decimal(32,4) DEFAULT NULL,
  "t0700" decimal(32,4) DEFAULT NULL,
  "t0715" decimal(32,4) DEFAULT NULL,
  "t0730" decimal(32,4) DEFAULT NULL,
  "t0745" decimal(32,4) DEFAULT NULL,
  "t0800" decimal(32,4) DEFAULT NULL,
  "t0815" decimal(32,4) DEFAULT NULL,
  "t0830" decimal(32,4) DEFAULT NULL,
  "t0845" decimal(32,4) DEFAULT NULL,
  "t0900" decimal(32,4) DEFAULT NULL,
  "t0915" decimal(32,4) DEFAULT NULL,
  "t0930" decimal(32,4) DEFAULT NULL,
  "t0945" decimal(32,4) DEFAULT NULL,
  "t1000" decimal(32,4) DEFAULT NULL,
  "t1015" decimal(32,4) DEFAULT NULL,
  "t1030" decimal(32,4) DEFAULT NULL,
  "t1045" decimal(32,4) DEFAULT NULL,
  "t1100" decimal(32,4) DEFAULT NULL,
  "t1115" decimal(32,4) DEFAULT NULL,
  "t1130" decimal(32,4) DEFAULT NULL,
  "t1145" decimal(32,4) DEFAULT NULL,
  "t1200" decimal(32,4) DEFAULT NULL,
  "t1215" decimal(32,4) DEFAULT NULL,
  "t1230" decimal(32,4) DEFAULT NULL,
  "t1245" decimal(32,4) DEFAULT NULL,
  "t1300" decimal(32,4) DEFAULT NULL,
  "t1315" decimal(32,4) DEFAULT NULL,
  "t1330" decimal(32,4) DEFAULT NULL,
  "t1345" decimal(32,4) DEFAULT NULL,
  "t1400" decimal(32,4) DEFAULT NULL,
  "t1415" decimal(32,4) DEFAULT NULL,
  "t1430" decimal(32,4) DEFAULT NULL,
  "t1445" decimal(32,4) DEFAULT NULL,
  "t1500" decimal(32,4) DEFAULT NULL,
  "t1515" decimal(32,4) DEFAULT NULL,
  "t1530" decimal(32,4) DEFAULT NULL,
  "t1545" decimal(32,4) DEFAULT NULL,
  "t1600" decimal(32,4) DEFAULT NULL,
  "t1615" decimal(32,4) DEFAULT NULL,
  "t1630" decimal(32,4) DEFAULT NULL,
  "t1645" decimal(32,4) DEFAULT NULL,
  "t1700" decimal(32,4) DEFAULT NULL,
  "t1715" decimal(32,4) DEFAULT NULL,
  "t1730" decimal(32,4) DEFAULT NULL,
  "t1745" decimal(32,4) DEFAULT NULL,
  "t1800" decimal(32,4) DEFAULT NULL,
  "t1815" decimal(32,4) DEFAULT NULL,
  "t1830" decimal(32,4) DEFAULT NULL,
  "t1845" decimal(32,4) DEFAULT NULL,
  "t1900" decimal(32,4) DEFAULT NULL,
  "t1915" decimal(32,4) DEFAULT NULL,
  "t1930" decimal(32,4) DEFAULT NULL,
  "t1945" decimal(32,4) DEFAULT NULL,
  "t2000" decimal(32,4) DEFAULT NULL,
  "t2015" decimal(32,4) DEFAULT NULL,
  "t2030" decimal(32,4) DEFAULT NULL,
  "t2045" decimal(32,4) DEFAULT NULL,
  "t2100" decimal(32,4) DEFAULT NULL,
  "t2115" decimal(32,4) DEFAULT NULL,
  "t2130" decimal(32,4) DEFAULT NULL,
  "t2145" decimal(32,4) DEFAULT NULL,
  "t2200" decimal(32,4) DEFAULT NULL,
  "t2215" decimal(32,4) DEFAULT NULL,
  "t2230" decimal(32,4) DEFAULT NULL,
  "t2245" decimal(32,4) DEFAULT NULL,
  "t2300" decimal(32,4) DEFAULT NULL,
  "t2315" decimal(32,4) DEFAULT NULL,
  "t2330" decimal(32,4) DEFAULT NULL,
  "t2345" decimal(32,4) DEFAULT NULL,
  "t2400" decimal(32,4) DEFAULT NULL,
  "createtime" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  "recommend" tinyint(1) DEFAULT NULL,
  "report" tinyint(1) DEFAULT '0' COMMENT '是否上报结果（1：是；0：否）',
  "report_time" datetime DEFAULT NULL COMMENT '上报时间',
  "user_id" varchar(32) DEFAULT NULL COMMENT '操作者ID',
  "succeed" tinyint(1) DEFAULT NULL COMMENT '是否上报成功（1：是；0：否）',
  PRIMARY KEY ("id") USING BTREE,
  UNIQUE KEY "load_city_fc_unique" ("date","city_id","algorithm_id","caliber_id","batch_id") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='地区批次预测负荷表';

--2021/2/25 修改索引
ALTER TABLE `load_city_fc_batch`
DROP INDEX `load_city_fc_unique`,
ADD UNIQUE INDEX `load_city_fc_unique`(`date`, `city_id`, `caliber_id`, `algorithm_id`, `batch_id`) USING BTREE;


--add by yzm 2021-10-12 新增双高负荷表 14257 子 【广东系统】-【新增页面】-双高负荷填报 / 【后端】-【广东系统】-【预测填报】-增加双高负荷填报相关内容
-- ----------------------------
-- Table structure for report_load_his_double_height
-- ----------------------------
CREATE TABLE `report_load_his_double_height`  (
  `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '主键',
  `city_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '城市ID',
  `date` date NOT NULL COMMENT '日期',
  `load_total` decimal(32, 4) DEFAULT NULL COMMENT '双高总负荷',
  `load_reduce` decimal(32, 4) DEFAULT NULL COMMENT '预计压减双高负荷',
  `report_time` datetime(0) DEFAULT NULL COMMENT '提交时间',
  `createtime` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime(0) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `forecast_info_index`(`date`, `city_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '双高负荷表' ROW_FORMAT = Dynamic;


--2021/10/09  ---14253 子 【广东系统】-【新增需求】-新增有序用电报表页面 / 【后端】-【广东系统】-【报表豁免】-新增有序用电报表页面
--4028fa816c8b208b12343da97f90554	有序用电报表	1	OrderlyUseElectricity	20	有序用电报表	navTab	有序用电报表	9999 菜单需要手动添加权限
CREATE TABLE "grid_load_peak" (
  "id" varchar(255) NOT NULL,
  "date" datetime DEFAULT NULL,
  "city_id" varchar(11) DEFAULT NULL COMMENT '城市id',
  "early_peak" decimal(10,4) DEFAULT NULL COMMENT '早峰',
  "early_peak_time" varchar(255) DEFAULT NULL COMMENT '早峰时刻',
  "noon_peak" decimal(10,4) DEFAULT NULL COMMENT '午',
  "noon_peak_time" varchar(255) DEFAULT NULL COMMENT '午峰时刻',
  "night_peak" decimal(10,4) DEFAULT NULL COMMENT '晚峰',
  "night_peak_time" varchar(255) DEFAULT NULL COMMENT '晚峰时刻',
  "updatetime" datetime DEFAULT NULL COMMENT '更新时间',
  "createtime" datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY ("id"),
  KEY "grid_load_peak" ("date","city_id")
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='网供负荷峰值表';

//广东全网上报截止时间设置开关
INSERT INTO `load_guangdong_dev_v3`.`setting_system_init`(`id`, `field`, `value`, `name`, `description`) VALUES ('43', 'province_end_report_time', '1,21:00', '省调上报截至开关', '0开，1关');
--广东累计综合准确率修改
ALTER TABLE report_accuracy_synthesize_cumulative add  "point_accuracy" decimal(10,4) DEFAULT NULL COMMENT '96点累计综合准确率';
ALTER TABLE report_accuracy_synthesize_cumulative add  "min_synthesize_accuracy" decimal(10,4) DEFAULT NULL COMMENT '累计最大负荷准确率';
ALTER TABLE report_accuracy_synthesize_cumulative add  "max_synthesize_accuracy" decimal(10,4) DEFAULT NULL COMMENT '累计最小准确率';
ALTER TABLE report_accuracy_synthesize_cumulative add "energy_synthesize_accuracy" decimal(10,4) DEFAULT NULL COMMENT '累计月电量准确率';

--子网累加
INSERT INTO load_guangdong_dev_v3.setting_system_init (id, field, value, name, description) VALUES ('88', 'subnet_accumulation', '0.945', '子网累加常量', null);
INSERT INTO load_guangdong_dev_v3.algorithm_base_init (id, algorithm_en, algorithm_cn, code, order_no, valid, join_model, type, province_view, city_view) VALUES ('123', 'ZIWANGLEIJIA', '子网累加', '188', 17, 1, '0', 1, 1, 0);



-- 20220302 yangjin 广东增综合台，中央台，广东台预测气象综合指标表

CREATE TABLE "statistics_synthesize_weather_city_day_fc_gdt" (
  "id" varchar(32) NOT NULL COMMENT '96点气象表id',
  "date" date NOT NULL COMMENT '日期',
  "city_id" varchar(32) NOT NULL COMMENT '城市表id',
  "type" tinyint(2) NOT NULL COMMENT '综合气象类型(5实感温度,6寒湿指数,7焓值)',
  "t0000" decimal(10,4) DEFAULT NULL,
  "t0015" decimal(10,4) DEFAULT NULL,
  "t0030" decimal(10,4) DEFAULT NULL,
  "t0045" decimal(10,4) DEFAULT NULL,
  "t0100" decimal(10,4) DEFAULT NULL,
  "t0115" decimal(10,4) DEFAULT NULL,
  "t0130" decimal(10,4) DEFAULT NULL,
  "t0145" decimal(10,4) DEFAULT NULL,
  "t0200" decimal(10,4) DEFAULT NULL,
  "t0215" decimal(10,4) DEFAULT NULL,
  "t0230" decimal(10,4) DEFAULT NULL,
  "t0245" decimal(10,4) DEFAULT NULL,
  "t0300" decimal(10,4) DEFAULT NULL,
  "t0315" decimal(10,4) DEFAULT NULL,
  "t0330" decimal(10,4) DEFAULT NULL,
  "t0345" decimal(10,4) DEFAULT NULL,
  "t0400" decimal(10,4) DEFAULT NULL,
  "t0415" decimal(10,4) DEFAULT NULL,
  "t0430" decimal(10,4) DEFAULT NULL,
  "t0445" decimal(10,4) DEFAULT NULL,
  "t0500" decimal(10,4) DEFAULT NULL,
  "t0515" decimal(10,4) DEFAULT NULL,
  "t0530" decimal(10,4) DEFAULT NULL,
  "t0545" decimal(10,4) DEFAULT NULL,
  "t0600" decimal(10,4) DEFAULT NULL,
  "t0615" decimal(10,4) DEFAULT NULL,
  "t0630" decimal(10,4) DEFAULT NULL,
  "t0645" decimal(10,4) DEFAULT NULL,
  "t0700" decimal(10,4) DEFAULT NULL,
  "t0715" decimal(10,4) DEFAULT NULL,
  "t0730" decimal(10,4) DEFAULT NULL,
  "t0745" decimal(10,4) DEFAULT NULL,
  "t0800" decimal(10,4) DEFAULT NULL,
  "t0815" decimal(10,4) DEFAULT NULL,
  "t0830" decimal(10,4) DEFAULT NULL,
  "t0845" decimal(10,4) DEFAULT NULL,
  "t0900" decimal(10,4) DEFAULT NULL,
  "t0915" decimal(10,4) DEFAULT NULL,
  "t0930" decimal(10,4) DEFAULT NULL,
  "t0945" decimal(10,4) DEFAULT NULL,
  "t1000" decimal(10,4) DEFAULT NULL,
  "t1015" decimal(10,4) DEFAULT NULL,
  "t1030" decimal(10,4) DEFAULT NULL,
  "t1045" decimal(10,4) DEFAULT NULL,
  "t1100" decimal(10,4) DEFAULT NULL,
  "t1115" decimal(10,4) DEFAULT NULL,
  "t1130" decimal(10,4) DEFAULT NULL,
  "t1145" decimal(10,4) DEFAULT NULL,
  "t1200" decimal(10,4) DEFAULT NULL,
  "t1215" decimal(10,4) DEFAULT NULL,
  "t1230" decimal(10,4) DEFAULT NULL,
  "t1245" decimal(10,4) DEFAULT NULL,
  "t1300" decimal(10,4) DEFAULT NULL,
  "t1315" decimal(10,4) DEFAULT NULL,
  "t1330" decimal(10,4) DEFAULT NULL,
  "t1345" decimal(10,4) DEFAULT NULL,
  "t1400" decimal(10,4) DEFAULT NULL,
  "t1415" decimal(10,4) DEFAULT NULL,
  "t1430" decimal(10,4) DEFAULT NULL,
  "t1445" decimal(10,4) DEFAULT NULL,
  "t1500" decimal(10,4) DEFAULT NULL,
  "t1515" decimal(10,4) DEFAULT NULL,
  "t1530" decimal(10,4) DEFAULT NULL,
  "t1545" decimal(10,4) DEFAULT NULL,
  "t1600" decimal(10,4) DEFAULT NULL,
  "t1615" decimal(10,4) DEFAULT NULL,
  "t1630" decimal(10,4) DEFAULT NULL,
  "t1645" decimal(10,4) DEFAULT NULL,
  "t1700" decimal(10,4) DEFAULT NULL,
  "t1715" decimal(10,4) DEFAULT NULL,
  "t1730" decimal(10,4) DEFAULT NULL,
  "t1745" decimal(10,4) DEFAULT NULL,
  "t1800" decimal(10,4) DEFAULT NULL,
  "t1815" decimal(10,4) DEFAULT NULL,
  "t1830" decimal(10,4) DEFAULT NULL,
  "t1845" decimal(10,4) DEFAULT NULL,
  "t1900" decimal(10,4) DEFAULT NULL,
  "t1915" decimal(10,4) DEFAULT NULL,
  "t1930" decimal(10,4) DEFAULT NULL,
  "t1945" decimal(10,4) DEFAULT NULL,
  "t2000" decimal(10,4) DEFAULT NULL,
  "t2015" decimal(10,4) DEFAULT NULL,
  "t2030" decimal(10,4) DEFAULT NULL,
  "t2045" decimal(10,4) DEFAULT NULL,
  "t2100" decimal(10,4) DEFAULT NULL,
  "t2115" decimal(10,4) DEFAULT NULL,
  "t2130" decimal(10,4) DEFAULT NULL,
  "t2145" decimal(10,4) DEFAULT NULL,
  "t2200" decimal(10,4) DEFAULT NULL,
  "t2215" decimal(10,4) DEFAULT NULL,
  "t2230" decimal(10,4) DEFAULT NULL,
  "t2245" decimal(10,4) DEFAULT NULL,
  "t2300" decimal(10,4) DEFAULT NULL,
  "t2315" decimal(10,4) DEFAULT NULL,
  "t2330" decimal(10,4) DEFAULT NULL,
  "t2345" decimal(10,4) DEFAULT NULL,
  "t2400" decimal(10,4) DEFAULT NULL,
  "createtime" datetime DEFAULT CURRENT_TIMESTAMP,
  "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY ("id") USING BTREE,
  UNIQUE KEY "statistics_synthesize_weather_city_day_fc_unique" ("date","city_id","type") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='广东台预测综合气象统计表';



CREATE TABLE "statistics_synthesize_weather_city_day_fc_zht" (
  "id" varchar(32) NOT NULL COMMENT '96点气象表id',
  "date" date NOT NULL COMMENT '日期',
  "city_id" varchar(32) NOT NULL COMMENT '城市表id',
  "type" tinyint(2) NOT NULL COMMENT '综合气象类型(5实感温度,6寒湿指数,7焓值)',
  "t0000" decimal(10,4) DEFAULT NULL,
  "t0015" decimal(10,4) DEFAULT NULL,
  "t0030" decimal(10,4) DEFAULT NULL,
  "t0045" decimal(10,4) DEFAULT NULL,
  "t0100" decimal(10,4) DEFAULT NULL,
  "t0115" decimal(10,4) DEFAULT NULL,
  "t0130" decimal(10,4) DEFAULT NULL,
  "t0145" decimal(10,4) DEFAULT NULL,
  "t0200" decimal(10,4) DEFAULT NULL,
  "t0215" decimal(10,4) DEFAULT NULL,
  "t0230" decimal(10,4) DEFAULT NULL,
  "t0245" decimal(10,4) DEFAULT NULL,
  "t0300" decimal(10,4) DEFAULT NULL,
  "t0315" decimal(10,4) DEFAULT NULL,
  "t0330" decimal(10,4) DEFAULT NULL,
  "t0345" decimal(10,4) DEFAULT NULL,
  "t0400" decimal(10,4) DEFAULT NULL,
  "t0415" decimal(10,4) DEFAULT NULL,
  "t0430" decimal(10,4) DEFAULT NULL,
  "t0445" decimal(10,4) DEFAULT NULL,
  "t0500" decimal(10,4) DEFAULT NULL,
  "t0515" decimal(10,4) DEFAULT NULL,
  "t0530" decimal(10,4) DEFAULT NULL,
  "t0545" decimal(10,4) DEFAULT NULL,
  "t0600" decimal(10,4) DEFAULT NULL,
  "t0615" decimal(10,4) DEFAULT NULL,
  "t0630" decimal(10,4) DEFAULT NULL,
  "t0645" decimal(10,4) DEFAULT NULL,
  "t0700" decimal(10,4) DEFAULT NULL,
  "t0715" decimal(10,4) DEFAULT NULL,
  "t0730" decimal(10,4) DEFAULT NULL,
  "t0745" decimal(10,4) DEFAULT NULL,
  "t0800" decimal(10,4) DEFAULT NULL,
  "t0815" decimal(10,4) DEFAULT NULL,
  "t0830" decimal(10,4) DEFAULT NULL,
  "t0845" decimal(10,4) DEFAULT NULL,
  "t0900" decimal(10,4) DEFAULT NULL,
  "t0915" decimal(10,4) DEFAULT NULL,
  "t0930" decimal(10,4) DEFAULT NULL,
  "t0945" decimal(10,4) DEFAULT NULL,
  "t1000" decimal(10,4) DEFAULT NULL,
  "t1015" decimal(10,4) DEFAULT NULL,
  "t1030" decimal(10,4) DEFAULT NULL,
  "t1045" decimal(10,4) DEFAULT NULL,
  "t1100" decimal(10,4) DEFAULT NULL,
  "t1115" decimal(10,4) DEFAULT NULL,
  "t1130" decimal(10,4) DEFAULT NULL,
  "t1145" decimal(10,4) DEFAULT NULL,
  "t1200" decimal(10,4) DEFAULT NULL,
  "t1215" decimal(10,4) DEFAULT NULL,
  "t1230" decimal(10,4) DEFAULT NULL,
  "t1245" decimal(10,4) DEFAULT NULL,
  "t1300" decimal(10,4) DEFAULT NULL,
  "t1315" decimal(10,4) DEFAULT NULL,
  "t1330" decimal(10,4) DEFAULT NULL,
  "t1345" decimal(10,4) DEFAULT NULL,
  "t1400" decimal(10,4) DEFAULT NULL,
  "t1415" decimal(10,4) DEFAULT NULL,
  "t1430" decimal(10,4) DEFAULT NULL,
  "t1445" decimal(10,4) DEFAULT NULL,
  "t1500" decimal(10,4) DEFAULT NULL,
  "t1515" decimal(10,4) DEFAULT NULL,
  "t1530" decimal(10,4) DEFAULT NULL,
  "t1545" decimal(10,4) DEFAULT NULL,
  "t1600" decimal(10,4) DEFAULT NULL,
  "t1615" decimal(10,4) DEFAULT NULL,
  "t1630" decimal(10,4) DEFAULT NULL,
  "t1645" decimal(10,4) DEFAULT NULL,
  "t1700" decimal(10,4) DEFAULT NULL,
  "t1715" decimal(10,4) DEFAULT NULL,
  "t1730" decimal(10,4) DEFAULT NULL,
  "t1745" decimal(10,4) DEFAULT NULL,
  "t1800" decimal(10,4) DEFAULT NULL,
  "t1815" decimal(10,4) DEFAULT NULL,
  "t1830" decimal(10,4) DEFAULT NULL,
  "t1845" decimal(10,4) DEFAULT NULL,
  "t1900" decimal(10,4) DEFAULT NULL,
  "t1915" decimal(10,4) DEFAULT NULL,
  "t1930" decimal(10,4) DEFAULT NULL,
  "t1945" decimal(10,4) DEFAULT NULL,
  "t2000" decimal(10,4) DEFAULT NULL,
  "t2015" decimal(10,4) DEFAULT NULL,
  "t2030" decimal(10,4) DEFAULT NULL,
  "t2045" decimal(10,4) DEFAULT NULL,
  "t2100" decimal(10,4) DEFAULT NULL,
  "t2115" decimal(10,4) DEFAULT NULL,
  "t2130" decimal(10,4) DEFAULT NULL,
  "t2145" decimal(10,4) DEFAULT NULL,
  "t2200" decimal(10,4) DEFAULT NULL,
  "t2215" decimal(10,4) DEFAULT NULL,
  "t2230" decimal(10,4) DEFAULT NULL,
  "t2245" decimal(10,4) DEFAULT NULL,
  "t2300" decimal(10,4) DEFAULT NULL,
  "t2315" decimal(10,4) DEFAULT NULL,
  "t2330" decimal(10,4) DEFAULT NULL,
  "t2345" decimal(10,4) DEFAULT NULL,
  "t2400" decimal(10,4) DEFAULT NULL,
  "createtime" datetime DEFAULT CURRENT_TIMESTAMP,
  "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY ("id") USING BTREE,
  UNIQUE KEY "statistics_synthesize_weather_city_day_fc_unique" ("date","city_id","type") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='综合台预测综合气象统计表';


CREATE TABLE "statistics_synthesize_weather_city_day_fc_zyt" (
  "id" varchar(32) NOT NULL COMMENT '96点气象表id',
  "date" date NOT NULL COMMENT '日期',
  "city_id" varchar(32) NOT NULL COMMENT '城市表id',
  "type" tinyint(2) NOT NULL COMMENT '综合气象类型(5实感温度,6寒湿指数,7焓值)',
  "t0000" decimal(10,4) DEFAULT NULL,
  "t0015" decimal(10,4) DEFAULT NULL,
  "t0030" decimal(10,4) DEFAULT NULL,
  "t0045" decimal(10,4) DEFAULT NULL,
  "t0100" decimal(10,4) DEFAULT NULL,
  "t0115" decimal(10,4) DEFAULT NULL,
  "t0130" decimal(10,4) DEFAULT NULL,
  "t0145" decimal(10,4) DEFAULT NULL,
  "t0200" decimal(10,4) DEFAULT NULL,
  "t0215" decimal(10,4) DEFAULT NULL,
  "t0230" decimal(10,4) DEFAULT NULL,
  "t0245" decimal(10,4) DEFAULT NULL,
  "t0300" decimal(10,4) DEFAULT NULL,
  "t0315" decimal(10,4) DEFAULT NULL,
  "t0330" decimal(10,4) DEFAULT NULL,
  "t0345" decimal(10,4) DEFAULT NULL,
  "t0400" decimal(10,4) DEFAULT NULL,
  "t0415" decimal(10,4) DEFAULT NULL,
  "t0430" decimal(10,4) DEFAULT NULL,
  "t0445" decimal(10,4) DEFAULT NULL,
  "t0500" decimal(10,4) DEFAULT NULL,
  "t0515" decimal(10,4) DEFAULT NULL,
  "t0530" decimal(10,4) DEFAULT NULL,
  "t0545" decimal(10,4) DEFAULT NULL,
  "t0600" decimal(10,4) DEFAULT NULL,
  "t0615" decimal(10,4) DEFAULT NULL,
  "t0630" decimal(10,4) DEFAULT NULL,
  "t0645" decimal(10,4) DEFAULT NULL,
  "t0700" decimal(10,4) DEFAULT NULL,
  "t0715" decimal(10,4) DEFAULT NULL,
  "t0730" decimal(10,4) DEFAULT NULL,
  "t0745" decimal(10,4) DEFAULT NULL,
  "t0800" decimal(10,4) DEFAULT NULL,
  "t0815" decimal(10,4) DEFAULT NULL,
  "t0830" decimal(10,4) DEFAULT NULL,
  "t0845" decimal(10,4) DEFAULT NULL,
  "t0900" decimal(10,4) DEFAULT NULL,
  "t0915" decimal(10,4) DEFAULT NULL,
  "t0930" decimal(10,4) DEFAULT NULL,
  "t0945" decimal(10,4) DEFAULT NULL,
  "t1000" decimal(10,4) DEFAULT NULL,
  "t1015" decimal(10,4) DEFAULT NULL,
  "t1030" decimal(10,4) DEFAULT NULL,
  "t1045" decimal(10,4) DEFAULT NULL,
  "t1100" decimal(10,4) DEFAULT NULL,
  "t1115" decimal(10,4) DEFAULT NULL,
  "t1130" decimal(10,4) DEFAULT NULL,
  "t1145" decimal(10,4) DEFAULT NULL,
  "t1200" decimal(10,4) DEFAULT NULL,
  "t1215" decimal(10,4) DEFAULT NULL,
  "t1230" decimal(10,4) DEFAULT NULL,
  "t1245" decimal(10,4) DEFAULT NULL,
  "t1300" decimal(10,4) DEFAULT NULL,
  "t1315" decimal(10,4) DEFAULT NULL,
  "t1330" decimal(10,4) DEFAULT NULL,
  "t1345" decimal(10,4) DEFAULT NULL,
  "t1400" decimal(10,4) DEFAULT NULL,
  "t1415" decimal(10,4) DEFAULT NULL,
  "t1430" decimal(10,4) DEFAULT NULL,
  "t1445" decimal(10,4) DEFAULT NULL,
  "t1500" decimal(10,4) DEFAULT NULL,
  "t1515" decimal(10,4) DEFAULT NULL,
  "t1530" decimal(10,4) DEFAULT NULL,
  "t1545" decimal(10,4) DEFAULT NULL,
  "t1600" decimal(10,4) DEFAULT NULL,
  "t1615" decimal(10,4) DEFAULT NULL,
  "t1630" decimal(10,4) DEFAULT NULL,
  "t1645" decimal(10,4) DEFAULT NULL,
  "t1700" decimal(10,4) DEFAULT NULL,
  "t1715" decimal(10,4) DEFAULT NULL,
  "t1730" decimal(10,4) DEFAULT NULL,
  "t1745" decimal(10,4) DEFAULT NULL,
  "t1800" decimal(10,4) DEFAULT NULL,
  "t1815" decimal(10,4) DEFAULT NULL,
  "t1830" decimal(10,4) DEFAULT NULL,
  "t1845" decimal(10,4) DEFAULT NULL,
  "t1900" decimal(10,4) DEFAULT NULL,
  "t1915" decimal(10,4) DEFAULT NULL,
  "t1930" decimal(10,4) DEFAULT NULL,
  "t1945" decimal(10,4) DEFAULT NULL,
  "t2000" decimal(10,4) DEFAULT NULL,
  "t2015" decimal(10,4) DEFAULT NULL,
  "t2030" decimal(10,4) DEFAULT NULL,
  "t2045" decimal(10,4) DEFAULT NULL,
  "t2100" decimal(10,4) DEFAULT NULL,
  "t2115" decimal(10,4) DEFAULT NULL,
  "t2130" decimal(10,4) DEFAULT NULL,
  "t2145" decimal(10,4) DEFAULT NULL,
  "t2200" decimal(10,4) DEFAULT NULL,
  "t2215" decimal(10,4) DEFAULT NULL,
  "t2230" decimal(10,4) DEFAULT NULL,
  "t2245" decimal(10,4) DEFAULT NULL,
  "t2300" decimal(10,4) DEFAULT NULL,
  "t2315" decimal(10,4) DEFAULT NULL,
  "t2330" decimal(10,4) DEFAULT NULL,
  "t2345" decimal(10,4) DEFAULT NULL,
  "t2400" decimal(10,4) DEFAULT NULL,
  "createtime" datetime DEFAULT CURRENT_TIMESTAMP,
  "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY ("id") USING BTREE,
  UNIQUE KEY "statistics_synthesize_weather_city_day_fc_unique" ("date","city_id","type") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='中央台预测综合气象统计表';


-- 2022-05-16 中长期相关sql
INSERT INTO `load_guangdong_dev_v3`.`tsie_menu`(`id`, `name`, `type`, `menupath`, `parentId`, `description`, `targetType`, `rel`, `sort`) VALUES ('4028fa816c8b208b12343da97f90555', '中长期', 1, 'LabelManagement', 'root', '中长期', 'navTab', '中长期', 9999);

INSERT INTO `load_guangdong_dev_v3`.`tsie_menu`(`id`, `name`, `type`, `menupath`, `parentId`, `description`, `targetType`, `rel`, `sort`) VALUES ('4028fa816c8b208b12343da97f90556', '查询影响标签详情', 1, '/web/longForecast/label/getLabelInfo', '4028fa816c8b208b12343da97f90555', '查询影响标签详情', 'navTab', '查询影响标签详情', 9999);
INSERT INTO `load_guangdong_dev_v3`.`tsie_menu`(`id`, `name`, `type`, `menupath`, `parentId`, `description`, `targetType`, `rel`, `sort`) VALUES ('4028fa816c8b208b12343da97f90557', '删除影响标签', 1, '/web/longForecast/label/delete', '4028fa816c8b208b12343da97f90555', '删除影响标签', 'navTab', '删除影响标签', 9999);
INSERT INTO `load_guangdong_dev_v3`.`tsie_menu`(`id`, `name`, `type`, `menupath`, `parentId`, `description`, `targetType`, `rel`, `sort`) VALUES ('4028fa816c8b208b12343da97f90558', '编辑添加影响标签', 1, '/web/longForecast/label/saveOrUpdate', '4028fa816c8b208b12343da97f90555', '编辑添加影响标签', 'navTab', '编辑添加影响标签', 9999);
INSERT INTO `load_guangdong_dev_v3`.`tsie_menu`(`id`, `name`, `type`, `menupath`, `parentId`, `description`, `targetType`, `rel`, `sort`) VALUES ('4028fa816c8b208b12343da97f90559', '批量导入影响标签', 1, '/web/longForecast/label/import', '4028fa816c8b208b12343da97f90555', '批量导入影响标签', 'navTab', '批量导入影响标签', 9999);
INSERT INTO `load_guangdong_dev_v3`.`tsie_menu`(`id`, `name`, `type`, `menupath`, `parentId`, `description`, `targetType`, `rel`, `sort`) VALUES ('4028fa816c8b208b12343da97f90560', '影响标签下拉框查询', 1, '/web/longForecast/label/getLabelType', '4028fa816c8b208b12343da97f90555', '影响标签下拉框查询', 'navTab', '影响标签下拉框查询', 9999);



CREATE TABLE `load_city_fc_long`  (
  `id` varchar(32) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `city_id` varchar(0) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `algorithm_id` varchar(32) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `caliber_id` varchar(32) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `date` date NOT NULL,
  `max_load` decimal(32, 0) DEFAULT NULL COMMENT '最大负荷',
  `max_load_lower_limit` decimal(32, 0) DEFAULT NULL COMMENT '最大负荷下限',
  `max_load_upper_limit` decimal(32, 0) DEFAULT NULL COMMENT '最大负荷上限',
  `min_load` decimal(32, 0) DEFAULT NULL COMMENT '最小负荷',
  `min_load_lower_limit` decimal(32, 0) DEFAULT NULL COMMENT '最小负荷下限',
  `min_load_upper_limit` decimal(32, 0) DEFAULT NULL COMMENT '最小负荷上限',
  `mean_load` decimal(32, 0) DEFAULT NULL COMMENT '平均负荷',
  `mean_load_lower_limit` decimal(32, 0) DEFAULT NULL COMMENT '平均负荷下限',
  `mean_load_upper_limit` decimal(32, 0) DEFAULT NULL COMMENT '平均负荷上限',
  `ele_load` decimal(32, 0) DEFAULT NULL COMMENT '日电量',
  `ele_load_lower_limit` decimal(32, 0) DEFAULT NULL COMMENT '日电量下限',
  `ele_load_upper_limit` decimal(32, 0) DEFAULT NULL COMMENT '日电量上限',
  `createtime` datetime(0) DEFAULT CURRENT_TIMESTAMP,
  `updatetime` datetime(0) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

CREATE TABLE `setting_label_init`  (
  `id` varchar(32) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `year` varchar(32) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL COMMENT '年份',
  `startDate` date NOT NULL COMMENT '开始日期',
  `endDate` date NOT NULL COMMENT '结束日期',
  `labelType` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '标签类型',
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '影响情况描述',
  `createtime` datetime(0) DEFAULT CURRENT_TIMESTAMP,
  `updatetime` datetime(0) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;


CREATE TABLE "statistics_accuracy_weather_city_day_his_zyt" (
  "id" varchar(32) NOT NULL COMMENT '96点气象表id',
  "date" date NOT NULL COMMENT '日期',
  "city_id" varchar(32) NOT NULL COMMENT '城市表id',
  "type" tinyint(2) NOT NULL COMMENT '气象类型(1：湿度，2：温度，3：降雨量，4：风速，5：实感温度,:6：寒湿指数)',
  "t0000" decimal(10,4) DEFAULT NULL,
  "t0015" decimal(10,4) DEFAULT NULL,
  "t0030" decimal(10,4) DEFAULT NULL,
  "t0045" decimal(10,4) DEFAULT NULL,
  "t0100" decimal(10,4) DEFAULT NULL,
  "t0115" decimal(10,4) DEFAULT NULL,
  "t0130" decimal(10,4) DEFAULT NULL,
  "t0145" decimal(10,4) DEFAULT NULL,
  "t0200" decimal(10,4) DEFAULT NULL,
  "t0215" decimal(10,4) DEFAULT NULL,
  "t0230" decimal(10,4) DEFAULT NULL,
  "t0245" decimal(10,4) DEFAULT NULL,
  "t0300" decimal(10,4) DEFAULT NULL,
  "t0315" decimal(10,4) DEFAULT NULL,
  "t0330" decimal(10,4) DEFAULT NULL,
  "t0345" decimal(10,4) DEFAULT NULL,
  "t0400" decimal(10,4) DEFAULT NULL,
  "t0415" decimal(10,4) DEFAULT NULL,
  "t0430" decimal(10,4) DEFAULT NULL,
  "t0445" decimal(10,4) DEFAULT NULL,
  "t0500" decimal(10,4) DEFAULT NULL,
  "t0515" decimal(10,4) DEFAULT NULL,
  "t0530" decimal(10,4) DEFAULT NULL,
  "t0545" decimal(10,4) DEFAULT NULL,
  "t0600" decimal(10,4) DEFAULT NULL,
  "t0615" decimal(10,4) DEFAULT NULL,
  "t0630" decimal(10,4) DEFAULT NULL,
  "t0645" decimal(10,4) DEFAULT NULL,
  "t0700" decimal(10,4) DEFAULT NULL,
  "t0715" decimal(10,4) DEFAULT NULL,
  "t0730" decimal(10,4) DEFAULT NULL,
  "t0745" decimal(10,4) DEFAULT NULL,
  "t0800" decimal(10,4) DEFAULT NULL,
  "t0815" decimal(10,4) DEFAULT NULL,
  "t0830" decimal(10,4) DEFAULT NULL,
  "t0845" decimal(10,4) DEFAULT NULL,
  "t0900" decimal(10,4) DEFAULT NULL,
  "t0915" decimal(10,4) DEFAULT NULL,
  "t0930" decimal(10,4) DEFAULT NULL,
  "t0945" decimal(10,4) DEFAULT NULL,
  "t1000" decimal(10,4) DEFAULT NULL,
  "t1015" decimal(10,4) DEFAULT NULL,
  "t1030" decimal(10,4) DEFAULT NULL,
  "t1045" decimal(10,4) DEFAULT NULL,
  "t1100" decimal(10,4) DEFAULT NULL,
  "t1115" decimal(10,4) DEFAULT NULL,
  "t1130" decimal(10,4) DEFAULT NULL,
  "t1145" decimal(10,4) DEFAULT NULL,
  "t1200" decimal(10,4) DEFAULT NULL,
  "t1215" decimal(10,4) DEFAULT NULL,
  "t1230" decimal(10,4) DEFAULT NULL,
  "t1245" decimal(10,4) DEFAULT NULL,
  "t1300" decimal(10,4) DEFAULT NULL,
  "t1315" decimal(10,4) DEFAULT NULL,
  "t1330" decimal(10,4) DEFAULT NULL,
  "t1345" decimal(10,4) DEFAULT NULL,
  "t1400" decimal(10,4) DEFAULT NULL,
  "t1415" decimal(10,4) DEFAULT NULL,
  "t1430" decimal(10,4) DEFAULT NULL,
  "t1445" decimal(10,4) DEFAULT NULL,
  "t1500" decimal(10,4) DEFAULT NULL,
  "t1515" decimal(10,4) DEFAULT NULL,
  "t1530" decimal(10,4) DEFAULT NULL,
  "t1545" decimal(10,4) DEFAULT NULL,
  "t1600" decimal(10,4) DEFAULT NULL,
  "t1615" decimal(10,4) DEFAULT NULL,
  "t1630" decimal(10,4) DEFAULT NULL,
  "t1645" decimal(10,4) DEFAULT NULL,
  "t1700" decimal(10,4) DEFAULT NULL,
  "t1715" decimal(10,4) DEFAULT NULL,
  "t1730" decimal(10,4) DEFAULT NULL,
  "t1745" decimal(10,4) DEFAULT NULL,
  "t1800" decimal(10,4) DEFAULT NULL,
  "t1815" decimal(10,4) DEFAULT NULL,
  "t1830" decimal(10,4) DEFAULT NULL,
  "t1845" decimal(10,4) DEFAULT NULL,
  "t1900" decimal(10,4) DEFAULT NULL,
  "t1915" decimal(10,4) DEFAULT NULL,
  "t1930" decimal(10,4) DEFAULT NULL,
  "t1945" decimal(10,4) DEFAULT NULL,
  "t2000" decimal(10,4) DEFAULT NULL,
  "t2015" decimal(10,4) DEFAULT NULL,
  "t2030" decimal(10,4) DEFAULT NULL,
  "t2045" decimal(10,4) DEFAULT NULL,
  "t2100" decimal(10,4) DEFAULT NULL,
  "t2115" decimal(10,4) DEFAULT NULL,
  "t2130" decimal(10,4) DEFAULT NULL,
  "t2145" decimal(10,4) DEFAULT NULL,
  "t2200" decimal(10,4) DEFAULT NULL,
  "t2215" decimal(10,4) DEFAULT NULL,
  "t2230" decimal(10,4) DEFAULT NULL,
  "t2245" decimal(10,4) DEFAULT NULL,
  "t2300" decimal(10,4) DEFAULT NULL,
  "t2315" decimal(10,4) DEFAULT NULL,
  "t2330" decimal(10,4) DEFAULT NULL,
  "t2345" decimal(10,4) DEFAULT NULL,
  "t2400" decimal(10,4) DEFAULT NULL,
  "avg" decimal(10,4) DEFAULT NULL COMMENT '平均准确率',
  "createtime" datetime DEFAULT CURRENT_TIMESTAMP,
  "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY ("id") USING BTREE,
  UNIQUE KEY "statistics_accuracy_weather_city_day_his_unique" ("date","city_id","type") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='气象准确率统计表（日）';

CREATE TABLE "statistics_accuracy_weather_city_day_his_zht" (
  "id" varchar(32) NOT NULL COMMENT '96点气象表id',
  "date" date NOT NULL COMMENT '日期',
  "city_id" varchar(32) NOT NULL COMMENT '城市表id',
  "type" tinyint(2) NOT NULL COMMENT '气象类型(1：湿度，2：温度，3：降雨量，4：风速，5：实感温度,:6：寒湿指数)',
  "t0000" decimal(10,4) DEFAULT NULL,
  "t0015" decimal(10,4) DEFAULT NULL,
  "t0030" decimal(10,4) DEFAULT NULL,
  "t0045" decimal(10,4) DEFAULT NULL,
  "t0100" decimal(10,4) DEFAULT NULL,
  "t0115" decimal(10,4) DEFAULT NULL,
  "t0130" decimal(10,4) DEFAULT NULL,
  "t0145" decimal(10,4) DEFAULT NULL,
  "t0200" decimal(10,4) DEFAULT NULL,
  "t0215" decimal(10,4) DEFAULT NULL,
  "t0230" decimal(10,4) DEFAULT NULL,
  "t0245" decimal(10,4) DEFAULT NULL,
  "t0300" decimal(10,4) DEFAULT NULL,
  "t0315" decimal(10,4) DEFAULT NULL,
  "t0330" decimal(10,4) DEFAULT NULL,
  "t0345" decimal(10,4) DEFAULT NULL,
  "t0400" decimal(10,4) DEFAULT NULL,
  "t0415" decimal(10,4) DEFAULT NULL,
  "t0430" decimal(10,4) DEFAULT NULL,
  "t0445" decimal(10,4) DEFAULT NULL,
  "t0500" decimal(10,4) DEFAULT NULL,
  "t0515" decimal(10,4) DEFAULT NULL,
  "t0530" decimal(10,4) DEFAULT NULL,
  "t0545" decimal(10,4) DEFAULT NULL,
  "t0600" decimal(10,4) DEFAULT NULL,
  "t0615" decimal(10,4) DEFAULT NULL,
  "t0630" decimal(10,4) DEFAULT NULL,
  "t0645" decimal(10,4) DEFAULT NULL,
  "t0700" decimal(10,4) DEFAULT NULL,
  "t0715" decimal(10,4) DEFAULT NULL,
  "t0730" decimal(10,4) DEFAULT NULL,
  "t0745" decimal(10,4) DEFAULT NULL,
  "t0800" decimal(10,4) DEFAULT NULL,
  "t0815" decimal(10,4) DEFAULT NULL,
  "t0830" decimal(10,4) DEFAULT NULL,
  "t0845" decimal(10,4) DEFAULT NULL,
  "t0900" decimal(10,4) DEFAULT NULL,
  "t0915" decimal(10,4) DEFAULT NULL,
  "t0930" decimal(10,4) DEFAULT NULL,
  "t0945" decimal(10,4) DEFAULT NULL,
  "t1000" decimal(10,4) DEFAULT NULL,
  "t1015" decimal(10,4) DEFAULT NULL,
  "t1030" decimal(10,4) DEFAULT NULL,
  "t1045" decimal(10,4) DEFAULT NULL,
  "t1100" decimal(10,4) DEFAULT NULL,
  "t1115" decimal(10,4) DEFAULT NULL,
  "t1130" decimal(10,4) DEFAULT NULL,
  "t1145" decimal(10,4) DEFAULT NULL,
  "t1200" decimal(10,4) DEFAULT NULL,
  "t1215" decimal(10,4) DEFAULT NULL,
  "t1230" decimal(10,4) DEFAULT NULL,
  "t1245" decimal(10,4) DEFAULT NULL,
  "t1300" decimal(10,4) DEFAULT NULL,
  "t1315" decimal(10,4) DEFAULT NULL,
  "t1330" decimal(10,4) DEFAULT NULL,
  "t1345" decimal(10,4) DEFAULT NULL,
  "t1400" decimal(10,4) DEFAULT NULL,
  "t1415" decimal(10,4) DEFAULT NULL,
  "t1430" decimal(10,4) DEFAULT NULL,
  "t1445" decimal(10,4) DEFAULT NULL,
  "t1500" decimal(10,4) DEFAULT NULL,
  "t1515" decimal(10,4) DEFAULT NULL,
  "t1530" decimal(10,4) DEFAULT NULL,
  "t1545" decimal(10,4) DEFAULT NULL,
  "t1600" decimal(10,4) DEFAULT NULL,
  "t1615" decimal(10,4) DEFAULT NULL,
  "t1630" decimal(10,4) DEFAULT NULL,
  "t1645" decimal(10,4) DEFAULT NULL,
  "t1700" decimal(10,4) DEFAULT NULL,
  "t1715" decimal(10,4) DEFAULT NULL,
  "t1730" decimal(10,4) DEFAULT NULL,
  "t1745" decimal(10,4) DEFAULT NULL,
  "t1800" decimal(10,4) DEFAULT NULL,
  "t1815" decimal(10,4) DEFAULT NULL,
  "t1830" decimal(10,4) DEFAULT NULL,
  "t1845" decimal(10,4) DEFAULT NULL,
  "t1900" decimal(10,4) DEFAULT NULL,
  "t1915" decimal(10,4) DEFAULT NULL,
  "t1930" decimal(10,4) DEFAULT NULL,
  "t1945" decimal(10,4) DEFAULT NULL,
  "t2000" decimal(10,4) DEFAULT NULL,
  "t2015" decimal(10,4) DEFAULT NULL,
  "t2030" decimal(10,4) DEFAULT NULL,
  "t2045" decimal(10,4) DEFAULT NULL,
  "t2100" decimal(10,4) DEFAULT NULL,
  "t2115" decimal(10,4) DEFAULT NULL,
  "t2130" decimal(10,4) DEFAULT NULL,
  "t2145" decimal(10,4) DEFAULT NULL,
  "t2200" decimal(10,4) DEFAULT NULL,
  "t2215" decimal(10,4) DEFAULT NULL,
  "t2230" decimal(10,4) DEFAULT NULL,
  "t2245" decimal(10,4) DEFAULT NULL,
  "t2300" decimal(10,4) DEFAULT NULL,
  "t2315" decimal(10,4) DEFAULT NULL,
  "t2330" decimal(10,4) DEFAULT NULL,
  "t2345" decimal(10,4) DEFAULT NULL,
  "t2400" decimal(10,4) DEFAULT NULL,
  "avg" decimal(10,4) DEFAULT NULL COMMENT '平均准确率',
  "createtime" datetime DEFAULT CURRENT_TIMESTAMP,
  "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY ("id") USING BTREE,
  UNIQUE KEY "statistics_accuracy_weather_city_day_his_unique" ("date","city_id","type") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='气象准确率统计表（日）';
CREATE TABLE "statistics_accuracy_weather_city_day_his_gdt" (
  "id" varchar(32) NOT NULL COMMENT '96点气象表id',
  "date" date NOT NULL COMMENT '日期',
  "city_id" varchar(32) NOT NULL COMMENT '城市表id',
  "type" tinyint(2) NOT NULL COMMENT '气象类型(1：湿度，2：温度，3：降雨量，4：风速，5：实感温度,:6：寒湿指数)',
  "t0000" decimal(10,4) DEFAULT NULL,
  "t0015" decimal(10,4) DEFAULT NULL,
  "t0030" decimal(10,4) DEFAULT NULL,
  "t0045" decimal(10,4) DEFAULT NULL,
  "t0100" decimal(10,4) DEFAULT NULL,
  "t0115" decimal(10,4) DEFAULT NULL,
  "t0130" decimal(10,4) DEFAULT NULL,
  "t0145" decimal(10,4) DEFAULT NULL,
  "t0200" decimal(10,4) DEFAULT NULL,
  "t0215" decimal(10,4) DEFAULT NULL,
  "t0230" decimal(10,4) DEFAULT NULL,
  "t0245" decimal(10,4) DEFAULT NULL,
  "t0300" decimal(10,4) DEFAULT NULL,
  "t0315" decimal(10,4) DEFAULT NULL,
  "t0330" decimal(10,4) DEFAULT NULL,
  "t0345" decimal(10,4) DEFAULT NULL,
  "t0400" decimal(10,4) DEFAULT NULL,
  "t0415" decimal(10,4) DEFAULT NULL,
  "t0430" decimal(10,4) DEFAULT NULL,
  "t0445" decimal(10,4) DEFAULT NULL,
  "t0500" decimal(10,4) DEFAULT NULL,
  "t0515" decimal(10,4) DEFAULT NULL,
  "t0530" decimal(10,4) DEFAULT NULL,
  "t0545" decimal(10,4) DEFAULT NULL,
  "t0600" decimal(10,4) DEFAULT NULL,
  "t0615" decimal(10,4) DEFAULT NULL,
  "t0630" decimal(10,4) DEFAULT NULL,
  "t0645" decimal(10,4) DEFAULT NULL,
  "t0700" decimal(10,4) DEFAULT NULL,
  "t0715" decimal(10,4) DEFAULT NULL,
  "t0730" decimal(10,4) DEFAULT NULL,
  "t0745" decimal(10,4) DEFAULT NULL,
  "t0800" decimal(10,4) DEFAULT NULL,
  "t0815" decimal(10,4) DEFAULT NULL,
  "t0830" decimal(10,4) DEFAULT NULL,
  "t0845" decimal(10,4) DEFAULT NULL,
  "t0900" decimal(10,4) DEFAULT NULL,
  "t0915" decimal(10,4) DEFAULT NULL,
  "t0930" decimal(10,4) DEFAULT NULL,
  "t0945" decimal(10,4) DEFAULT NULL,
  "t1000" decimal(10,4) DEFAULT NULL,
  "t1015" decimal(10,4) DEFAULT NULL,
  "t1030" decimal(10,4) DEFAULT NULL,
  "t1045" decimal(10,4) DEFAULT NULL,
  "t1100" decimal(10,4) DEFAULT NULL,
  "t1115" decimal(10,4) DEFAULT NULL,
  "t1130" decimal(10,4) DEFAULT NULL,
  "t1145" decimal(10,4) DEFAULT NULL,
  "t1200" decimal(10,4) DEFAULT NULL,
  "t1215" decimal(10,4) DEFAULT NULL,
  "t1230" decimal(10,4) DEFAULT NULL,
  "t1245" decimal(10,4) DEFAULT NULL,
  "t1300" decimal(10,4) DEFAULT NULL,
  "t1315" decimal(10,4) DEFAULT NULL,
  "t1330" decimal(10,4) DEFAULT NULL,
  "t1345" decimal(10,4) DEFAULT NULL,
  "t1400" decimal(10,4) DEFAULT NULL,
  "t1415" decimal(10,4) DEFAULT NULL,
  "t1430" decimal(10,4) DEFAULT NULL,
  "t1445" decimal(10,4) DEFAULT NULL,
  "t1500" decimal(10,4) DEFAULT NULL,
  "t1515" decimal(10,4) DEFAULT NULL,
  "t1530" decimal(10,4) DEFAULT NULL,
  "t1545" decimal(10,4) DEFAULT NULL,
  "t1600" decimal(10,4) DEFAULT NULL,
  "t1615" decimal(10,4) DEFAULT NULL,
  "t1630" decimal(10,4) DEFAULT NULL,
  "t1645" decimal(10,4) DEFAULT NULL,
  "t1700" decimal(10,4) DEFAULT NULL,
  "t1715" decimal(10,4) DEFAULT NULL,
  "t1730" decimal(10,4) DEFAULT NULL,
  "t1745" decimal(10,4) DEFAULT NULL,
  "t1800" decimal(10,4) DEFAULT NULL,
  "t1815" decimal(10,4) DEFAULT NULL,
  "t1830" decimal(10,4) DEFAULT NULL,
  "t1845" decimal(10,4) DEFAULT NULL,
  "t1900" decimal(10,4) DEFAULT NULL,
  "t1915" decimal(10,4) DEFAULT NULL,
  "t1930" decimal(10,4) DEFAULT NULL,
  "t1945" decimal(10,4) DEFAULT NULL,
  "t2000" decimal(10,4) DEFAULT NULL,
  "t2015" decimal(10,4) DEFAULT NULL,
  "t2030" decimal(10,4) DEFAULT NULL,
  "t2045" decimal(10,4) DEFAULT NULL,
  "t2100" decimal(10,4) DEFAULT NULL,
  "t2115" decimal(10,4) DEFAULT NULL,
  "t2130" decimal(10,4) DEFAULT NULL,
  "t2145" decimal(10,4) DEFAULT NULL,
  "t2200" decimal(10,4) DEFAULT NULL,
  "t2215" decimal(10,4) DEFAULT NULL,
  "t2230" decimal(10,4) DEFAULT NULL,
  "t2245" decimal(10,4) DEFAULT NULL,
  "t2300" decimal(10,4) DEFAULT NULL,
  "t2315" decimal(10,4) DEFAULT NULL,
  "t2330" decimal(10,4) DEFAULT NULL,
  "t2345" decimal(10,4) DEFAULT NULL,
  "t2400" decimal(10,4) DEFAULT NULL,
  "avg" decimal(10,4) DEFAULT NULL COMMENT '平均准确率',
  "createtime" datetime DEFAULT CURRENT_TIMESTAMP,
  "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY ("id") USING BTREE,
  UNIQUE KEY "statistics_accuracy_weather_city_day_his_unique" ("date","city_id","type") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='气象准确率统计表（日）';

CREATE TABLE "statistics_accuracy_weather_city_month_his_gdt" (
  "id" varchar(32) NOT NULL COMMENT 'id',
  "year" varchar(4) NOT NULL COMMENT '年',
  "month" varchar(2) NOT NULL COMMENT '月',
  "city_id" varchar(32) NOT NULL COMMENT '城市表id',
  "type" tinyint(2) NOT NULL COMMENT '气象类型(1：湿度，2：温度，3：降雨量，4：风速，5：实感温度,:6：寒湿指数)',
  "day01" decimal(10,4) DEFAULT NULL,
  "day02" decimal(10,4) DEFAULT NULL,
  "day03" decimal(10,4) DEFAULT NULL,
  "day04" decimal(10,4) DEFAULT NULL,
  "day05" decimal(10,4) DEFAULT NULL,
  "day06" decimal(10,4) DEFAULT NULL,
  "day07" decimal(10,4) DEFAULT NULL,
  "day08" decimal(10,4) DEFAULT NULL,
  "day09" decimal(10,4) DEFAULT NULL,
  "day10" decimal(10,4) DEFAULT NULL,
  "day11" decimal(10,4) DEFAULT NULL,
  "day12" decimal(10,4) DEFAULT NULL,
  "day13" decimal(10,4) DEFAULT NULL,
  "day14" decimal(10,4) DEFAULT NULL,
  "day15" decimal(10,4) DEFAULT NULL,
  "day16" decimal(10,4) DEFAULT NULL,
  "day17" decimal(10,4) DEFAULT NULL,
  "day18" decimal(10,4) DEFAULT NULL,
  "day19" decimal(10,4) DEFAULT NULL,
  "day20" decimal(10,4) DEFAULT NULL,
  "day21" decimal(10,4) DEFAULT NULL,
  "day22" decimal(10,4) DEFAULT NULL,
  "day23" decimal(10,4) DEFAULT NULL,
  "day24" decimal(10,4) DEFAULT NULL,
  "day25" decimal(10,4) DEFAULT NULL,
  "day26" decimal(10,4) DEFAULT NULL,
  "day27" decimal(10,4) DEFAULT NULL,
  "day28" decimal(10,4) DEFAULT NULL,
  "day29" decimal(10,4) DEFAULT NULL,
  "day30" decimal(10,4) DEFAULT NULL,
  "day31" decimal(10,4) DEFAULT NULL,
  "avg" decimal(10,4) DEFAULT NULL COMMENT '平均准确率',
  "createtime" datetime DEFAULT CURRENT_TIMESTAMP,
  "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY ("id") USING BTREE,
  UNIQUE KEY "statistics_accuracy_weather_city_month_his_unique" ("year","month","city_id","type") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='月气象准确率统计表';

CREATE TABLE "statistics_accuracy_weather_city_month_his_zht" (
  "id" varchar(32) NOT NULL COMMENT 'id',
  "year" varchar(4) NOT NULL COMMENT '年',
  "month" varchar(2) NOT NULL COMMENT '月',
  "city_id" varchar(32) NOT NULL COMMENT '城市表id',
  "type" tinyint(2) NOT NULL COMMENT '气象类型(1：湿度，2：温度，3：降雨量，4：风速，5：实感温度,:6：寒湿指数)',
  "day01" decimal(10,4) DEFAULT NULL,
  "day02" decimal(10,4) DEFAULT NULL,
  "day03" decimal(10,4) DEFAULT NULL,
  "day04" decimal(10,4) DEFAULT NULL,
  "day05" decimal(10,4) DEFAULT NULL,
  "day06" decimal(10,4) DEFAULT NULL,
  "day07" decimal(10,4) DEFAULT NULL,
  "day08" decimal(10,4) DEFAULT NULL,
  "day09" decimal(10,4) DEFAULT NULL,
  "day10" decimal(10,4) DEFAULT NULL,
  "day11" decimal(10,4) DEFAULT NULL,
  "day12" decimal(10,4) DEFAULT NULL,
  "day13" decimal(10,4) DEFAULT NULL,
  "day14" decimal(10,4) DEFAULT NULL,
  "day15" decimal(10,4) DEFAULT NULL,
  "day16" decimal(10,4) DEFAULT NULL,
  "day17" decimal(10,4) DEFAULT NULL,
  "day18" decimal(10,4) DEFAULT NULL,
  "day19" decimal(10,4) DEFAULT NULL,
  "day20" decimal(10,4) DEFAULT NULL,
  "day21" decimal(10,4) DEFAULT NULL,
  "day22" decimal(10,4) DEFAULT NULL,
  "day23" decimal(10,4) DEFAULT NULL,
  "day24" decimal(10,4) DEFAULT NULL,
  "day25" decimal(10,4) DEFAULT NULL,
  "day26" decimal(10,4) DEFAULT NULL,
  "day27" decimal(10,4) DEFAULT NULL,
  "day28" decimal(10,4) DEFAULT NULL,
  "day29" decimal(10,4) DEFAULT NULL,
  "day30" decimal(10,4) DEFAULT NULL,
  "day31" decimal(10,4) DEFAULT NULL,
  "avg" decimal(10,4) DEFAULT NULL COMMENT '平均准确率',
  "createtime" datetime DEFAULT CURRENT_TIMESTAMP,
  "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY ("id") USING BTREE,
  UNIQUE KEY "statistics_accuracy_weather_city_month_his_unique" ("year","month","city_id","type") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='月气象准确率统计表';

CREATE TABLE "statistics_accuracy_weather_city_month_his_zyt" (
  "id" varchar(32) NOT NULL COMMENT 'id',
  "year" varchar(4) NOT NULL COMMENT '年',
  "month" varchar(2) NOT NULL COMMENT '月',
  "city_id" varchar(32) NOT NULL COMMENT '城市表id',
  "type" tinyint(2) NOT NULL COMMENT '气象类型(1：湿度，2：温度，3：降雨量，4：风速，5：实感温度,:6：寒湿指数)',
  "day01" decimal(10,4) DEFAULT NULL,
  "day02" decimal(10,4) DEFAULT NULL,
  "day03" decimal(10,4) DEFAULT NULL,
  "day04" decimal(10,4) DEFAULT NULL,
  "day05" decimal(10,4) DEFAULT NULL,
  "day06" decimal(10,4) DEFAULT NULL,
  "day07" decimal(10,4) DEFAULT NULL,
  "day08" decimal(10,4) DEFAULT NULL,
  "day09" decimal(10,4) DEFAULT NULL,
  "day10" decimal(10,4) DEFAULT NULL,
  "day11" decimal(10,4) DEFAULT NULL,
  "day12" decimal(10,4) DEFAULT NULL,
  "day13" decimal(10,4) DEFAULT NULL,
  "day14" decimal(10,4) DEFAULT NULL,
  "day15" decimal(10,4) DEFAULT NULL,
  "day16" decimal(10,4) DEFAULT NULL,
  "day17" decimal(10,4) DEFAULT NULL,
  "day18" decimal(10,4) DEFAULT NULL,
  "day19" decimal(10,4) DEFAULT NULL,
  "day20" decimal(10,4) DEFAULT NULL,
  "day21" decimal(10,4) DEFAULT NULL,
  "day22" decimal(10,4) DEFAULT NULL,
  "day23" decimal(10,4) DEFAULT NULL,
  "day24" decimal(10,4) DEFAULT NULL,
  "day25" decimal(10,4) DEFAULT NULL,
  "day26" decimal(10,4) DEFAULT NULL,
  "day27" decimal(10,4) DEFAULT NULL,
  "day28" decimal(10,4) DEFAULT NULL,
  "day29" decimal(10,4) DEFAULT NULL,
  "day30" decimal(10,4) DEFAULT NULL,
  "day31" decimal(10,4) DEFAULT NULL,
  "avg" decimal(10,4) DEFAULT NULL COMMENT '平均准确率',
  "createtime" datetime DEFAULT CURRENT_TIMESTAMP,
  "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY ("id") USING BTREE,
  UNIQUE KEY "statistics_accuracy_weather_city_month_his_unique" ("year","month","city_id","type") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='月气象准确率统计表';

CREATE TABLE "statistics_accuracy_weather_city_year_his_gdt" (
  "id" varchar(32) NOT NULL COMMENT 'id',
  "year" varchar(4) NOT NULL COMMENT '年',
  "city_id" varchar(32) NOT NULL COMMENT '城市表id',
  "type" tinyint(2) NOT NULL COMMENT '气象类型(1：湿度，2：温度，3：降雨量，4：风速，5：实感温度,:6：寒湿指数)',
  "month01" decimal(10,4) DEFAULT NULL,
  "month02" decimal(10,4) DEFAULT NULL,
  "month03" decimal(10,4) DEFAULT NULL,
  "month04" decimal(10,4) DEFAULT NULL,
  "month05" decimal(10,4) DEFAULT NULL,
  "month06" decimal(10,4) DEFAULT NULL,
  "month07" decimal(10,4) DEFAULT NULL,
  "month08" decimal(10,4) DEFAULT NULL,
  "month09" decimal(10,4) DEFAULT NULL,
  "month10" decimal(10,4) DEFAULT NULL,
  "month11" decimal(10,4) DEFAULT NULL,
  "month12" decimal(10,4) DEFAULT NULL,
  "avg" decimal(10,4) DEFAULT NULL COMMENT '平均准确率',
  "createtime" datetime DEFAULT CURRENT_TIMESTAMP,
  "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY ("id") USING BTREE,
  UNIQUE KEY "statistics_accuracy_weather_city_year_his_unique" ("year","city_id","type") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='年气象准确率统计表';

CREATE TABLE "statistics_accuracy_weather_city_year_his_zht" (
  "id" varchar(32) NOT NULL COMMENT 'id',
  "year" varchar(4) NOT NULL COMMENT '年',
  "city_id" varchar(32) NOT NULL COMMENT '城市表id',
  "type" tinyint(2) NOT NULL COMMENT '气象类型(1：湿度，2：温度，3：降雨量，4：风速，5：实感温度,:6：寒湿指数)',
  "month01" decimal(10,4) DEFAULT NULL,
  "month02" decimal(10,4) DEFAULT NULL,
  "month03" decimal(10,4) DEFAULT NULL,
  "month04" decimal(10,4) DEFAULT NULL,
  "month05" decimal(10,4) DEFAULT NULL,
  "month06" decimal(10,4) DEFAULT NULL,
  "month07" decimal(10,4) DEFAULT NULL,
  "month08" decimal(10,4) DEFAULT NULL,
  "month09" decimal(10,4) DEFAULT NULL,
  "month10" decimal(10,4) DEFAULT NULL,
  "month11" decimal(10,4) DEFAULT NULL,
  "month12" decimal(10,4) DEFAULT NULL,
  "avg" decimal(10,4) DEFAULT NULL COMMENT '平均准确率',
  "createtime" datetime DEFAULT CURRENT_TIMESTAMP,
  "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY ("id") USING BTREE,
  UNIQUE KEY "statistics_accuracy_weather_city_year_his_unique" ("year","city_id","type") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='年气象准确率统计表';


CREATE TABLE "statistics_accuracy_weather_city_year_his_zyt" (
  "id" varchar(32) NOT NULL COMMENT 'id',
  "year" varchar(4) NOT NULL COMMENT '年',
  "city_id" varchar(32) NOT NULL COMMENT '城市表id',
  "type" tinyint(2) NOT NULL COMMENT '气象类型(1：湿度，2：温度，3：降雨量，4：风速，5：实感温度,:6：寒湿指数)',
  "month01" decimal(10,4) DEFAULT NULL,
  "month02" decimal(10,4) DEFAULT NULL,
  "month03" decimal(10,4) DEFAULT NULL,
  "month04" decimal(10,4) DEFAULT NULL,
  "month05" decimal(10,4) DEFAULT NULL,
  "month06" decimal(10,4) DEFAULT NULL,
  "month07" decimal(10,4) DEFAULT NULL,
  "month08" decimal(10,4) DEFAULT NULL,
  "month09" decimal(10,4) DEFAULT NULL,
  "month10" decimal(10,4) DEFAULT NULL,
  "month11" decimal(10,4) DEFAULT NULL,
  "month12" decimal(10,4) DEFAULT NULL,
  "avg" decimal(10,4) DEFAULT NULL COMMENT '平均准确率',
  "createtime" datetime DEFAULT CURRENT_TIMESTAMP,
  "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY ("id") USING BTREE,
  UNIQUE KEY "statistics_accuracy_weather_city_year_his_unique" ("year","city_id","type") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='年气象准确率统计表';

CREATE TABLE "weather_feature_city_day_long_fc_service" (
  "id" varchar(32) NOT NULL COMMENT 'id',
  "city_id" varchar(32) NOT NULL COMMENT '城市id',
  "date" date NOT NULL COMMENT '日期',
  "highest_temperature" decimal(32,2) DEFAULT NULL COMMENT '最高温度',
  "lowest_temperature" decimal(32,2) DEFAULT NULL COMMENT '最低温度',
  "ave_temperature" decimal(32,2) DEFAULT NULL COMMENT '平均温度',
  "createtime" datetime DEFAULT CURRENT_TIMESTAMP,
  "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY ("id") USING BTREE,
  UNIQUE KEY "weather_feature_city_day_long_fc_service" ("city_id","date") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='中长期气象日特性表';

-- 中长期预测查询页面menu
INSERT INTO `load_guangdong_dev_v3`.`tsie_menu`(`id`, `name`, `type`, `menupath`, `parentId`, `description`, `targetType`, `rel`, `sort`) VALUES ('4028fa816c8b208b12343da97f90123', '中长期预测查询页面', 1, 'ForecastCorrection', 'root', '中长期预测查询页面', 'navTab', '中长期预测查询页面', 9999);
INSERT INTO `load_guangdong_dev_v3`.`tsie_menu`(`id`, `name`, `type`, `menupath`, `parentId`, `description`, `targetType`, `rel`, `sort`) VALUES ('4028fa816c8b208b12343da97f90234', '中长期预测查询', 1, '/web/mediumAndLong/fcData', '4028fa816c8b208b12343da97f90123', '中长期预测查询', 'navTab', '中长期预测查询', 9999);
INSERT INTO `load_guangdong_dev_v3`.`tsie_menu`(`id`, `name`, `type`, `menupath`, `parentId`, `description`, `targetType`, `rel`, `sort`) VALUES ('4028fa816c8b208b12343da97f90623', '中长期预测手动预测', 1, '/web/mediumAndLong/manualPrediction', '4028fa816c8b208b12343da97f90123', '中长期预测手动预测', 'navTab', '中长期预测查询', 9999);
INSERT INTO `load_guangdong_dev_v3`.`tsie_menu`(`id`, `name`, `type`, `menupath`, `parentId`, `description`, `targetType`, `rel`, `sort`) VALUES ('4028fa816c8b208b12343da97f90asd', '中长期预测手动预测轮询', 1, '/web/mediumAndLong/polling', '4028fa816c8b208b12343da97f90123', '中长期预测手动预测轮询', 'navTab', '中长期预测手动预测轮询', 9999);
INSERT INTO `load_guangdong_dev_v3`.`tsie_menu_role` (`id`, `menuid`, `roleid`) VALUES ('40287f9780cb9ca20180cba70d1b002x', '4028fa816c8b208b12343da97f90123', '1');
INSERT INTO `load_guangdong_dev_v3`.`tsie_menu_role` (`id`, `menuid`, `roleid`) VALUES ('40287f9780cb9ca20180cba70d1b0033', '4028fa816c8b208b12343da97f90234', '1');
INSERT INTO `load_guangdong_dev_v3`.`tsie_menu_role` (`id`, `menuid`, `roleid`) VALUES ('40287f9780cb9ca20180cba70d1b0042', '4028fa816c8b208b12343da97f90623', '1');
INSERT INTO `load_guangdong_dev_v3`.`tsie_menu_role` (`id`, `menuid`, `roleid`) VALUES ('40287f9780cb9ca20180cba70d1b00ww', '4028fa816c8b208b12343da97f90asd', '1');
--调整免考逻辑
alter table setting_check_basic add number varchar(32) comment '序列号' ;
alter table setting_check_basic add check_user varchar(32) comment '审核人id';
alter table setting_check_basic add file_name varchar(32) comment '文件名';
alter table setting_check_basic add pass_reason varchar(255) comment '审核理由';


-- 预测数据发送服务端信息属性表
DROP TABLE IF EXISTS `load_fc_send_server_config`;
CREATE TABLE `load_fc_send_server_config`  (
  `id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `taskType` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `userName` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `password` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `ip` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `port` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `uploadDir` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `outputFileDir` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

INSERT INTO `load_fc_send_server_config` VALUES ('e4e4e57b7c92b1e0017c92c11b8e0016', '1', 'fhyc_qn', 'qn_fhyc@123', '*************', '9999', '/home/<USER>/gpdc/ops/ops_gdnew_web/ops_jnlp/ops_jnlp_service/files/toqn/', '/home/<USER>/data');


-- 实际气象数据缺失监控表
CREATE TABLE "weather_his_monitor_stats" (
                                             "id" varchar(32) NOT NULL COMMENT '96点气象表ID',
                                             "date" date NOT NULL COMMENT '日期',
                                             "city_id" varchar(32) NOT NULL COMMENT '潮州	1\r\n东莞	2\r\n佛山	3\r\n广州	4\r\n河源	5\r\n惠州	6\r\n江门	7\r\n揭阳	8\r\n茂名	9\r\n梅州	10\r\n清远	11\r\n汕头	12\r\n汕尾	13\r\n韶关	14\r\n深圳	15\r\n阳江	16\r\n云浮	17\r\n湛江	18\r\n肇庆	19\r\n中山	20\r\n珠海	21\r\n',
                                             "type" tinyint(2) DEFAULT NULL COMMENT '气象类型(1：湿度，2：温度，3：降雨量，4：风速)',
                                             "t0000" decimal(32,2) DEFAULT NULL,
                                             "t0015" decimal(32,2) DEFAULT NULL,
                                             "t0030" decimal(32,2) DEFAULT NULL,
                                             "t0045" decimal(32,2) DEFAULT NULL,
                                             "t0100" decimal(32,2) DEFAULT NULL,
                                             "t0115" decimal(32,2) DEFAULT NULL,
                                             "t0130" decimal(32,2) DEFAULT NULL,
                                             "t0145" decimal(32,2) DEFAULT NULL,
                                             "t0200" decimal(32,2) DEFAULT NULL,
                                             "t0215" decimal(32,2) DEFAULT NULL,
                                             "t0230" decimal(32,2) DEFAULT NULL,
                                             "t0245" decimal(32,2) DEFAULT NULL,
                                             "t0300" decimal(32,2) DEFAULT NULL,
                                             "t0315" decimal(32,2) DEFAULT NULL,
                                             "t0330" decimal(32,2) DEFAULT NULL,
                                             "t0345" decimal(32,2) DEFAULT NULL,
                                             "t0400" decimal(32,2) DEFAULT NULL,
                                             "t0415" decimal(32,2) DEFAULT NULL,
                                             "t0430" decimal(32,2) DEFAULT NULL,
                                             "t0445" decimal(32,2) DEFAULT NULL,
                                             "t0500" decimal(32,2) DEFAULT NULL,
                                             "t0515" decimal(32,2) DEFAULT NULL,
                                             "t0530" decimal(32,2) DEFAULT NULL,
                                             "t0545" decimal(32,2) DEFAULT NULL,
                                             "t0600" decimal(32,2) DEFAULT NULL,
                                             "t0615" decimal(32,2) DEFAULT NULL,
                                             "t0630" decimal(32,2) DEFAULT NULL,
                                             "t0645" decimal(32,2) DEFAULT NULL,
                                             "t0700" decimal(32,2) DEFAULT NULL,
                                             "t0715" decimal(32,2) DEFAULT NULL,
                                             "t0730" decimal(32,2) DEFAULT NULL,
                                             "t0745" decimal(32,2) DEFAULT NULL,
                                             "t0800" decimal(32,2) DEFAULT NULL,
                                             "t0815" decimal(32,2) DEFAULT NULL,
                                             "t0830" decimal(32,2) DEFAULT NULL,
                                             "t0845" decimal(32,2) DEFAULT NULL,
                                             "t0900" decimal(32,2) DEFAULT NULL,
                                             "t0915" decimal(32,2) DEFAULT NULL,
                                             "t0930" decimal(32,2) DEFAULT NULL,
                                             "t0945" decimal(32,2) DEFAULT NULL,
                                             "t1000" decimal(32,2) DEFAULT NULL,
                                             "t1015" decimal(32,2) DEFAULT NULL,
                                             "t1030" decimal(32,2) DEFAULT NULL,
                                             "t1045" decimal(32,2) DEFAULT NULL,
                                             "t1100" decimal(32,2) DEFAULT NULL,
                                             "t1115" decimal(32,2) DEFAULT NULL,
                                             "t1130" decimal(32,2) DEFAULT NULL,
                                             "t1145" decimal(32,2) DEFAULT NULL,
                                             "t1200" decimal(32,2) DEFAULT NULL,
                                             "t1215" decimal(32,2) DEFAULT NULL,
                                             "t1230" decimal(32,2) DEFAULT NULL,
                                             "t1245" decimal(32,2) DEFAULT NULL,
                                             "t1300" decimal(32,2) DEFAULT NULL,
                                             "t1315" decimal(32,2) DEFAULT NULL,
                                             "t1330" decimal(32,2) DEFAULT NULL,
                                             "t1345" decimal(32,2) DEFAULT NULL,
                                             "t1400" decimal(32,2) DEFAULT NULL,
                                             "t1415" decimal(32,2) DEFAULT NULL,
                                             "t1430" decimal(32,2) DEFAULT NULL,
                                             "t1445" decimal(32,2) DEFAULT NULL,
                                             "t1500" decimal(32,2) DEFAULT NULL,
                                             "t1515" decimal(32,2) DEFAULT NULL,
                                             "t1530" decimal(32,2) DEFAULT NULL,
                                             "t1545" decimal(32,2) DEFAULT NULL,
                                             "t1600" decimal(32,2) DEFAULT NULL,
                                             "t1615" decimal(32,2) DEFAULT NULL,
                                             "t1630" decimal(32,2) DEFAULT NULL,
                                             "t1645" decimal(32,2) DEFAULT NULL,
                                             "t1700" decimal(32,2) DEFAULT NULL,
                                             "t1715" decimal(32,2) DEFAULT NULL,
                                             "t1730" decimal(32,2) DEFAULT NULL,
                                             "t1745" decimal(32,2) DEFAULT NULL,
                                             "t1800" decimal(32,2) DEFAULT NULL,
                                             "t1815" decimal(32,2) DEFAULT NULL,
                                             "t1830" decimal(32,2) DEFAULT NULL,
                                             "t1845" decimal(32,2) DEFAULT NULL,
                                             "t1900" decimal(32,2) DEFAULT NULL,
                                             "t1915" decimal(32,2) DEFAULT NULL,
                                             "t1930" decimal(32,2) DEFAULT NULL,
                                             "t1945" decimal(32,2) DEFAULT NULL,
                                             "t2000" decimal(32,2) DEFAULT NULL,
                                             "t2015" decimal(32,2) DEFAULT NULL,
                                             "t2030" decimal(32,2) DEFAULT NULL,
                                             "t2045" decimal(32,2) DEFAULT NULL,
                                             "t2100" decimal(32,2) DEFAULT NULL,
                                             "t2115" decimal(32,2) DEFAULT NULL,
                                             "t2130" decimal(32,2) DEFAULT NULL,
                                             "t2145" decimal(32,2) DEFAULT NULL,
                                             "t2200" decimal(32,2) DEFAULT NULL,
                                             "t2215" decimal(32,2) DEFAULT NULL,
                                             "t2230" decimal(32,2) DEFAULT NULL,
                                             "t2245" decimal(32,2) DEFAULT NULL,
                                             "t2300" decimal(32,2) DEFAULT NULL,
                                             "t2315" decimal(32,2) DEFAULT NULL,
                                             "t2330" decimal(32,2) DEFAULT NULL,
                                             "t2345" decimal(32,2) DEFAULT NULL,
                                             "t2400" decimal(32,2) DEFAULT NULL,
                                             "createtime" datetime DEFAULT CURRENT_TIMESTAMP,
                                             "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
                                             PRIMARY KEY ("id") USING BTREE,
                                             UNIQUE KEY "weather_city_his_unique" ("date","city_id","type") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='实际气象数据缺失监控表';


--预测气象数据缺失监控表
CREATE TABLE "weather_fc_monitor_stats" (
                                            "id" varchar(32) NOT NULL COMMENT '96点气象表ID',
                                            "date" date NOT NULL COMMENT '日期',
                                            "city_id" varchar(32) NOT NULL COMMENT '潮州	1\r\n东莞	2\r\n佛山	3\r\n广州	4\r\n河源	5\r\n惠州	6\r\n江门	7\r\n揭阳	8\r\n茂名	9\r\n梅州	10\r\n清远	11\r\n汕头	12\r\n汕尾	13\r\n韶关	14\r\n深圳	15\r\n阳江	16\r\n云浮	17\r\n湛江	18\r\n肇庆	19\r\n中山	20\r\n珠海	21\r\n',
                                            "type" tinyint(2) DEFAULT NULL COMMENT '气象类型(1：湿度，2：温度，3：降雨量，4：风速)',
                                            "t0000" decimal(32,2) DEFAULT NULL,
                                            "t0015" decimal(32,2) DEFAULT NULL,
                                            "t0030" decimal(32,2) DEFAULT NULL,
                                            "t0045" decimal(32,2) DEFAULT NULL,
                                            "t0100" decimal(32,2) DEFAULT NULL,
                                            "t0115" decimal(32,2) DEFAULT NULL,
                                            "t0130" decimal(32,2) DEFAULT NULL,
                                            "t0145" decimal(32,2) DEFAULT NULL,
                                            "t0200" decimal(32,2) DEFAULT NULL,
                                            "t0215" decimal(32,2) DEFAULT NULL,
                                            "t0230" decimal(32,2) DEFAULT NULL,
                                            "t0245" decimal(32,2) DEFAULT NULL,
                                            "t0300" decimal(32,2) DEFAULT NULL,
                                            "t0315" decimal(32,2) DEFAULT NULL,
                                            "t0330" decimal(32,2) DEFAULT NULL,
                                            "t0345" decimal(32,2) DEFAULT NULL,
                                            "t0400" decimal(32,2) DEFAULT NULL,
                                            "t0415" decimal(32,2) DEFAULT NULL,
                                            "t0430" decimal(32,2) DEFAULT NULL,
                                            "t0445" decimal(32,2) DEFAULT NULL,
                                            "t0500" decimal(32,2) DEFAULT NULL,
                                            "t0515" decimal(32,2) DEFAULT NULL,
                                            "t0530" decimal(32,2) DEFAULT NULL,
                                            "t0545" decimal(32,2) DEFAULT NULL,
                                            "t0600" decimal(32,2) DEFAULT NULL,
                                            "t0615" decimal(32,2) DEFAULT NULL,
                                            "t0630" decimal(32,2) DEFAULT NULL,
                                            "t0645" decimal(32,2) DEFAULT NULL,
                                            "t0700" decimal(32,2) DEFAULT NULL,
                                            "t0715" decimal(32,2) DEFAULT NULL,
                                            "t0730" decimal(32,2) DEFAULT NULL,
                                            "t0745" decimal(32,2) DEFAULT NULL,
                                            "t0800" decimal(32,2) DEFAULT NULL,
                                            "t0815" decimal(32,2) DEFAULT NULL,
                                            "t0830" decimal(32,2) DEFAULT NULL,
                                            "t0845" decimal(32,2) DEFAULT NULL,
                                            "t0900" decimal(32,2) DEFAULT NULL,
                                            "t0915" decimal(32,2) DEFAULT NULL,
                                            "t0930" decimal(32,2) DEFAULT NULL,
                                            "t0945" decimal(32,2) DEFAULT NULL,
                                            "t1000" decimal(32,2) DEFAULT NULL,
                                            "t1015" decimal(32,2) DEFAULT NULL,
                                            "t1030" decimal(32,2) DEFAULT NULL,
                                            "t1045" decimal(32,2) DEFAULT NULL,
                                            "t1100" decimal(32,2) DEFAULT NULL,
                                            "t1115" decimal(32,2) DEFAULT NULL,
                                            "t1130" decimal(32,2) DEFAULT NULL,
                                            "t1145" decimal(32,2) DEFAULT NULL,
                                            "t1200" decimal(32,2) DEFAULT NULL,
                                            "t1215" decimal(32,2) DEFAULT NULL,
                                            "t1230" decimal(32,2) DEFAULT NULL,
                                            "t1245" decimal(32,2) DEFAULT NULL,
                                            "t1300" decimal(32,2) DEFAULT NULL,
                                            "t1315" decimal(32,2) DEFAULT NULL,
                                            "t1330" decimal(32,2) DEFAULT NULL,
                                            "t1345" decimal(32,2) DEFAULT NULL,
                                            "t1400" decimal(32,2) DEFAULT NULL,
                                            "t1415" decimal(32,2) DEFAULT NULL,
                                            "t1430" decimal(32,2) DEFAULT NULL,
                                            "t1445" decimal(32,2) DEFAULT NULL,
                                            "t1500" decimal(32,2) DEFAULT NULL,
                                            "t1515" decimal(32,2) DEFAULT NULL,
                                            "t1530" decimal(32,2) DEFAULT NULL,
                                            "t1545" decimal(32,2) DEFAULT NULL,
                                            "t1600" decimal(32,2) DEFAULT NULL,
                                            "t1615" decimal(32,2) DEFAULT NULL,
                                            "t1630" decimal(32,2) DEFAULT NULL,
                                            "t1645" decimal(32,2) DEFAULT NULL,
                                            "t1700" decimal(32,2) DEFAULT NULL,
                                            "t1715" decimal(32,2) DEFAULT NULL,
                                            "t1730" decimal(32,2) DEFAULT NULL,
                                            "t1745" decimal(32,2) DEFAULT NULL,
                                            "t1800" decimal(32,2) DEFAULT NULL,
                                            "t1815" decimal(32,2) DEFAULT NULL,
                                            "t1830" decimal(32,2) DEFAULT NULL,
                                            "t1845" decimal(32,2) DEFAULT NULL,
                                            "t1900" decimal(32,2) DEFAULT NULL,
                                            "t1915" decimal(32,2) DEFAULT NULL,
                                            "t1930" decimal(32,2) DEFAULT NULL,
                                            "t1945" decimal(32,2) DEFAULT NULL,
                                            "t2000" decimal(32,2) DEFAULT NULL,
                                            "t2015" decimal(32,2) DEFAULT NULL,
                                            "t2030" decimal(32,2) DEFAULT NULL,
                                            "t2045" decimal(32,2) DEFAULT NULL,
                                            "t2100" decimal(32,2) DEFAULT NULL,
                                            "t2115" decimal(32,2) DEFAULT NULL,
                                            "t2130" decimal(32,2) DEFAULT NULL,
                                            "t2145" decimal(32,2) DEFAULT NULL,
                                            "t2200" decimal(32,2) DEFAULT NULL,
                                            "t2215" decimal(32,2) DEFAULT NULL,
                                            "t2230" decimal(32,2) DEFAULT NULL,
                                            "t2245" decimal(32,2) DEFAULT NULL,
                                            "t2300" decimal(32,2) DEFAULT NULL,
                                            "t2315" decimal(32,2) DEFAULT NULL,
                                            "t2330" decimal(32,2) DEFAULT NULL,
                                            "t2345" decimal(32,2) DEFAULT NULL,
                                            "t2400" decimal(32,2) DEFAULT NULL,
                                            "createtime" datetime DEFAULT CURRENT_TIMESTAMP,
                                            "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
                                            PRIMARY KEY ("id") USING BTREE,
                                            UNIQUE KEY "weather_city_his_unique" ("date","city_id","type") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='预测气象数据缺失监控表';



-- yangjin  2022-11-16   添加实施页面预测后评估页面
INSERT INTO  `tsie_menu` (`id`, `name`, `type`, `menupath`, `parentId`, `description`, `targetType`, `rel`, `sort`) VALUES ('4028d0816cb8644d016cb86f05bb0122', '预测后评估', 1, 'SetLoadForecastEvaluation', '4028d0816cb8644d016cb86f05b20132', '预测后评估', 'navTab', '预测后评估', 9999);

-- 2023-02-09气象站点相关页面菜单
INSERT INTO `tsie_menu` (id,name,`type`,menupath,parentId,description,targetType,rel,sort) VALUES
('4028d0816cb8644d016cb86f05a00125','新能源站点气象',1,'NewEnergyMeteorology','21','新能源站点气象','navTab','新能源站点气象',9999),
('4028d0816cb8644d016cb86f05a00126','站点气象数据缺失分析',1,'WeatherMissing','21','站点气象数据缺失分析','navTab','站点气象数据缺失分析',9999);



-- load_guangdong_dev_v3.load_city_fc_basic definition

CREATE TABLE "load_area_fc_basic" (
                                      "id" varchar(32) NOT NULL COMMENT '96点负荷表id',
                                      "date" date NOT NULL COMMENT '日期',
                                      "area_id" varchar(32) NOT NULL COMMENT '区域id',
                                      "caliber_id" varchar(32) NOT NULL COMMENT '口径id',
                                      "algorithm_id" varchar(32) NOT NULL COMMENT '预测算法id',
                                      "t0000" decimal(32,4) DEFAULT NULL,
                                      "t0015" decimal(32,4) DEFAULT NULL,
                                      "t0030" decimal(32,4) DEFAULT NULL,
                                      "t0045" decimal(32,4) DEFAULT NULL,
                                      "t0100" decimal(32,4) DEFAULT NULL,
                                      "t0115" decimal(32,4) DEFAULT NULL,
                                      "t0130" decimal(32,4) DEFAULT NULL,
                                      "t0145" decimal(32,4) DEFAULT NULL,
                                      "t0200" decimal(32,4) DEFAULT NULL,
                                      "t0215" decimal(32,4) DEFAULT NULL,
                                      "t0230" decimal(32,4) DEFAULT NULL,
                                      "t0245" decimal(32,4) DEFAULT NULL,
                                      "t0300" decimal(32,4) DEFAULT NULL,
                                      "t0315" decimal(32,4) DEFAULT NULL,
                                      "t0330" decimal(32,4) DEFAULT NULL,
                                      "t0345" decimal(32,4) DEFAULT NULL,
                                      "t0400" decimal(32,4) DEFAULT NULL,
                                      "t0415" decimal(32,4) DEFAULT NULL,
                                      "t0430" decimal(32,4) DEFAULT NULL,
                                      "t0445" decimal(32,4) DEFAULT NULL,
                                      "t0500" decimal(32,4) DEFAULT NULL,
                                      "t0515" decimal(32,4) DEFAULT NULL,
                                      "t0530" decimal(32,4) DEFAULT NULL,
                                      "t0545" decimal(32,4) DEFAULT NULL,
                                      "t0600" decimal(32,4) DEFAULT NULL,
                                      "t0615" decimal(32,4) DEFAULT NULL,
                                      "t0630" decimal(32,4) DEFAULT NULL,
                                      "t0645" decimal(32,4) DEFAULT NULL,
                                      "t0700" decimal(32,4) DEFAULT NULL,
                                      "t0715" decimal(32,4) DEFAULT NULL,
                                      "t0730" decimal(32,4) DEFAULT NULL,
                                      "t0745" decimal(32,4) DEFAULT NULL,
                                      "t0800" decimal(32,4) DEFAULT NULL,
                                      "t0815" decimal(32,4) DEFAULT NULL,
                                      "t0830" decimal(32,4) DEFAULT NULL,
                                      "t0845" decimal(32,4) DEFAULT NULL,
                                      "t0900" decimal(32,4) DEFAULT NULL,
                                      "t0915" decimal(32,4) DEFAULT NULL,
                                      "t0930" decimal(32,4) DEFAULT NULL,
                                      "t0945" decimal(32,4) DEFAULT NULL,
                                      "t1000" decimal(32,4) DEFAULT NULL,
                                      "t1015" decimal(32,4) DEFAULT NULL,
                                      "t1030" decimal(32,4) DEFAULT NULL,
                                      "t1045" decimal(32,4) DEFAULT NULL,
                                      "t1100" decimal(32,4) DEFAULT NULL,
                                      "t1115" decimal(32,4) DEFAULT NULL,
                                      "t1130" decimal(32,4) DEFAULT NULL,
                                      "t1145" decimal(32,4) DEFAULT NULL,
                                      "t1200" decimal(32,4) DEFAULT NULL,
                                      "t1215" decimal(32,4) DEFAULT NULL,
                                      "t1230" decimal(32,4) DEFAULT NULL,
                                      "t1245" decimal(32,4) DEFAULT NULL,
                                      "t1300" decimal(32,4) DEFAULT NULL,
                                      "t1315" decimal(32,4) DEFAULT NULL,
                                      "t1330" decimal(32,4) DEFAULT NULL,
                                      "t1345" decimal(32,4) DEFAULT NULL,
                                      "t1400" decimal(32,4) DEFAULT NULL,
                                      "t1415" decimal(32,4) DEFAULT NULL,
                                      "t1430" decimal(32,4) DEFAULT NULL,
                                      "t1445" decimal(32,4) DEFAULT NULL,
                                      "t1500" decimal(32,4) DEFAULT NULL,
                                      "t1515" decimal(32,4) DEFAULT NULL,
                                      "t1530" decimal(32,4) DEFAULT NULL,
                                      "t1545" decimal(32,4) DEFAULT NULL,
                                      "t1600" decimal(32,4) DEFAULT NULL,
                                      "t1615" decimal(32,4) DEFAULT NULL,
                                      "t1630" decimal(32,4) DEFAULT NULL,
                                      "t1645" decimal(32,4) DEFAULT NULL,
                                      "t1700" decimal(32,4) DEFAULT NULL,
                                      "t1715" decimal(32,4) DEFAULT NULL,
                                      "t1730" decimal(32,4) DEFAULT NULL,
                                      "t1745" decimal(32,4) DEFAULT NULL,
                                      "t1800" decimal(32,4) DEFAULT NULL,
                                      "t1815" decimal(32,4) DEFAULT NULL,
                                      "t1830" decimal(32,4) DEFAULT NULL,
                                      "t1845" decimal(32,4) DEFAULT NULL,
                                      "t1900" decimal(32,4) DEFAULT NULL,
                                      "t1915" decimal(32,4) DEFAULT NULL,
                                      "t1930" decimal(32,4) DEFAULT NULL,
                                      "t1945" decimal(32,4) DEFAULT NULL,
                                      "t2000" decimal(32,4) DEFAULT NULL,
                                      "t2015" decimal(32,4) DEFAULT NULL,
                                      "t2030" decimal(32,4) DEFAULT NULL,
                                      "t2045" decimal(32,4) DEFAULT NULL,
                                      "t2100" decimal(32,4) DEFAULT NULL,
                                      "t2115" decimal(32,4) DEFAULT NULL,
                                      "t2130" decimal(32,4) DEFAULT NULL,
                                      "t2145" decimal(32,4) DEFAULT NULL,
                                      "t2200" decimal(32,4) DEFAULT NULL,
                                      "t2215" decimal(32,4) DEFAULT NULL,
                                      "t2230" decimal(32,4) DEFAULT NULL,
                                      "t2245" decimal(32,4) DEFAULT NULL,
                                      "t2300" decimal(32,4) DEFAULT NULL,
                                      "t2315" decimal(32,4) DEFAULT NULL,
                                      "t2330" decimal(32,4) DEFAULT NULL,
                                      "t2345" decimal(32,4) DEFAULT NULL,
                                      "t2400" decimal(32,4) DEFAULT NULL,
                                      "createtime" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                      "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
                                      PRIMARY KEY ("id") USING BTREE,
                                      UNIQUE KEY "load_area_fc_unique" ("date","area_id","algorithm_id","caliber_id") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='区域预测负荷表';


-- load_guangdong_dev_v3.weather_city_fc_basic definition

CREATE TABLE "weather_area_fc_basic" (
                                         "id" varchar(32) NOT NULL COMMENT '96点气象表ID',
                                         "date" date NOT NULL COMMENT '日期',
                                         "area_id" varchar(32) NOT NULL ,
                                         "source" varchar(32) not null COMMENT '气象源 广东台 gdt    中央台 zyt  综合台 zht  玖天 ',
                                         "type" tinyint(2) DEFAULT NULL COMMENT '气象类型(1：湿度，2：温度，3：降雨量，4：风速)',
                                         "t0000" decimal(32,2) DEFAULT NULL,
                                         "t0015" decimal(32,2) DEFAULT NULL,
                                         "t0030" decimal(32,2) DEFAULT NULL,
                                         "t0045" decimal(32,2) DEFAULT NULL,
                                         "t0100" decimal(32,2) DEFAULT NULL,
                                         "t0115" decimal(32,2) DEFAULT NULL,
                                         "t0130" decimal(32,2) DEFAULT NULL,
                                         "t0145" decimal(32,2) DEFAULT NULL,
                                         "t0200" decimal(32,2) DEFAULT NULL,
                                         "t0215" decimal(32,2) DEFAULT NULL,
                                         "t0230" decimal(32,2) DEFAULT NULL,
                                         "t0245" decimal(32,2) DEFAULT NULL,
                                         "t0300" decimal(32,2) DEFAULT NULL,
                                         "t0315" decimal(32,2) DEFAULT NULL,
                                         "t0330" decimal(32,2) DEFAULT NULL,
                                         "t0345" decimal(32,2) DEFAULT NULL,
                                         "t0400" decimal(32,2) DEFAULT NULL,
                                         "t0415" decimal(32,2) DEFAULT NULL,
                                         "t0430" decimal(32,2) DEFAULT NULL,
                                         "t0445" decimal(32,2) DEFAULT NULL,
                                         "t0500" decimal(32,2) DEFAULT NULL,
                                         "t0515" decimal(32,2) DEFAULT NULL,
                                         "t0530" decimal(32,2) DEFAULT NULL,
                                         "t0545" decimal(32,2) DEFAULT NULL,
                                         "t0600" decimal(32,2) DEFAULT NULL,
                                         "t0615" decimal(32,2) DEFAULT NULL,
                                         "t0630" decimal(32,2) DEFAULT NULL,
                                         "t0645" decimal(32,2) DEFAULT NULL,
                                         "t0700" decimal(32,2) DEFAULT NULL,
                                         "t0715" decimal(32,2) DEFAULT NULL,
                                         "t0730" decimal(32,2) DEFAULT NULL,
                                         "t0745" decimal(32,2) DEFAULT NULL,
                                         "t0800" decimal(32,2) DEFAULT NULL,
                                         "t0815" decimal(32,2) DEFAULT NULL,
                                         "t0830" decimal(32,2) DEFAULT NULL,
                                         "t0845" decimal(32,2) DEFAULT NULL,
                                         "t0900" decimal(32,2) DEFAULT NULL,
                                         "t0915" decimal(32,2) DEFAULT NULL,
                                         "t0930" decimal(32,2) DEFAULT NULL,
                                         "t0945" decimal(32,2) DEFAULT NULL,
                                         "t1000" decimal(32,2) DEFAULT NULL,
                                         "t1015" decimal(32,2) DEFAULT NULL,
                                         "t1030" decimal(32,2) DEFAULT NULL,
                                         "t1045" decimal(32,2) DEFAULT NULL,
                                         "t1100" decimal(32,2) DEFAULT NULL,
                                         "t1115" decimal(32,2) DEFAULT NULL,
                                         "t1130" decimal(32,2) DEFAULT NULL,
                                         "t1145" decimal(32,2) DEFAULT NULL,
                                         "t1200" decimal(32,2) DEFAULT NULL,
                                         "t1215" decimal(32,2) DEFAULT NULL,
                                         "t1230" decimal(32,2) DEFAULT NULL,
                                         "t1245" decimal(32,2) DEFAULT NULL,
                                         "t1300" decimal(32,2) DEFAULT NULL,
                                         "t1315" decimal(32,2) DEFAULT NULL,
                                         "t1330" decimal(32,2) DEFAULT NULL,
                                         "t1345" decimal(32,2) DEFAULT NULL,
                                         "t1400" decimal(32,2) DEFAULT NULL,
                                         "t1415" decimal(32,2) DEFAULT NULL,
                                         "t1430" decimal(32,2) DEFAULT NULL,
                                         "t1445" decimal(32,2) DEFAULT NULL,
                                         "t1500" decimal(32,2) DEFAULT NULL,
                                         "t1515" decimal(32,2) DEFAULT NULL,
                                         "t1530" decimal(32,2) DEFAULT NULL,
                                         "t1545" decimal(32,2) DEFAULT NULL,
                                         "t1600" decimal(32,2) DEFAULT NULL,
                                         "t1615" decimal(32,2) DEFAULT NULL,
                                         "t1630" decimal(32,2) DEFAULT NULL,
                                         "t1645" decimal(32,2) DEFAULT NULL,
                                         "t1700" decimal(32,2) DEFAULT NULL,
                                         "t1715" decimal(32,2) DEFAULT NULL,
                                         "t1730" decimal(32,2) DEFAULT NULL,
                                         "t1745" decimal(32,2) DEFAULT NULL,
                                         "t1800" decimal(32,2) DEFAULT NULL,
                                         "t1815" decimal(32,2) DEFAULT NULL,
                                         "t1830" decimal(32,2) DEFAULT NULL,
                                         "t1845" decimal(32,2) DEFAULT NULL,
                                         "t1900" decimal(32,2) DEFAULT NULL,
                                         "t1915" decimal(32,2) DEFAULT NULL,
                                         "t1930" decimal(32,2) DEFAULT NULL,
                                         "t1945" decimal(32,2) DEFAULT NULL,
                                         "t2000" decimal(32,2) DEFAULT NULL,
                                         "t2015" decimal(32,2) DEFAULT NULL,
                                         "t2030" decimal(32,2) DEFAULT NULL,
                                         "t2045" decimal(32,2) DEFAULT NULL,
                                         "t2100" decimal(32,2) DEFAULT NULL,
                                         "t2115" decimal(32,2) DEFAULT NULL,
                                         "t2130" decimal(32,2) DEFAULT NULL,
                                         "t2145" decimal(32,2) DEFAULT NULL,
                                         "t2200" decimal(32,2) DEFAULT NULL,
                                         "t2215" decimal(32,2) DEFAULT NULL,
                                         "t2230" decimal(32,2) DEFAULT NULL,
                                         "t2245" decimal(32,2) DEFAULT NULL,
                                         "t2300" decimal(32,2) DEFAULT NULL,
                                         "t2315" decimal(32,2) DEFAULT NULL,
                                         "t2330" decimal(32,2) DEFAULT NULL,
                                         "t2345" decimal(32,2) DEFAULT NULL,
                                         "t2400" decimal(32,2) DEFAULT NULL,
                                         "createtime" datetime DEFAULT CURRENT_TIMESTAMP,
                                         "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
                                         PRIMARY KEY ("id") USING BTREE,
                                         UNIQUE KEY "weather_area_fc_unique" ("date","area_id","type","source") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='区域预测表96点';



CREATE TABLE "weather_area_his_basic" (
                                          "id" varchar(32) NOT NULL COMMENT '96点气象表ID',
                                          "date" date NOT NULL COMMENT '日期',
                                          "area_id" varchar(32) NOT NULL ,
                                          "type" tinyint(2) DEFAULT NULL COMMENT '气象类型(1：湿度，2：温度，3：降雨量，4：风速)',
                                          "t0000" decimal(32,2) DEFAULT NULL,
                                          "t0015" decimal(32,2) DEFAULT NULL,
                                          "t0030" decimal(32,2) DEFAULT NULL,
                                          "t0045" decimal(32,2) DEFAULT NULL,
                                          "t0100" decimal(32,2) DEFAULT NULL,
                                          "t0115" decimal(32,2) DEFAULT NULL,
                                          "t0130" decimal(32,2) DEFAULT NULL,
                                          "t0145" decimal(32,2) DEFAULT NULL,
                                          "t0200" decimal(32,2) DEFAULT NULL,
                                          "t0215" decimal(32,2) DEFAULT NULL,
                                          "t0230" decimal(32,2) DEFAULT NULL,
                                          "t0245" decimal(32,2) DEFAULT NULL,
                                          "t0300" decimal(32,2) DEFAULT NULL,
                                          "t0315" decimal(32,2) DEFAULT NULL,
                                          "t0330" decimal(32,2) DEFAULT NULL,
                                          "t0345" decimal(32,2) DEFAULT NULL,
                                          "t0400" decimal(32,2) DEFAULT NULL,
                                          "t0415" decimal(32,2) DEFAULT NULL,
                                          "t0430" decimal(32,2) DEFAULT NULL,
                                          "t0445" decimal(32,2) DEFAULT NULL,
                                          "t0500" decimal(32,2) DEFAULT NULL,
                                          "t0515" decimal(32,2) DEFAULT NULL,
                                          "t0530" decimal(32,2) DEFAULT NULL,
                                          "t0545" decimal(32,2) DEFAULT NULL,
                                          "t0600" decimal(32,2) DEFAULT NULL,
                                          "t0615" decimal(32,2) DEFAULT NULL,
                                          "t0630" decimal(32,2) DEFAULT NULL,
                                          "t0645" decimal(32,2) DEFAULT NULL,
                                          "t0700" decimal(32,2) DEFAULT NULL,
                                          "t0715" decimal(32,2) DEFAULT NULL,
                                          "t0730" decimal(32,2) DEFAULT NULL,
                                          "t0745" decimal(32,2) DEFAULT NULL,
                                          "t0800" decimal(32,2) DEFAULT NULL,
                                          "t0815" decimal(32,2) DEFAULT NULL,
                                          "t0830" decimal(32,2) DEFAULT NULL,
                                          "t0845" decimal(32,2) DEFAULT NULL,
                                          "t0900" decimal(32,2) DEFAULT NULL,
                                          "t0915" decimal(32,2) DEFAULT NULL,
                                          "t0930" decimal(32,2) DEFAULT NULL,
                                          "t0945" decimal(32,2) DEFAULT NULL,
                                          "t1000" decimal(32,2) DEFAULT NULL,
                                          "t1015" decimal(32,2) DEFAULT NULL,
                                          "t1030" decimal(32,2) DEFAULT NULL,
                                          "t1045" decimal(32,2) DEFAULT NULL,
                                          "t1100" decimal(32,2) DEFAULT NULL,
                                          "t1115" decimal(32,2) DEFAULT NULL,
                                          "t1130" decimal(32,2) DEFAULT NULL,
                                          "t1145" decimal(32,2) DEFAULT NULL,
                                          "t1200" decimal(32,2) DEFAULT NULL,
                                          "t1215" decimal(32,2) DEFAULT NULL,
                                          "t1230" decimal(32,2) DEFAULT NULL,
                                          "t1245" decimal(32,2) DEFAULT NULL,
                                          "t1300" decimal(32,2) DEFAULT NULL,
                                          "t1315" decimal(32,2) DEFAULT NULL,
                                          "t1330" decimal(32,2) DEFAULT NULL,
                                          "t1345" decimal(32,2) DEFAULT NULL,
                                          "t1400" decimal(32,2) DEFAULT NULL,
                                          "t1415" decimal(32,2) DEFAULT NULL,
                                          "t1430" decimal(32,2) DEFAULT NULL,
                                          "t1445" decimal(32,2) DEFAULT NULL,
                                          "t1500" decimal(32,2) DEFAULT NULL,
                                          "t1515" decimal(32,2) DEFAULT NULL,
                                          "t1530" decimal(32,2) DEFAULT NULL,
                                          "t1545" decimal(32,2) DEFAULT NULL,
                                          "t1600" decimal(32,2) DEFAULT NULL,
                                          "t1615" decimal(32,2) DEFAULT NULL,
                                          "t1630" decimal(32,2) DEFAULT NULL,
                                          "t1645" decimal(32,2) DEFAULT NULL,
                                          "t1700" decimal(32,2) DEFAULT NULL,
                                          "t1715" decimal(32,2) DEFAULT NULL,
                                          "t1730" decimal(32,2) DEFAULT NULL,
                                          "t1745" decimal(32,2) DEFAULT NULL,
                                          "t1800" decimal(32,2) DEFAULT NULL,
                                          "t1815" decimal(32,2) DEFAULT NULL,
                                          "t1830" decimal(32,2) DEFAULT NULL,
                                          "t1845" decimal(32,2) DEFAULT NULL,
                                          "t1900" decimal(32,2) DEFAULT NULL,
                                          "t1915" decimal(32,2) DEFAULT NULL,
                                          "t1930" decimal(32,2) DEFAULT NULL,
                                          "t1945" decimal(32,2) DEFAULT NULL,
                                          "t2000" decimal(32,2) DEFAULT NULL,
                                          "t2015" decimal(32,2) DEFAULT NULL,
                                          "t2030" decimal(32,2) DEFAULT NULL,
                                          "t2045" decimal(32,2) DEFAULT NULL,
                                          "t2100" decimal(32,2) DEFAULT NULL,
                                          "t2115" decimal(32,2) DEFAULT NULL,
                                          "t2130" decimal(32,2) DEFAULT NULL,
                                          "t2145" decimal(32,2) DEFAULT NULL,
                                          "t2200" decimal(32,2) DEFAULT NULL,
                                          "t2215" decimal(32,2) DEFAULT NULL,
                                          "t2230" decimal(32,2) DEFAULT NULL,
                                          "t2245" decimal(32,2) DEFAULT NULL,
                                          "t2300" decimal(32,2) DEFAULT NULL,
                                          "t2315" decimal(32,2) DEFAULT NULL,
                                          "t2330" decimal(32,2) DEFAULT NULL,
                                          "t2345" decimal(32,2) DEFAULT NULL,
                                          "t2400" decimal(32,2) DEFAULT NULL,
                                          "createtime" datetime DEFAULT CURRENT_TIMESTAMP,
                                          "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
                                          PRIMARY KEY ("id") USING BTREE,
                                          UNIQUE KEY "weather_area_his_unique" ("date","area_id","type") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='区气象实际预测表96点';
-- load_guangdong_dev_v3.weather_feature_city_day_fc_service definition

CREATE TABLE "weather_feature_area_day_fc_service" (
                                                       "id" varchar(32) NOT NULL COMMENT '温度业务表id',
                                                       "date" date NOT NULL COMMENT '日期',
                                                       "area_id" varchar(32) NOT NULL COMMENT '城市id',
                                                       "source" varchar(32) not null COMMENT '气象源 广东台 gdt    中央台 zyt  综合台 zht  玖天 ',
                                                       "highest_temperature" decimal(10,2) DEFAULT NULL COMMENT '最高温度',
                                                       "lowest_temperature" decimal(32,2) DEFAULT NULL COMMENT '最低温度',
                                                       "ave_temperature" decimal(32,2) DEFAULT NULL COMMENT '平均温度',
                                                       "highest_humidity" decimal(32,2) DEFAULT NULL COMMENT '最高相对湿度',
                                                       "lowest_humidity" decimal(32,2) DEFAULT NULL COMMENT '最低相对湿度',
                                                       "ave_humidity" decimal(32,2) DEFAULT NULL COMMENT '平均相对湿度',
                                                       "max_winds" decimal(32,2) DEFAULT NULL COMMENT '最大风速',
                                                       "min_winds" decimal(32,2) DEFAULT NULL COMMENT '最小风速',
                                                       "ave_winds" decimal(32,2) DEFAULT NULL COMMENT '平均风速',
                                                       "highest_comfort" decimal(32,2) DEFAULT NULL COMMENT '最大人体舒适度',
                                                       "lowest_comfort" decimal(32,2) DEFAULT NULL COMMENT '最低人体舒适度',
                                                       "ave_comfort" decimal(32,2) DEFAULT NULL COMMENT '平均人体舒适度',
                                                       "highest_effective_temperature" decimal(32,2) DEFAULT NULL COMMENT '最高实感温度',
                                                       "lowest_effective_temperature" decimal(32,2) DEFAULT NULL COMMENT '最低实感温度',
                                                       "ave_effective_temperature" decimal(32,2) DEFAULT NULL COMMENT '平均实感温度',
                                                       "max_coldness" decimal(32,2) DEFAULT NULL COMMENT '最大寒冷指数',
                                                       "min_coldness" decimal(32,2) DEFAULT NULL COMMENT '最小寒冷指数',
                                                       "ave_coldness" decimal(32,2) DEFAULT NULL COMMENT '平均寒冷指数',
                                                       "max_temperature_humidity" decimal(32,2) DEFAULT NULL COMMENT '最大温湿指数',
                                                       "min_temperature_humidity" decimal(32,2) DEFAULT NULL COMMENT '最小温湿指数',
                                                       "ave_temperature_humidity" decimal(32,2) DEFAULT NULL COMMENT '平均温湿指数',
                                                       "rainfall" decimal(32,2) DEFAULT NULL COMMENT '降雨量',
                                                       "createtime" datetime DEFAULT CURRENT_TIMESTAMP,
                                                       "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
                                                       PRIMARY KEY ("id") USING BTREE,
                                                       UNIQUE KEY "weather_feature_area_fc_unique" ("date","area_id","source") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='气象日特性表（预报）';



-- 区域历史气象特性

CREATE TABLE "weather_feature_area_day_his_service" (
                                                        "id" varchar(32) NOT NULL COMMENT '温度业务表id',
                                                        "date" date NOT NULL COMMENT '日期',
                                                        "area_id" varchar(32) NOT NULL COMMENT '城市id',
                                                        "highest_temperature" decimal(10,2) DEFAULT NULL COMMENT '最高温度',
                                                        "lowest_temperature" decimal(32,2) DEFAULT NULL COMMENT '最低温度',
                                                        "ave_temperature" decimal(32,2) DEFAULT NULL COMMENT '平均温度',
                                                        "highest_humidity" decimal(32,2) DEFAULT NULL COMMENT '最高相对湿度',
                                                        "lowest_humidity" decimal(32,2) DEFAULT NULL COMMENT '最低相对湿度',
                                                        "ave_humidity" decimal(32,2) DEFAULT NULL COMMENT '平均相对湿度',
                                                        "max_winds" decimal(32,2) DEFAULT NULL COMMENT '最大风速',
                                                        "min_winds" decimal(32,2) DEFAULT NULL COMMENT '最小风速',
                                                        "ave_winds" decimal(32,2) DEFAULT NULL COMMENT '平均风速',
                                                        "highest_comfort" decimal(32,2) DEFAULT NULL COMMENT '最大人体舒适度',
                                                        "lowest_comfort" decimal(32,2) DEFAULT NULL COMMENT '最低人体舒适度',
                                                        "ave_comfort" decimal(32,2) DEFAULT NULL COMMENT '平均人体舒适度',
                                                        "highest_effective_temperature" decimal(32,2) DEFAULT NULL COMMENT '最高实感温度',
                                                        "lowest_effective_temperature" decimal(32,2) DEFAULT NULL COMMENT '最低实感温度',
                                                        "ave_effective_temperature" decimal(32,2) DEFAULT NULL COMMENT '平均实感温度',
                                                        "max_coldness" decimal(32,2) DEFAULT NULL COMMENT '最大寒冷指数',
                                                        "min_coldness" decimal(32,2) DEFAULT NULL COMMENT '最小寒冷指数',
                                                        "ave_coldness" decimal(32,2) DEFAULT NULL COMMENT '平均寒冷指数',
                                                        "max_temperature_humidity" decimal(32,2) DEFAULT NULL COMMENT '最大温湿指数',
                                                        "min_temperature_humidity" decimal(32,2) DEFAULT NULL COMMENT '最小温湿指数',
                                                        "ave_temperature_humidity" decimal(32,2) DEFAULT NULL COMMENT '平均温湿指数',
                                                        "rainfall" decimal(32,2) DEFAULT NULL COMMENT '降雨量',
                                                        "createtime" datetime DEFAULT CURRENT_TIMESTAMP,
                                                        "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
                                                        "recently_rainfall" decimal(32,2) DEFAULT NULL COMMENT '最近3天降雨量',
                                                        PRIMARY KEY ("id") USING BTREE,
                                                        UNIQUE KEY "weather_feature_area_his_unique" ("date","area_id") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='气象日特性表';


-- 新增分区预测算法
INSERT INTO algorithm_base_init (id,algorithm_en,algorithm_cn,code,order_no,valid,join_model,`type`,province_view,city_view) VALUES
('801','SUBREGION','分区预测','801',25,1,'0',1,1,1);


-- 新增粤西粤东分区方案
INSERT INTO base_plan_info (id,name,area_ids,description,valid,fc_mode,create_time,update_time) VALUES
                                                                                                                        ('ff8080818291b4ce0182b502625d0061','粤东粤西分区','99,100','粤东粤西分区',2,NULL,NULL,NULL);

INSERT INTO base_area_info (id,name,city_ids,description,create_time,update_time) VALUES
                                                                                                            ('99','粤东','3,5,12,13,14,15,18,20,21',NULL,NULL,NULL)
                                                                                                            ('100','粤西','2,4,6,7,8,9,10,11,16,17,19,22,23',NULL,NULL,NULL);
INSERT INTO city_base_init (id, city, type, belong_id, weather_city_id, area, order_no, valid) VALUES ('99', '粤东', 2, '1', '99', null, 99, 1);
INSERT INTO city_base_init (id, city, type, belong_id, weather_city_id, area, order_no, valid) VALUES ('100', '粤西', 2, '1', '100', null, 100, 1);


create table algorithm_power_init
(
    id   varchar(32) collate latin1_swedish_ci not null
        primary key,
    name varchar(32)                           null comment '名称',
    code varchar(32) collate latin1_swedish_ci null
)
    comment '分布式光伏预测算法表' collate = utf8_general_ci;

INSERT INTO load_guangdong_dev_v3.algorithm_power_init (id, name, code) VALUES ('1', '点预测', null);
INSERT INTO load_guangdong_dev_v3.algorithm_power_init (id, name, code) VALUES ('2', '区间预测', null);


create table load_guangdong_dev_v3.base_power_info
(
    id             varchar(32) null,
    date           date        null comment '日期',
    city_id        varchar(32) null,
    low_capacity   varchar(50) null comment '低压容量',
    mid_capacity   varchar(50) null comment '中压容量',
    sum_capacity   varchar(32) null comment '集中式容量',
    total_capacity varchar(50) null comment '分布式总容量',
    low_num        varchar(32) null comment '低压电站数量',
    mid_num        varchar(32) null comment '中压电站数量',
    sum_num        varchar(32) null comment '集中式电站数量',
    total_num      varchar(32) null comment '分布式光伏电站总数量',
    createtime     timestamp   null,
    updatetime     timestamp   null
)
    comment '分布式出力信息';

create table load_guangdong_dev_v3.small_power_load_fc_basic
(
    id           varchar(32)                        not null comment 'id'
        primary key,
    date_time    date                               null,
    city_id      varchar(32)                        not null comment '城市id',
    algorithm_id varchar(32)                        not null comment '算法id',
    t0000        decimal(16, 4)                     null,
    t0015        decimal(16, 4)                     null,
    t0030        decimal(16, 4)                     null,
    t0045        decimal(16, 4)                     null,
    t0100        decimal(16, 4)                     null,
    t0115        decimal(16, 4)                     null,
    t0130        decimal(16, 4)                     null,
    t0145        decimal(16, 4)                     null,
    t0200        decimal(16, 4)                     null,
    t0215        decimal(16, 4)                     null,
    t0230        decimal(16, 4)                     null,
    t0245        decimal(16, 4)                     null,
    t0300        decimal(16, 4)                     null,
    t0315        decimal(16, 4)                     null,
    t0330        decimal(16, 4)                     null,
    t0345        decimal(16, 4)                     null,
    t0400        decimal(16, 4)                     null,
    t0415        decimal(16, 4)                     null,
    t0430        decimal(16, 4)                     null,
    t0445        decimal(16, 4)                     null,
    t0500        decimal(16, 4)                     null,
    t0515        decimal(16, 4)                     null,
    t0530        decimal(16, 4)                     null,
    t0545        decimal(16, 4)                     null,
    t0600        decimal(16, 4)                     null,
    t0615        decimal(16, 4)                     null,
    t0630        decimal(16, 4)                     null,
    t0645        decimal(16, 4)                     null,
    t0700        decimal(16, 4)                     null,
    t0715        decimal(16, 4)                     null,
    t0730        decimal(16, 4)                     null,
    t0745        decimal(16, 4)                     null,
    t0800        decimal(16, 4)                     null,
    t0815        decimal(16, 4)                     null,
    t0830        decimal(16, 4)                     null,
    t0845        decimal(16, 4)                     null,
    t0900        decimal(16, 4)                     null,
    t0915        decimal(16, 4)                     null,
    t0930        decimal(16, 4)                     null,
    t0945        decimal(16, 4)                     null,
    t1000        decimal(16, 4)                     null,
    t1015        decimal(16, 4)                     null,
    t1030        decimal(16, 4)                     null,
    t1045        decimal(16, 4)                     null,
    t1100        decimal(16, 4)                     null,
    t1115        decimal(16, 4)                     null,
    t1130        decimal(16, 4)                     null,
    t1145        decimal(16, 4)                     null,
    t1200        decimal(16, 4)                     null,
    t1215        decimal(16, 4)                     null,
    t1230        decimal(16, 4)                     null,
    t1245        decimal(16, 4)                     null,
    t1300        decimal(16, 4)                     null,
    t1315        decimal(16, 4)                     null,
    t1330        decimal(16, 4)                     null,
    t1345        decimal(16, 4)                     null,
    t1400        decimal(16, 4)                     null,
    t1415        decimal(16, 4)                     null,
    t1430        decimal(16, 4)                     null,
    t1445        decimal(16, 4)                     null,
    t1500        decimal(16, 4)                     null,
    t1515        decimal(16, 4)                     null,
    t1530        decimal(16, 4)                     null,
    t1545        decimal(16, 4)                     null,
    t1600        decimal(16, 4)                     null,
    t1615        decimal(16, 4)                     null,
    t1630        decimal(16, 4)                     null,
    t1645        decimal(16, 4)                     null,
    t1700        decimal(16, 4)                     null,
    t1715        decimal(16, 4)                     null,
    t1730        decimal(16, 4)                     null,
    t1745        decimal(16, 4)                     null,
    t1800        decimal(16, 4)                     null,
    t1815        decimal(16, 4)                     null,
    t1830        decimal(16, 4)                     null,
    t1845        decimal(16, 4)                     null,
    t1900        decimal(16, 4)                     null,
    t1915        decimal(16, 4)                     null,
    t1930        decimal(16, 4)                     null,
    t1945        decimal(16, 4)                     null,
    t2000        decimal(16, 4)                     null,
    t2015        decimal(16, 4)                     null,
    t2030        decimal(16, 4)                     null,
    t2045        decimal(16, 4)                     null,
    t2100        decimal(16, 4)                     null,
    t2115        decimal(16, 4)                     null,
    t2130        decimal(16, 4)                     null,
    t2145        decimal(16, 4)                     null,
    t2200        decimal(16, 4)                     null,
    t2215        decimal(16, 4)                     null,
    t2230        decimal(16, 4)                     null,
    t2245        decimal(16, 4)                     null,
    t2300        decimal(16, 4)                     null,
    t2315        decimal(16, 4)                     null,
    t2330        decimal(16, 4)                     null,
    t2345        decimal(16, 4)                     null,
    t2400        decimal(16, 4)                     null,
    createtime   datetime default CURRENT_TIMESTAMP not null,
    updatetime   datetime                           null on update CURRENT_TIMESTAMP,
    constraint small_power_load_fc_basic_unique
        unique (date_time, city_id)
)
    comment '分布式光伏预测出力采集' collate = utf8_general_ci;

create table load_guangdong_dev_v3.small_power_load_his_basic
(
    id         varchar(32)                        not null comment 'id'
        primary key,
    date_time  date                               null,
    city_id    varchar(32)                        not null comment '城市id',
    t0000      decimal(16, 4)                     null,
    t0015      decimal(16, 4)                     null,
    t0030      decimal(16, 4)                     null,
    t0045      decimal(16, 4)                     null,
    t0100      decimal(16, 4)                     null,
    t0115      decimal(16, 4)                     null,
    t0130      decimal(16, 4)                     null,
    t0145      decimal(16, 4)                     null,
    t0200      decimal(16, 4)                     null,
    t0215      decimal(16, 4)                     null,
    t0230      decimal(16, 4)                     null,
    t0245      decimal(16, 4)                     null,
    t0300      decimal(16, 4)                     null,
    t0315      decimal(16, 4)                     null,
    t0330      decimal(16, 4)                     null,
    t0345      decimal(16, 4)                     null,
    t0400      decimal(16, 4)                     null,
    t0415      decimal(16, 4)                     null,
    t0430      decimal(16, 4)                     null,
    t0445      decimal(16, 4)                     null,
    t0500      decimal(16, 4)                     null,
    t0515      decimal(16, 4)                     null,
    t0530      decimal(16, 4)                     null,
    t0545      decimal(16, 4)                     null,
    t0600      decimal(16, 4)                     null,
    t0615      decimal(16, 4)                     null,
    t0630      decimal(16, 4)                     null,
    t0645      decimal(16, 4)                     null,
    t0700      decimal(16, 4)                     null,
    t0715      decimal(16, 4)                     null,
    t0730      decimal(16, 4)                     null,
    t0745      decimal(16, 4)                     null,
    t0800      decimal(16, 4)                     null,
    t0815      decimal(16, 4)                     null,
    t0830      decimal(16, 4)                     null,
    t0845      decimal(16, 4)                     null,
    t0900      decimal(16, 4)                     null,
    t0915      decimal(16, 4)                     null,
    t0930      decimal(16, 4)                     null,
    t0945      decimal(16, 4)                     null,
    t1000      decimal(16, 4)                     null,
    t1015      decimal(16, 4)                     null,
    t1030      decimal(16, 4)                     null,
    t1045      decimal(16, 4)                     null,
    t1100      decimal(16, 4)                     null,
    t1115      decimal(16, 4)                     null,
    t1130      decimal(16, 4)                     null,
    t1145      decimal(16, 4)                     null,
    t1200      decimal(16, 4)                     null,
    t1215      decimal(16, 4)                     null,
    t1230      decimal(16, 4)                     null,
    t1245      decimal(16, 4)                     null,
    t1300      decimal(16, 4)                     null,
    t1315      decimal(16, 4)                     null,
    t1330      decimal(16, 4)                     null,
    t1345      decimal(16, 4)                     null,
    t1400      decimal(16, 4)                     null,
    t1415      decimal(16, 4)                     null,
    t1430      decimal(16, 4)                     null,
    t1445      decimal(16, 4)                     null,
    t1500      decimal(16, 4)                     null,
    t1515      decimal(16, 4)                     null,
    t1530      decimal(16, 4)                     null,
    t1545      decimal(16, 4)                     null,
    t1600      decimal(16, 4)                     null,
    t1615      decimal(16, 4)                     null,
    t1630      decimal(16, 4)                     null,
    t1645      decimal(16, 4)                     null,
    t1700      decimal(16, 4)                     null,
    t1715      decimal(16, 4)                     null,
    t1730      decimal(16, 4)                     null,
    t1745      decimal(16, 4)                     null,
    t1800      decimal(16, 4)                     null,
    t1815      decimal(16, 4)                     null,
    t1830      decimal(16, 4)                     null,
    t1845      decimal(16, 4)                     null,
    t1900      decimal(16, 4)                     null,
    t1915      decimal(16, 4)                     null,
    t1930      decimal(16, 4)                     null,
    t1945      decimal(16, 4)                     null,
    t2000      decimal(16, 4)                     null,
    t2015      decimal(16, 4)                     null,
    t2030      decimal(16, 4)                     null,
    t2045      decimal(16, 4)                     null,
    t2100      decimal(16, 4)                     null,
    t2115      decimal(16, 4)                     null,
    t2130      decimal(16, 4)                     null,
    t2145      decimal(16, 4)                     null,
    t2200      decimal(16, 4)                     null,
    t2215      decimal(16, 4)                     null,
    t2230      decimal(16, 4)                     null,
    t2245      decimal(16, 4)                     null,
    t2300      decimal(16, 4)                     null,
    t2315      decimal(16, 4)                     null,
    t2330      decimal(16, 4)                     null,
    t2345      decimal(16, 4)                     null,
    t2400      decimal(16, 4)                     null,
    createtime datetime default CURRENT_TIMESTAMP not null,
    updatetime datetime                           null on update CURRENT_TIMESTAMP,
    constraint small_power_load_his_basic_unique
        unique (date_time, city_id)
)
    comment '小电源历史出力采集' collate = utf8_general_ci;

CREATE TABLE `load_city_fc_basic_week`
(
    `id` varchar(32) NOT NULL COMMENT '96点负荷表id',
    `date` date NOT NULL COMMENT '日期',
    `city_id` varchar(32) NOT NULL COMMENT '城市表id',
    `caliber_id` varchar(32) NOT NULL COMMENT '口径id',
    `algorithm_id` varchar(32) NOT NULL COMMENT '预测算法id',
    `t0000` decimal(32,4) DEFAULT NULL,
    `t0015` decimal(32,4) DEFAULT NULL,
    `t0030` decimal(32,4) DEFAULT NULL,
    `t0045` decimal(32,4) DEFAULT NULL,
    `t0100` decimal(32,4) DEFAULT NULL,
    `t0115` decimal(32,4) DEFAULT NULL,
    `t0130` decimal(32,4) DEFAULT NULL,
    `t0145` decimal(32,4) DEFAULT NULL,
    `t0200` decimal(32,4) DEFAULT NULL,
    `t0215` decimal(32,4) DEFAULT NULL,
    `t0230` decimal(32,4) DEFAULT NULL,
    `t0245` decimal(32,4) DEFAULT NULL,
    `t0300` decimal(32,4) DEFAULT NULL,
    `t0315` decimal(32,4) DEFAULT NULL,
    `t0330` decimal(32,4) DEFAULT NULL,
    `t0345` decimal(32,4) DEFAULT NULL,
    `t0400` decimal(32,4) DEFAULT NULL,
    `t0415` decimal(32,4) DEFAULT NULL,
    `t0430` decimal(32,4) DEFAULT NULL,
    `t0445` decimal(32,4) DEFAULT NULL,
    `t0500` decimal(32,4) DEFAULT NULL,
    `t0515` decimal(32,4) DEFAULT NULL,
    `t0530` decimal(32,4) DEFAULT NULL,
    `t0545` decimal(32,4) DEFAULT NULL,
    `t0600` decimal(32,4) DEFAULT NULL,
    `t0615` decimal(32,4) DEFAULT NULL,
    `t0630` decimal(32,4) DEFAULT NULL,
    `t0645` decimal(32,4) DEFAULT NULL,
    `t0700` decimal(32,4) DEFAULT NULL,
    `t0715` decimal(32,4) DEFAULT NULL,
    `t0730` decimal(32,4) DEFAULT NULL,
    `t0745` decimal(32,4) DEFAULT NULL,
    `t0800` decimal(32,4) DEFAULT NULL,
    `t0815` decimal(32,4) DEFAULT NULL,
    `t0830` decimal(32,4) DEFAULT NULL,
    `t0845` decimal(32,4) DEFAULT NULL,
    `t0900` decimal(32,4) DEFAULT NULL,
    `t0915` decimal(32,4) DEFAULT NULL,
    `t0930` decimal(32,4) DEFAULT NULL,
    `t0945` decimal(32,4) DEFAULT NULL,
    `t1000` decimal(32,4) DEFAULT NULL,
    `t1015` decimal(32,4) DEFAULT NULL,
    `t1030` decimal(32,4) DEFAULT NULL,
    `t1045` decimal(32,4) DEFAULT NULL,
    `t1100` decimal(32,4) DEFAULT NULL,
    `t1115` decimal(32,4) DEFAULT NULL,
    `t1130` decimal(32,4) DEFAULT NULL,
    `t1145` decimal(32,4) DEFAULT NULL,
    `t1200` decimal(32,4) DEFAULT NULL,
    `t1215` decimal(32,4) DEFAULT NULL,
    `t1230` decimal(32,4) DEFAULT NULL,
    `t1245` decimal(32,4) DEFAULT NULL,
    `t1300` decimal(32,4) DEFAULT NULL,
    `t1315` decimal(32,4) DEFAULT NULL,
    `t1330` decimal(32,4) DEFAULT NULL,
    `t1345` decimal(32,4) DEFAULT NULL,
    `t1400` decimal(32,4) DEFAULT NULL,
    `t1415` decimal(32,4) DEFAULT NULL,
    `t1430` decimal(32,4) DEFAULT NULL,
    `t1445` decimal(32,4) DEFAULT NULL,
    `t1500` decimal(32,4) DEFAULT NULL,
    `t1515` decimal(32,4) DEFAULT NULL,
    `t1530` decimal(32,4) DEFAULT NULL,
    `t1545` decimal(32,4) DEFAULT NULL,
    `t1600` decimal(32,4) DEFAULT NULL,
    `t1615` decimal(32,4) DEFAULT NULL,
    `t1630` decimal(32,4) DEFAULT NULL,
    `t1645` decimal(32,4) DEFAULT NULL,
    `t1700` decimal(32,4) DEFAULT NULL,
    `t1715` decimal(32,4) DEFAULT NULL,
    `t1730` decimal(32,4) DEFAULT NULL,
    `t1745` decimal(32,4) DEFAULT NULL,
    `t1800` decimal(32,4) DEFAULT NULL,
    `t1815` decimal(32,4) DEFAULT NULL,
    `t1830` decimal(32,4) DEFAULT NULL,
    `t1845` decimal(32,4) DEFAULT NULL,
    `t1900` decimal(32,4) DEFAULT NULL,
    `t1915` decimal(32,4) DEFAULT NULL,
    `t1930` decimal(32,4) DEFAULT NULL,
    `t1945` decimal(32,4) DEFAULT NULL,
    `t2000` decimal(32,4) DEFAULT NULL,
    `t2015` decimal(32,4) DEFAULT NULL,
    `t2030` decimal(32,4) DEFAULT NULL,
    `t2045` decimal(32,4) DEFAULT NULL,
    `t2100` decimal(32,4) DEFAULT NULL,
    `t2115` decimal(32,4) DEFAULT NULL,
    `t2130` decimal(32,4) DEFAULT NULL,
    `t2145` decimal(32,4) DEFAULT NULL,
    `t2200` decimal(32,4) DEFAULT NULL,
    `t2215` decimal(32,4) DEFAULT NULL,
    `t2230` decimal(32,4) DEFAULT NULL,
    `t2245` decimal(32,4) DEFAULT NULL,
    `t2300` decimal(32,4) DEFAULT NULL,
    `t2315` decimal(32,4) DEFAULT NULL,
    `t2330` decimal(32,4) DEFAULT NULL,
    `t2345` decimal(32,4) DEFAULT NULL,
    `t2400` decimal(32,4) DEFAULT NULL,
    `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updatetime` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    `recommend` tinyint(1) DEFAULT NULL,
    `report` tinyint(1) DEFAULT '0' COMMENT '是否上报结果（1：是；0：否）',
    `report_time` datetime DEFAULT NULL COMMENT '上报时间',
    `user_id` varchar(32) DEFAULT NULL COMMENT '操作者ID',
    `succeed` tinyint(1) DEFAULT NULL COMMENT '是否上报成功（1：是；0：否）',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `load_city_fc_unique` (`date`,`city_id`,`algorithm_id`,`caliber_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='地区周预测负荷表';

CREATE TABLE `weather_feature_city_week_fc_service`
(
    `id` varchar(32) NOT NULL COMMENT 'id',
    `city_id` varchar(32) NOT NULL COMMENT '城市id',
    `date` date NOT NULL COMMENT '日期',
    `highest_temperature` decimal(32,2) DEFAULT NULL COMMENT '最高温度',
    `lowest_temperature` decimal(32,2) DEFAULT NULL COMMENT '最低温度',
    `createtime` datetime DEFAULT CURRENT_TIMESTAMP,
    `updatetime` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `weather_feature_city_day_long_fc_service` (`city_id`,`date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='周气象日特性表';

CREATE TABLE `weather_forecast_info`
(
    `id` varchar(32) NOT NULL COMMENT '天气预报信息配置表id',
    `date` date NOT NULL COMMENT '日期',
    `path` varchar(1000) NOT NULL COMMENT '文件路径',
    `name` varchar(32) NOT NULL COMMENT '文件名称',
    `createtime` datetime DEFAULT CURRENT_TIMESTAMP,
    `updatetime` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='天气预报信息配置表';

INSERT INTO load_guangdong_dev_v3.weather_forecast_info (id, `date`, `path`, name, createtime, updatetime) VALUES ('20230426001', '2023-04-26', '/home/<USER>/lf/lf/weather/forecast/', 'forecast.txt', '2023-04-26 20:11:14', '2023-04-27 09:21:40');

CREATE TABLE `base_storage_info`
(
    `id` varchar(32) CHARACTER SET utf8 NOT NULL,
    `station_name` varchar(100) CHARACTER SET utf8 DEFAULT NULL COMMENT '电厂名称',
    `city_name` varchar(10) CHARACTER SET utf8 DEFAULT NULL COMMENT '所属地区',
    `relation` varchar(10) CHARACTER SET utf8 DEFAULT NULL COMMENT '调度关系',
    `capacity` decimal(10,2) DEFAULT NULL COMMENT '额定容量',
    `power` decimal(10,2) DEFAULT NULL COMMENT '额定功率',
    `speed` decimal(10,2) DEFAULT NULL COMMENT '调节速率',
    `level` decimal(10,2) DEFAULT NULL COMMENT '电压等级',
    `upper` decimal(10,2) DEFAULT NULL COMMENT '调节上限',
    `lower` decimal(10,2) DEFAULT NULL COMMENT '调节下限',
    `free_soc` decimal(10,2) DEFAULT NULL COMMENT 'SOC',
    `upper_soc` decimal(10,2) DEFAULT NULL COMMENT 'SOC上限',
    `lower_soc` decimal(10,2) DEFAULT NULL COMMENT 'SOC上限',
    `max_charge` decimal(10,2) DEFAULT NULL COMMENT '最大充电时长',
    `max_discharge` decimal(10,2) DEFAULT NULL COMMENT '最大放电时长',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `load_storage_power_fc_basic`
(
    `id` varchar(32) NOT NULL COMMENT '96点负荷表id',
    `date` date NOT NULL COMMENT '日期',
    `city_id` varchar(32) NOT NULL COMMENT '城市表id',
    `station_id` varchar(32) NOT NULL COMMENT '机组id',
    `name` varchar(32) NOT NULL COMMENT '机组名称',
    `t0000` decimal(32,4) DEFAULT NULL,
    `t0015` decimal(32,4) DEFAULT NULL,
    `t0030` decimal(32,4) DEFAULT NULL,
    `t0045` decimal(32,4) DEFAULT NULL,
    `t0100` decimal(32,4) DEFAULT NULL,
    `t0115` decimal(32,4) DEFAULT NULL,
    `t0130` decimal(32,4) DEFAULT NULL,
    `t0145` decimal(32,4) DEFAULT NULL,
    `t0200` decimal(32,4) DEFAULT NULL,
    `t0215` decimal(32,4) DEFAULT NULL,
    `t0230` decimal(32,4) DEFAULT NULL,
    `t0245` decimal(32,4) DEFAULT NULL,
    `t0300` decimal(32,4) DEFAULT NULL,
    `t0315` decimal(32,4) DEFAULT NULL,
    `t0330` decimal(32,4) DEFAULT NULL,
    `t0345` decimal(32,4) DEFAULT NULL,
    `t0400` decimal(32,4) DEFAULT NULL,
    `t0415` decimal(32,4) DEFAULT NULL,
    `t0430` decimal(32,4) DEFAULT NULL,
    `t0445` decimal(32,4) DEFAULT NULL,
    `t0500` decimal(32,4) DEFAULT NULL,
    `t0515` decimal(32,4) DEFAULT NULL,
    `t0530` decimal(32,4) DEFAULT NULL,
    `t0545` decimal(32,4) DEFAULT NULL,
    `t0600` decimal(32,4) DEFAULT NULL,
    `t0615` decimal(32,4) DEFAULT NULL,
    `t0630` decimal(32,4) DEFAULT NULL,
    `t0645` decimal(32,4) DEFAULT NULL,
    `t0700` decimal(32,4) DEFAULT NULL,
    `t0715` decimal(32,4) DEFAULT NULL,
    `t0730` decimal(32,4) DEFAULT NULL,
    `t0745` decimal(32,4) DEFAULT NULL,
    `t0800` decimal(32,4) DEFAULT NULL,
    `t0815` decimal(32,4) DEFAULT NULL,
    `t0830` decimal(32,4) DEFAULT NULL,
    `t0845` decimal(32,4) DEFAULT NULL,
    `t0900` decimal(32,4) DEFAULT NULL,
    `t0915` decimal(32,4) DEFAULT NULL,
    `t0930` decimal(32,4) DEFAULT NULL,
    `t0945` decimal(32,4) DEFAULT NULL,
    `t1000` decimal(32,4) DEFAULT NULL,
    `t1015` decimal(32,4) DEFAULT NULL,
    `t1030` decimal(32,4) DEFAULT NULL,
    `t1045` decimal(32,4) DEFAULT NULL,
    `t1100` decimal(32,4) DEFAULT NULL,
    `t1115` decimal(32,4) DEFAULT NULL,
    `t1130` decimal(32,4) DEFAULT NULL,
    `t1145` decimal(32,4) DEFAULT NULL,
    `t1200` decimal(32,4) DEFAULT NULL,
    `t1215` decimal(32,4) DEFAULT NULL,
    `t1230` decimal(32,4) DEFAULT NULL,
    `t1245` decimal(32,4) DEFAULT NULL,
    `t1300` decimal(32,4) DEFAULT NULL,
    `t1315` decimal(32,4) DEFAULT NULL,
    `t1330` decimal(32,4) DEFAULT NULL,
    `t1345` decimal(32,4) DEFAULT NULL,
    `t1400` decimal(32,4) DEFAULT NULL,
    `t1415` decimal(32,4) DEFAULT NULL,
    `t1430` decimal(32,4) DEFAULT NULL,
    `t1445` decimal(32,4) DEFAULT NULL,
    `t1500` decimal(32,4) DEFAULT NULL,
    `t1515` decimal(32,4) DEFAULT NULL,
    `t1530` decimal(32,4) DEFAULT NULL,
    `t1545` decimal(32,4) DEFAULT NULL,
    `t1600` decimal(32,4) DEFAULT NULL,
    `t1615` decimal(32,4) DEFAULT NULL,
    `t1630` decimal(32,4) DEFAULT NULL,
    `t1645` decimal(32,4) DEFAULT NULL,
    `t1700` decimal(32,4) DEFAULT NULL,
    `t1715` decimal(32,4) DEFAULT NULL,
    `t1730` decimal(32,4) DEFAULT NULL,
    `t1745` decimal(32,4) DEFAULT NULL,
    `t1800` decimal(32,4) DEFAULT NULL,
    `t1815` decimal(32,4) DEFAULT NULL,
    `t1830` decimal(32,4) DEFAULT NULL,
    `t1845` decimal(32,4) DEFAULT NULL,
    `t1900` decimal(32,4) DEFAULT NULL,
    `t1915` decimal(32,4) DEFAULT NULL,
    `t1930` decimal(32,4) DEFAULT NULL,
    `t1945` decimal(32,4) DEFAULT NULL,
    `t2000` decimal(32,4) DEFAULT NULL,
    `t2015` decimal(32,4) DEFAULT NULL,
    `t2030` decimal(32,4) DEFAULT NULL,
    `t2045` decimal(32,4) DEFAULT NULL,
    `t2100` decimal(32,4) DEFAULT NULL,
    `t2115` decimal(32,4) DEFAULT NULL,
    `t2130` decimal(32,4) DEFAULT NULL,
    `t2145` decimal(32,4) DEFAULT NULL,
    `t2200` decimal(32,4) DEFAULT NULL,
    `t2215` decimal(32,4) DEFAULT NULL,
    `t2230` decimal(32,4) DEFAULT NULL,
    `t2245` decimal(32,4) DEFAULT NULL,
    `t2300` decimal(32,4) DEFAULT NULL,
    `t2315` decimal(32,4) DEFAULT NULL,
    `t2330` decimal(32,4) DEFAULT NULL,
    `t2345` decimal(32,4) DEFAULT NULL,
    `t2400` decimal(32,4) DEFAULT NULL,
    `createtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updatetime` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `load_city_fc_storage` (`date`,`city_id`,`station_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='储能预测负荷表';

CREATE TABLE `load_storage_power_his_basic`
(
    `id` varchar(32) NOT NULL,
    `date` date DEFAULT NULL,
    `station_id` varchar(32) DEFAULT NULL,
    `city_id` varchar(32) DEFAULT NULL,
    `t0000` decimal(32,4) DEFAULT NULL,
    `t0015` decimal(32,4) DEFAULT NULL,
    `t0030` decimal(32,4) DEFAULT NULL,
    `t0045` decimal(32,4) DEFAULT NULL,
    `t0100` decimal(32,4) DEFAULT NULL,
    `t0115` decimal(32,4) DEFAULT NULL,
    `t0130` decimal(32,4) DEFAULT NULL,
    `t0145` decimal(32,4) DEFAULT NULL,
    `t0200` decimal(32,4) DEFAULT NULL,
    `t0215` decimal(32,4) DEFAULT NULL,
    `t0230` decimal(32,4) DEFAULT NULL,
    `t0245` decimal(32,4) DEFAULT NULL,
    `t0300` decimal(32,4) DEFAULT NULL,
    `t0315` decimal(32,4) DEFAULT NULL,
    `t0330` decimal(32,4) DEFAULT NULL,
    `t0345` decimal(32,4) DEFAULT NULL,
    `t0400` decimal(32,4) DEFAULT NULL,
    `t0415` decimal(32,4) DEFAULT NULL,
    `t0430` decimal(32,4) DEFAULT NULL,
    `t0445` decimal(32,4) DEFAULT NULL,
    `t0500` decimal(32,4) DEFAULT NULL,
    `t0515` decimal(32,4) DEFAULT NULL,
    `t0530` decimal(32,4) DEFAULT NULL,
    `t0545` decimal(32,4) DEFAULT NULL,
    `t0600` decimal(32,4) DEFAULT NULL,
    `t0615` decimal(32,4) DEFAULT NULL,
    `t0630` decimal(32,4) DEFAULT NULL,
    `t0645` decimal(32,4) DEFAULT NULL,
    `t0700` decimal(32,4) DEFAULT NULL,
    `t0715` decimal(32,4) DEFAULT NULL,
    `t0730` decimal(32,4) DEFAULT NULL,
    `t0745` decimal(32,4) DEFAULT NULL,
    `t0800` decimal(32,4) DEFAULT NULL,
    `t0815` decimal(32,4) DEFAULT NULL,
    `t0830` decimal(32,4) DEFAULT NULL,
    `t0845` decimal(32,4) DEFAULT NULL,
    `t0900` decimal(32,4) DEFAULT NULL,
    `t0915` decimal(32,4) DEFAULT NULL,
    `t0930` decimal(32,4) DEFAULT NULL,
    `t0945` decimal(32,4) DEFAULT NULL,
    `t1000` decimal(32,4) DEFAULT NULL,
    `t1015` decimal(32,4) DEFAULT NULL,
    `t1030` decimal(32,4) DEFAULT NULL,
    `t1045` decimal(32,4) DEFAULT NULL,
    `t1100` decimal(32,4) DEFAULT NULL,
    `t1115` decimal(32,4) DEFAULT NULL,
    `t1130` decimal(32,4) DEFAULT NULL,
    `t1145` decimal(32,4) DEFAULT NULL,
    `t1200` decimal(32,4) DEFAULT NULL,
    `t1215` decimal(32,4) DEFAULT NULL,
    `t1230` decimal(32,4) DEFAULT NULL,
    `t1245` decimal(32,4) DEFAULT NULL,
    `t1300` decimal(32,4) DEFAULT NULL,
    `t1315` decimal(32,4) DEFAULT NULL,
    `t1330` decimal(32,4) DEFAULT NULL,
    `t1345` decimal(32,4) DEFAULT NULL,
    `t1400` decimal(32,4) DEFAULT NULL,
    `t1415` decimal(32,4) DEFAULT NULL,
    `t1430` decimal(32,4) DEFAULT NULL,
    `t1445` decimal(32,4) DEFAULT NULL,
    `t1500` decimal(32,4) DEFAULT NULL,
    `t1515` decimal(32,4) DEFAULT NULL,
    `t1530` decimal(32,4) DEFAULT NULL,
    `t1545` decimal(32,4) DEFAULT NULL,
    `t1600` decimal(32,4) DEFAULT NULL,
    `t1615` decimal(32,4) DEFAULT NULL,
    `t1630` decimal(32,4) DEFAULT NULL,
    `t1645` decimal(32,4) DEFAULT NULL,
    `t1700` decimal(32,4) DEFAULT NULL,
    `t1715` decimal(32,4) DEFAULT NULL,
    `t1730` decimal(32,4) DEFAULT NULL,
    `t1745` decimal(32,4) DEFAULT NULL,
    `t1800` decimal(32,4) DEFAULT NULL,
    `t1815` decimal(32,4) DEFAULT NULL,
    `t1830` decimal(32,4) DEFAULT NULL,
    `t1845` decimal(32,4) DEFAULT NULL,
    `t1900` decimal(32,4) DEFAULT NULL,
    `t1915` decimal(32,4) DEFAULT NULL,
    `t1930` decimal(32,4) DEFAULT NULL,
    `t1945` decimal(32,4) DEFAULT NULL,
    `t2000` decimal(32,4) DEFAULT NULL,
    `t2015` decimal(32,4) DEFAULT NULL,
    `t2030` decimal(32,4) DEFAULT NULL,
    `t2045` decimal(32,4) DEFAULT NULL,
    `t2100` decimal(32,4) DEFAULT NULL,
    `t2115` decimal(32,4) DEFAULT NULL,
    `t2130` decimal(32,4) DEFAULT NULL,
    `t2145` decimal(32,4) DEFAULT NULL,
    `t2200` decimal(32,4) DEFAULT NULL,
    `t2215` decimal(32,4) DEFAULT NULL,
    `t2230` decimal(32,4) DEFAULT NULL,
    `t2245` decimal(32,4) DEFAULT NULL,
    `t2300` decimal(32,4) DEFAULT NULL,
    `t2315` decimal(32,4) DEFAULT NULL,
    `t2330` decimal(32,4) DEFAULT NULL,
    `t2345` decimal(32,4) DEFAULT NULL,
    `t2400` decimal(32,4) DEFAULT NULL,
    `createtime` datetime DEFAULT NULL,
    `updatetime` datetime DEFAULT NULL,
    UNIQUE KEY `load_storage_power_unique` (`date`,`station_id`,`city_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='储能实际负荷数据';

CREATE TABLE `base_storage_info_clct`
(
    `id` varchar(32) CHARACTER SET utf8 NOT NULL,
    `date` date NOT NULL COMMENT '日期',
    `station_id` varchar(100) CHARACTER SET utf8 DEFAULT NULL COMMENT '电厂id',
    `station_name` varchar(100) CHARACTER SET utf8 DEFAULT NULL COMMENT '电厂名称',
    `city_name` varchar(10) CHARACTER SET utf8 DEFAULT NULL COMMENT '所属地区',
    `max_charge` decimal(10,2) DEFAULT NULL COMMENT '最大充电时长',
    `max_discharge` decimal(10,2) DEFAULT NULL COMMENT '最大放电时长',
    PRIMARY KEY (`id`),
    UNIQUE KEY `base_storage_info_clct` (`date`,`station_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `load_guangdong_dev_v3`.`tsie_menu`(`id`, `name`, `type`, `menupath`, `parentId`, `description`, `targetType`, `rel`, `sort`) VALUES ('4028d0816cb8644d016cb86f05751111', '月度特性对比', 1, 'MonthCharacteristicCompare', '4028d0816cb8644d016cb86f056100fe', '月度特性对比', 'navTab', '月度特性对比', 9999);

alter table base_storage_info
    add loop_base decimal(10, 2) null comment '循环效率基数(计算循环效率 当日充电量出循环效率基数)';

UPDATE load_guangdong_dev_v3.base_storage_info t SET t.loop_base = 15.00 WHERE t.id LIKE '122723090013693849' ESCAPE '#';
UPDATE load_guangdong_dev_v3.base_storage_info t SET t.loop_base = 20.00 WHERE t.id LIKE '122723090013693741' ESCAPE '#';
UPDATE load_guangdong_dev_v3.base_storage_info t SET t.loop_base = 10.00 WHERE t.id LIKE '122723090013693789' ESCAPE '#';
UPDATE load_guangdong_dev_v3.base_storage_info t SET t.loop_base = 15.00 WHERE t.id LIKE '122723090013693928' ESCAPE '#';
UPDATE load_guangdong_dev_v3.base_storage_info t SET t.loop_base = 140.00 WHERE t.id LIKE '122723090013695791' ESCAPE '#';
UPDATE load_guangdong_dev_v3.base_storage_info t SET t.loop_base = 10.00 WHERE t.id LIKE '122723090013693757' ESCAPE '#';
UPDATE load_guangdong_dev_v3.base_storage_info t SET t.loop_base = 20.00 WHERE t.id LIKE '122723090013693773' ESCAPE '#';

INSERT INTO load_guangdong_dev_v3.base_storage_info (id, station_name, city_name, relation, capacity, power, speed, level, upper, lower, free_soc, upper_soc, lower_soc, max_charge, max_discharge, loop_base) VALUES ('12272309001369579', '宝塘储能电站', '广东', '广东中调', null, null, null, null, null, null, null, null, null, null, null, 300.00);
INSERT INTO load_guangdong_dev_v3.base_storage_info (id, station_name, city_name, relation, capacity, power, speed, level, upper, lower, free_soc, upper_soc, lower_soc, max_charge, max_discharge, loop_base) VALUES ('1227230900136957', '峡安储能电站', '广东', '广东中调', null, null, null, null, null, null, null, null, null, null, null, 30.00);
INSERT INTO load_guangdong_dev_v3.base_storage_info (id, station_name, city_name, relation, capacity, power, speed, level, upper, lower, free_soc, upper_soc, lower_soc, max_charge, max_discharge, loop_base) VALUES ('1227209001369579', '万羚储能电站', '广东', '广东中调', null, null, null, null, null, null, null, null, null, null, null, 100.00);

INSERT INTO load_guangdong_dev_v3.tsie_menu (id, name, type, menupath, parentId, description, targetType, rel, sort) VALUES ('166449745411', '储能电站特性统计', 1, 'Characteristics', '4028d0816cb8644d016cb86f05950111', '储能电站特性统计', 'navTab', '储能电站特性统计', 9999);
-- 241120 节假日早午晚高峰特性分析
INSERT INTO load_guangdong_dev_v3.tsie_menu (id, name, type, menupath, parentId, description, targetType, rel, sort) VALUES ('4028d06cb8644d016cb86f057d0145', '节假日早午高峰特性分析', 1, 'HolidayFeatures', '4028d0816cb8644d016cb86f056100fe', '节假日早午高峰特性分析', 'navTab', '节假日早午高峰特性分析', 9999);

CREATE TABLE "weather_city_station_fc_basic_gdt"
(
    "id" varchar(32) NOT NULL COMMENT '96点气象表ID',
    "date" date NOT NULL COMMENT '日期',
    "city_id" varchar(32) NOT NULL COMMENT '潮州	1\r\n东莞	2\r\n佛山	3\r\n广州	4\r\n河源	5\r\n惠州	6\r\n江门	7\r\n揭阳	8\r\n茂名	9\r\n梅州	10\r\n清远	11\r\n汕头	12\r\n汕尾	13\r\n韶关	14\r\n深圳	15\r\n阳江	16\r\n云浮	17\r\n湛江	18\r\n肇庆	19\r\n中山	20\r\n珠海	21\r\n',
    "type" tinyint(2) DEFAULT NULL COMMENT '气象类型(1：湿度，2：温度，3：降雨量，4：风速)',
    "t0000" decimal(32,2) DEFAULT NULL,
    "t0015" decimal(32,2) DEFAULT NULL,
    "t0030" decimal(32,2) DEFAULT NULL,
    "t0045" decimal(32,2) DEFAULT NULL,
    "t0100" decimal(32,2) DEFAULT NULL,
    "t0115" decimal(32,2) DEFAULT NULL,
    "t0130" decimal(32,2) DEFAULT NULL,
    "t0145" decimal(32,2) DEFAULT NULL,
    "t0200" decimal(32,2) DEFAULT NULL,
    "t0215" decimal(32,2) DEFAULT NULL,
    "t0230" decimal(32,2) DEFAULT NULL,
    "t0245" decimal(32,2) DEFAULT NULL,
    "t0300" decimal(32,2) DEFAULT NULL,
    "t0315" decimal(32,2) DEFAULT NULL,
    "t0330" decimal(32,2) DEFAULT NULL,
    "t0345" decimal(32,2) DEFAULT NULL,
    "t0400" decimal(32,2) DEFAULT NULL,
    "t0415" decimal(32,2) DEFAULT NULL,
    "t0430" decimal(32,2) DEFAULT NULL,
    "t0445" decimal(32,2) DEFAULT NULL,
    "t0500" decimal(32,2) DEFAULT NULL,
    "t0515" decimal(32,2) DEFAULT NULL,
    "t0530" decimal(32,2) DEFAULT NULL,
    "t0545" decimal(32,2) DEFAULT NULL,
    "t0600" decimal(32,2) DEFAULT NULL,
    "t0615" decimal(32,2) DEFAULT NULL,
    "t0630" decimal(32,2) DEFAULT NULL,
    "t0645" decimal(32,2) DEFAULT NULL,
    "t0700" decimal(32,2) DEFAULT NULL,
    "t0715" decimal(32,2) DEFAULT NULL,
    "t0730" decimal(32,2) DEFAULT NULL,
    "t0745" decimal(32,2) DEFAULT NULL,
    "t0800" decimal(32,2) DEFAULT NULL,
    "t0815" decimal(32,2) DEFAULT NULL,
    "t0830" decimal(32,2) DEFAULT NULL,
    "t0845" decimal(32,2) DEFAULT NULL,
    "t0900" decimal(32,2) DEFAULT NULL,
    "t0915" decimal(32,2) DEFAULT NULL,
    "t0930" decimal(32,2) DEFAULT NULL,
    "t0945" decimal(32,2) DEFAULT NULL,
    "t1000" decimal(32,2) DEFAULT NULL,
    "t1015" decimal(32,2) DEFAULT NULL,
    "t1030" decimal(32,2) DEFAULT NULL,
    "t1045" decimal(32,2) DEFAULT NULL,
    "t1100" decimal(32,2) DEFAULT NULL,
    "t1115" decimal(32,2) DEFAULT NULL,
    "t1130" decimal(32,2) DEFAULT NULL,
    "t1145" decimal(32,2) DEFAULT NULL,
    "t1200" decimal(32,2) DEFAULT NULL,
    "t1215" decimal(32,2) DEFAULT NULL,
    "t1230" decimal(32,2) DEFAULT NULL,
    "t1245" decimal(32,2) DEFAULT NULL,
    "t1300" decimal(32,2) DEFAULT NULL,
    "t1315" decimal(32,2) DEFAULT NULL,
    "t1330" decimal(32,2) DEFAULT NULL,
    "t1345" decimal(32,2) DEFAULT NULL,
    "t1400" decimal(32,2) DEFAULT NULL,
    "t1415" decimal(32,2) DEFAULT NULL,
    "t1430" decimal(32,2) DEFAULT NULL,
    "t1445" decimal(32,2) DEFAULT NULL,
    "t1500" decimal(32,2) DEFAULT NULL,
    "t1515" decimal(32,2) DEFAULT NULL,
    "t1530" decimal(32,2) DEFAULT NULL,
    "t1545" decimal(32,2) DEFAULT NULL,
    "t1600" decimal(32,2) DEFAULT NULL,
    "t1615" decimal(32,2) DEFAULT NULL,
    "t1630" decimal(32,2) DEFAULT NULL,
    "t1645" decimal(32,2) DEFAULT NULL,
    "t1700" decimal(32,2) DEFAULT NULL,
    "t1715" decimal(32,2) DEFAULT NULL,
    "t1730" decimal(32,2) DEFAULT NULL,
    "t1745" decimal(32,2) DEFAULT NULL,
    "t1800" decimal(32,2) DEFAULT NULL,
    "t1815" decimal(32,2) DEFAULT NULL,
    "t1830" decimal(32,2) DEFAULT NULL,
    "t1845" decimal(32,2) DEFAULT NULL,
    "t1900" decimal(32,2) DEFAULT NULL,
    "t1915" decimal(32,2) DEFAULT NULL,
    "t1930" decimal(32,2) DEFAULT NULL,
    "t1945" decimal(32,2) DEFAULT NULL,
    "t2000" decimal(32,2) DEFAULT NULL,
    "t2015" decimal(32,2) DEFAULT NULL,
    "t2030" decimal(32,2) DEFAULT NULL,
    "t2045" decimal(32,2) DEFAULT NULL,
    "t2100" decimal(32,2) DEFAULT NULL,
    "t2115" decimal(32,2) DEFAULT NULL,
    "t2130" decimal(32,2) DEFAULT NULL,
    "t2145" decimal(32,2) DEFAULT NULL,
    "t2200" decimal(32,2) DEFAULT NULL,
    "t2215" decimal(32,2) DEFAULT NULL,
    "t2230" decimal(32,2) DEFAULT NULL,
    "t2245" decimal(32,2) DEFAULT NULL,
    "t2300" decimal(32,2) DEFAULT NULL,
    "t2315" decimal(32,2) DEFAULT NULL,
    "t2330" decimal(32,2) DEFAULT NULL,
    "t2345" decimal(32,2) DEFAULT NULL,
    "t2400" decimal(32,2) DEFAULT NULL,
    "createtime" datetime DEFAULT CURRENT_TIMESTAMP,
    "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY ("id") USING BTREE,
    UNIQUE KEY "weather_city_station_fc_clct_gdt" ("date","city_id","type") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='预测气象表';

CREATE TABLE "weather_city_station_fc_basic_jt"
(
    "id" varchar(32) NOT NULL COMMENT '96点气象表ID',
    "date" date NOT NULL COMMENT '日期',
    "city_id" varchar(32) NOT NULL COMMENT '潮州	1\r\n东莞	2\r\n佛山	3\r\n广州	4\r\n河源	5\r\n惠州	6\r\n江门	7\r\n揭阳	8\r\n茂名	9\r\n梅州	10\r\n清远	11\r\n汕头	12\r\n汕尾	13\r\n韶关	14\r\n深圳	15\r\n阳江	16\r\n云浮	17\r\n湛江	18\r\n肇庆	19\r\n中山	20\r\n珠海	21\r\n',
    "type" tinyint(2) DEFAULT NULL COMMENT '气象类型(1：湿度，2：温度，3：降雨量，4：风速)',
    "t0000" decimal(32,2) DEFAULT NULL,
    "t0015" decimal(32,2) DEFAULT NULL,
    "t0030" decimal(32,2) DEFAULT NULL,
    "t0045" decimal(32,2) DEFAULT NULL,
    "t0100" decimal(32,2) DEFAULT NULL,
    "t0115" decimal(32,2) DEFAULT NULL,
    "t0130" decimal(32,2) DEFAULT NULL,
    "t0145" decimal(32,2) DEFAULT NULL,
    "t0200" decimal(32,2) DEFAULT NULL,
    "t0215" decimal(32,2) DEFAULT NULL,
    "t0230" decimal(32,2) DEFAULT NULL,
    "t0245" decimal(32,2) DEFAULT NULL,
    "t0300" decimal(32,2) DEFAULT NULL,
    "t0315" decimal(32,2) DEFAULT NULL,
    "t0330" decimal(32,2) DEFAULT NULL,
    "t0345" decimal(32,2) DEFAULT NULL,
    "t0400" decimal(32,2) DEFAULT NULL,
    "t0415" decimal(32,2) DEFAULT NULL,
    "t0430" decimal(32,2) DEFAULT NULL,
    "t0445" decimal(32,2) DEFAULT NULL,
    "t0500" decimal(32,2) DEFAULT NULL,
    "t0515" decimal(32,2) DEFAULT NULL,
    "t0530" decimal(32,2) DEFAULT NULL,
    "t0545" decimal(32,2) DEFAULT NULL,
    "t0600" decimal(32,2) DEFAULT NULL,
    "t0615" decimal(32,2) DEFAULT NULL,
    "t0630" decimal(32,2) DEFAULT NULL,
    "t0645" decimal(32,2) DEFAULT NULL,
    "t0700" decimal(32,2) DEFAULT NULL,
    "t0715" decimal(32,2) DEFAULT NULL,
    "t0730" decimal(32,2) DEFAULT NULL,
    "t0745" decimal(32,2) DEFAULT NULL,
    "t0800" decimal(32,2) DEFAULT NULL,
    "t0815" decimal(32,2) DEFAULT NULL,
    "t0830" decimal(32,2) DEFAULT NULL,
    "t0845" decimal(32,2) DEFAULT NULL,
    "t0900" decimal(32,2) DEFAULT NULL,
    "t0915" decimal(32,2) DEFAULT NULL,
    "t0930" decimal(32,2) DEFAULT NULL,
    "t0945" decimal(32,2) DEFAULT NULL,
    "t1000" decimal(32,2) DEFAULT NULL,
    "t1015" decimal(32,2) DEFAULT NULL,
    "t1030" decimal(32,2) DEFAULT NULL,
    "t1045" decimal(32,2) DEFAULT NULL,
    "t1100" decimal(32,2) DEFAULT NULL,
    "t1115" decimal(32,2) DEFAULT NULL,
    "t1130" decimal(32,2) DEFAULT NULL,
    "t1145" decimal(32,2) DEFAULT NULL,
    "t1200" decimal(32,2) DEFAULT NULL,
    "t1215" decimal(32,2) DEFAULT NULL,
    "t1230" decimal(32,2) DEFAULT NULL,
    "t1245" decimal(32,2) DEFAULT NULL,
    "t1300" decimal(32,2) DEFAULT NULL,
    "t1315" decimal(32,2) DEFAULT NULL,
    "t1330" decimal(32,2) DEFAULT NULL,
    "t1345" decimal(32,2) DEFAULT NULL,
    "t1400" decimal(32,2) DEFAULT NULL,
    "t1415" decimal(32,2) DEFAULT NULL,
    "t1430" decimal(32,2) DEFAULT NULL,
    "t1445" decimal(32,2) DEFAULT NULL,
    "t1500" decimal(32,2) DEFAULT NULL,
    "t1515" decimal(32,2) DEFAULT NULL,
    "t1530" decimal(32,2) DEFAULT NULL,
    "t1545" decimal(32,2) DEFAULT NULL,
    "t1600" decimal(32,2) DEFAULT NULL,
    "t1615" decimal(32,2) DEFAULT NULL,
    "t1630" decimal(32,2) DEFAULT NULL,
    "t1645" decimal(32,2) DEFAULT NULL,
    "t1700" decimal(32,2) DEFAULT NULL,
    "t1715" decimal(32,2) DEFAULT NULL,
    "t1730" decimal(32,2) DEFAULT NULL,
    "t1745" decimal(32,2) DEFAULT NULL,
    "t1800" decimal(32,2) DEFAULT NULL,
    "t1815" decimal(32,2) DEFAULT NULL,
    "t1830" decimal(32,2) DEFAULT NULL,
    "t1845" decimal(32,2) DEFAULT NULL,
    "t1900" decimal(32,2) DEFAULT NULL,
    "t1915" decimal(32,2) DEFAULT NULL,
    "t1930" decimal(32,2) DEFAULT NULL,
    "t1945" decimal(32,2) DEFAULT NULL,
    "t2000" decimal(32,2) DEFAULT NULL,
    "t2015" decimal(32,2) DEFAULT NULL,
    "t2030" decimal(32,2) DEFAULT NULL,
    "t2045" decimal(32,2) DEFAULT NULL,
    "t2100" decimal(32,2) DEFAULT NULL,
    "t2115" decimal(32,2) DEFAULT NULL,
    "t2130" decimal(32,2) DEFAULT NULL,
    "t2145" decimal(32,2) DEFAULT NULL,
    "t2200" decimal(32,2) DEFAULT NULL,
    "t2215" decimal(32,2) DEFAULT NULL,
    "t2230" decimal(32,2) DEFAULT NULL,
    "t2245" decimal(32,2) DEFAULT NULL,
    "t2300" decimal(32,2) DEFAULT NULL,
    "t2315" decimal(32,2) DEFAULT NULL,
    "t2330" decimal(32,2) DEFAULT NULL,
    "t2345" decimal(32,2) DEFAULT NULL,
    "t2400" decimal(32,2) DEFAULT NULL,
    "createtime" datetime DEFAULT CURRENT_TIMESTAMP,
    "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY ("id") USING BTREE,
    UNIQUE KEY "weather_city_station_fc_clct_jt" ("date","city_id","type") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='预测气象表';

CREATE TABLE "weather_city_station_fc_basic_zh"
(
    "id" varchar(32) NOT NULL COMMENT '96点气象表ID',
    "date" date NOT NULL COMMENT '日期',
    "city_id" varchar(32) NOT NULL COMMENT '潮州	1\r\n东莞	2\r\n佛山	3\r\n广州	4\r\n河源	5\r\n惠州	6\r\n江门	7\r\n揭阳	8\r\n茂名	9\r\n梅州	10\r\n清远	11\r\n汕头	12\r\n汕尾	13\r\n韶关	14\r\n深圳	15\r\n阳江	16\r\n云浮	17\r\n湛江	18\r\n肇庆	19\r\n中山	20\r\n珠海	21\r\n',
    "type" tinyint(2) DEFAULT NULL COMMENT '气象类型(1：湿度，2：温度，3：降雨量，4：风速)',
    "t0000" decimal(32,2) DEFAULT NULL,
    "t0015" decimal(32,2) DEFAULT NULL,
    "t0030" decimal(32,2) DEFAULT NULL,
    "t0045" decimal(32,2) DEFAULT NULL,
    "t0100" decimal(32,2) DEFAULT NULL,
    "t0115" decimal(32,2) DEFAULT NULL,
    "t0130" decimal(32,2) DEFAULT NULL,
    "t0145" decimal(32,2) DEFAULT NULL,
    "t0200" decimal(32,2) DEFAULT NULL,
    "t0215" decimal(32,2) DEFAULT NULL,
    "t0230" decimal(32,2) DEFAULT NULL,
    "t0245" decimal(32,2) DEFAULT NULL,
    "t0300" decimal(32,2) DEFAULT NULL,
    "t0315" decimal(32,2) DEFAULT NULL,
    "t0330" decimal(32,2) DEFAULT NULL,
    "t0345" decimal(32,2) DEFAULT NULL,
    "t0400" decimal(32,2) DEFAULT NULL,
    "t0415" decimal(32,2) DEFAULT NULL,
    "t0430" decimal(32,2) DEFAULT NULL,
    "t0445" decimal(32,2) DEFAULT NULL,
    "t0500" decimal(32,2) DEFAULT NULL,
    "t0515" decimal(32,2) DEFAULT NULL,
    "t0530" decimal(32,2) DEFAULT NULL,
    "t0545" decimal(32,2) DEFAULT NULL,
    "t0600" decimal(32,2) DEFAULT NULL,
    "t0615" decimal(32,2) DEFAULT NULL,
    "t0630" decimal(32,2) DEFAULT NULL,
    "t0645" decimal(32,2) DEFAULT NULL,
    "t0700" decimal(32,2) DEFAULT NULL,
    "t0715" decimal(32,2) DEFAULT NULL,
    "t0730" decimal(32,2) DEFAULT NULL,
    "t0745" decimal(32,2) DEFAULT NULL,
    "t0800" decimal(32,2) DEFAULT NULL,
    "t0815" decimal(32,2) DEFAULT NULL,
    "t0830" decimal(32,2) DEFAULT NULL,
    "t0845" decimal(32,2) DEFAULT NULL,
    "t0900" decimal(32,2) DEFAULT NULL,
    "t0915" decimal(32,2) DEFAULT NULL,
    "t0930" decimal(32,2) DEFAULT NULL,
    "t0945" decimal(32,2) DEFAULT NULL,
    "t1000" decimal(32,2) DEFAULT NULL,
    "t1015" decimal(32,2) DEFAULT NULL,
    "t1030" decimal(32,2) DEFAULT NULL,
    "t1045" decimal(32,2) DEFAULT NULL,
    "t1100" decimal(32,2) DEFAULT NULL,
    "t1115" decimal(32,2) DEFAULT NULL,
    "t1130" decimal(32,2) DEFAULT NULL,
    "t1145" decimal(32,2) DEFAULT NULL,
    "t1200" decimal(32,2) DEFAULT NULL,
    "t1215" decimal(32,2) DEFAULT NULL,
    "t1230" decimal(32,2) DEFAULT NULL,
    "t1245" decimal(32,2) DEFAULT NULL,
    "t1300" decimal(32,2) DEFAULT NULL,
    "t1315" decimal(32,2) DEFAULT NULL,
    "t1330" decimal(32,2) DEFAULT NULL,
    "t1345" decimal(32,2) DEFAULT NULL,
    "t1400" decimal(32,2) DEFAULT NULL,
    "t1415" decimal(32,2) DEFAULT NULL,
    "t1430" decimal(32,2) DEFAULT NULL,
    "t1445" decimal(32,2) DEFAULT NULL,
    "t1500" decimal(32,2) DEFAULT NULL,
    "t1515" decimal(32,2) DEFAULT NULL,
    "t1530" decimal(32,2) DEFAULT NULL,
    "t1545" decimal(32,2) DEFAULT NULL,
    "t1600" decimal(32,2) DEFAULT NULL,
    "t1615" decimal(32,2) DEFAULT NULL,
    "t1630" decimal(32,2) DEFAULT NULL,
    "t1645" decimal(32,2) DEFAULT NULL,
    "t1700" decimal(32,2) DEFAULT NULL,
    "t1715" decimal(32,2) DEFAULT NULL,
    "t1730" decimal(32,2) DEFAULT NULL,
    "t1745" decimal(32,2) DEFAULT NULL,
    "t1800" decimal(32,2) DEFAULT NULL,
    "t1815" decimal(32,2) DEFAULT NULL,
    "t1830" decimal(32,2) DEFAULT NULL,
    "t1845" decimal(32,2) DEFAULT NULL,
    "t1900" decimal(32,2) DEFAULT NULL,
    "t1915" decimal(32,2) DEFAULT NULL,
    "t1930" decimal(32,2) DEFAULT NULL,
    "t1945" decimal(32,2) DEFAULT NULL,
    "t2000" decimal(32,2) DEFAULT NULL,
    "t2015" decimal(32,2) DEFAULT NULL,
    "t2030" decimal(32,2) DEFAULT NULL,
    "t2045" decimal(32,2) DEFAULT NULL,
    "t2100" decimal(32,2) DEFAULT NULL,
    "t2115" decimal(32,2) DEFAULT NULL,
    "t2130" decimal(32,2) DEFAULT NULL,
    "t2145" decimal(32,2) DEFAULT NULL,
    "t2200" decimal(32,2) DEFAULT NULL,
    "t2215" decimal(32,2) DEFAULT NULL,
    "t2230" decimal(32,2) DEFAULT NULL,
    "t2245" decimal(32,2) DEFAULT NULL,
    "t2300" decimal(32,2) DEFAULT NULL,
    "t2315" decimal(32,2) DEFAULT NULL,
    "t2330" decimal(32,2) DEFAULT NULL,
    "t2345" decimal(32,2) DEFAULT NULL,
    "t2400" decimal(32,2) DEFAULT NULL,
    "createtime" datetime DEFAULT CURRENT_TIMESTAMP,
    "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY ("id") USING BTREE,
    UNIQUE KEY "weather_city_station_fc_clct_zh" ("date","city_id","type") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='预测气象表';

CREATE TABLE "weather_city_station_fc_basic_zyt"
(
    "id" varchar(32) NOT NULL COMMENT '96点气象表ID',
    "date" date NOT NULL COMMENT '日期',
    "city_id" varchar(32) NOT NULL COMMENT '潮州	1\r\n东莞	2\r\n佛山	3\r\n广州	4\r\n河源	5\r\n惠州	6\r\n江门	7\r\n揭阳	8\r\n茂名	9\r\n梅州	10\r\n清远	11\r\n汕头	12\r\n汕尾	13\r\n韶关	14\r\n深圳	15\r\n阳江	16\r\n云浮	17\r\n湛江	18\r\n肇庆	19\r\n中山	20\r\n珠海	21\r\n',
    "type" tinyint(2) DEFAULT NULL COMMENT '气象类型(1：湿度，2：温度，3：降雨量，4：风速)',
    "t0000" decimal(32,2) DEFAULT NULL,
    "t0015" decimal(32,2) DEFAULT NULL,
    "t0030" decimal(32,2) DEFAULT NULL,
    "t0045" decimal(32,2) DEFAULT NULL,
    "t0100" decimal(32,2) DEFAULT NULL,
    "t0115" decimal(32,2) DEFAULT NULL,
    "t0130" decimal(32,2) DEFAULT NULL,
    "t0145" decimal(32,2) DEFAULT NULL,
    "t0200" decimal(32,2) DEFAULT NULL,
    "t0215" decimal(32,2) DEFAULT NULL,
    "t0230" decimal(32,2) DEFAULT NULL,
    "t0245" decimal(32,2) DEFAULT NULL,
    "t0300" decimal(32,2) DEFAULT NULL,
    "t0315" decimal(32,2) DEFAULT NULL,
    "t0330" decimal(32,2) DEFAULT NULL,
    "t0345" decimal(32,2) DEFAULT NULL,
    "t0400" decimal(32,2) DEFAULT NULL,
    "t0415" decimal(32,2) DEFAULT NULL,
    "t0430" decimal(32,2) DEFAULT NULL,
    "t0445" decimal(32,2) DEFAULT NULL,
    "t0500" decimal(32,2) DEFAULT NULL,
    "t0515" decimal(32,2) DEFAULT NULL,
    "t0530" decimal(32,2) DEFAULT NULL,
    "t0545" decimal(32,2) DEFAULT NULL,
    "t0600" decimal(32,2) DEFAULT NULL,
    "t0615" decimal(32,2) DEFAULT NULL,
    "t0630" decimal(32,2) DEFAULT NULL,
    "t0645" decimal(32,2) DEFAULT NULL,
    "t0700" decimal(32,2) DEFAULT NULL,
    "t0715" decimal(32,2) DEFAULT NULL,
    "t0730" decimal(32,2) DEFAULT NULL,
    "t0745" decimal(32,2) DEFAULT NULL,
    "t0800" decimal(32,2) DEFAULT NULL,
    "t0815" decimal(32,2) DEFAULT NULL,
    "t0830" decimal(32,2) DEFAULT NULL,
    "t0845" decimal(32,2) DEFAULT NULL,
    "t0900" decimal(32,2) DEFAULT NULL,
    "t0915" decimal(32,2) DEFAULT NULL,
    "t0930" decimal(32,2) DEFAULT NULL,
    "t0945" decimal(32,2) DEFAULT NULL,
    "t1000" decimal(32,2) DEFAULT NULL,
    "t1015" decimal(32,2) DEFAULT NULL,
    "t1030" decimal(32,2) DEFAULT NULL,
    "t1045" decimal(32,2) DEFAULT NULL,
    "t1100" decimal(32,2) DEFAULT NULL,
    "t1115" decimal(32,2) DEFAULT NULL,
    "t1130" decimal(32,2) DEFAULT NULL,
    "t1145" decimal(32,2) DEFAULT NULL,
    "t1200" decimal(32,2) DEFAULT NULL,
    "t1215" decimal(32,2) DEFAULT NULL,
    "t1230" decimal(32,2) DEFAULT NULL,
    "t1245" decimal(32,2) DEFAULT NULL,
    "t1300" decimal(32,2) DEFAULT NULL,
    "t1315" decimal(32,2) DEFAULT NULL,
    "t1330" decimal(32,2) DEFAULT NULL,
    "t1345" decimal(32,2) DEFAULT NULL,
    "t1400" decimal(32,2) DEFAULT NULL,
    "t1415" decimal(32,2) DEFAULT NULL,
    "t1430" decimal(32,2) DEFAULT NULL,
    "t1445" decimal(32,2) DEFAULT NULL,
    "t1500" decimal(32,2) DEFAULT NULL,
    "t1515" decimal(32,2) DEFAULT NULL,
    "t1530" decimal(32,2) DEFAULT NULL,
    "t1545" decimal(32,2) DEFAULT NULL,
    "t1600" decimal(32,2) DEFAULT NULL,
    "t1615" decimal(32,2) DEFAULT NULL,
    "t1630" decimal(32,2) DEFAULT NULL,
    "t1645" decimal(32,2) DEFAULT NULL,
    "t1700" decimal(32,2) DEFAULT NULL,
    "t1715" decimal(32,2) DEFAULT NULL,
    "t1730" decimal(32,2) DEFAULT NULL,
    "t1745" decimal(32,2) DEFAULT NULL,
    "t1800" decimal(32,2) DEFAULT NULL,
    "t1815" decimal(32,2) DEFAULT NULL,
    "t1830" decimal(32,2) DEFAULT NULL,
    "t1845" decimal(32,2) DEFAULT NULL,
    "t1900" decimal(32,2) DEFAULT NULL,
    "t1915" decimal(32,2) DEFAULT NULL,
    "t1930" decimal(32,2) DEFAULT NULL,
    "t1945" decimal(32,2) DEFAULT NULL,
    "t2000" decimal(32,2) DEFAULT NULL,
    "t2015" decimal(32,2) DEFAULT NULL,
    "t2030" decimal(32,2) DEFAULT NULL,
    "t2045" decimal(32,2) DEFAULT NULL,
    "t2100" decimal(32,2) DEFAULT NULL,
    "t2115" decimal(32,2) DEFAULT NULL,
    "t2130" decimal(32,2) DEFAULT NULL,
    "t2145" decimal(32,2) DEFAULT NULL,
    "t2200" decimal(32,2) DEFAULT NULL,
    "t2215" decimal(32,2) DEFAULT NULL,
    "t2230" decimal(32,2) DEFAULT NULL,
    "t2245" decimal(32,2) DEFAULT NULL,
    "t2300" decimal(32,2) DEFAULT NULL,
    "t2315" decimal(32,2) DEFAULT NULL,
    "t2330" decimal(32,2) DEFAULT NULL,
    "t2345" decimal(32,2) DEFAULT NULL,
    "t2400" decimal(32,2) DEFAULT NULL,
    "createtime" datetime DEFAULT CURRENT_TIMESTAMP,
    "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY ("id") USING BTREE,
    UNIQUE KEY "weather_city_station_fc_clct_zyt" ("date","city_id","type") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='预测气象表';

CREATE TABLE "weather_city_station_his_basic"
(
    "id" varchar(32) NOT NULL COMMENT '96点气象表ID',
    "date" date NOT NULL COMMENT '日期',
    "city_id" varchar(32) NOT NULL COMMENT '潮州	1\r\n东莞	2\r\n佛山	3\r\n广州	4\r\n河源	5\r\n惠州	6\r\n江门	7\r\n揭阳	8\r\n茂名	9\r\n梅州	10\r\n清远	11\r\n汕头	12\r\n汕尾	13\r\n韶关	14\r\n深圳	15\r\n阳江	16\r\n云浮	17\r\n湛江	18\r\n肇庆	19\r\n中山	20\r\n珠海	21\r\n',
    "type" tinyint(2) DEFAULT NULL COMMENT '气象类型(1：湿度，2：温度，3：降雨量，4：风速)',
    "t0000" decimal(32,2) DEFAULT NULL,
    "t0015" decimal(32,2) DEFAULT NULL,
    "t0030" decimal(32,2) DEFAULT NULL,
    "t0045" decimal(32,2) DEFAULT NULL,
    "t0100" decimal(32,2) DEFAULT NULL,
    "t0115" decimal(32,2) DEFAULT NULL,
    "t0130" decimal(32,2) DEFAULT NULL,
    "t0145" decimal(32,2) DEFAULT NULL,
    "t0200" decimal(32,2) DEFAULT NULL,
    "t0215" decimal(32,2) DEFAULT NULL,
    "t0230" decimal(32,2) DEFAULT NULL,
    "t0245" decimal(32,2) DEFAULT NULL,
    "t0300" decimal(32,2) DEFAULT NULL,
    "t0315" decimal(32,2) DEFAULT NULL,
    "t0330" decimal(32,2) DEFAULT NULL,
    "t0345" decimal(32,2) DEFAULT NULL,
    "t0400" decimal(32,2) DEFAULT NULL,
    "t0415" decimal(32,2) DEFAULT NULL,
    "t0430" decimal(32,2) DEFAULT NULL,
    "t0445" decimal(32,2) DEFAULT NULL,
    "t0500" decimal(32,2) DEFAULT NULL,
    "t0515" decimal(32,2) DEFAULT NULL,
    "t0530" decimal(32,2) DEFAULT NULL,
    "t0545" decimal(32,2) DEFAULT NULL,
    "t0600" decimal(32,2) DEFAULT NULL,
    "t0615" decimal(32,2) DEFAULT NULL,
    "t0630" decimal(32,2) DEFAULT NULL,
    "t0645" decimal(32,2) DEFAULT NULL,
    "t0700" decimal(32,2) DEFAULT NULL,
    "t0715" decimal(32,2) DEFAULT NULL,
    "t0730" decimal(32,2) DEFAULT NULL,
    "t0745" decimal(32,2) DEFAULT NULL,
    "t0800" decimal(32,2) DEFAULT NULL,
    "t0815" decimal(32,2) DEFAULT NULL,
    "t0830" decimal(32,2) DEFAULT NULL,
    "t0845" decimal(32,2) DEFAULT NULL,
    "t0900" decimal(32,2) DEFAULT NULL,
    "t0915" decimal(32,2) DEFAULT NULL,
    "t0930" decimal(32,2) DEFAULT NULL,
    "t0945" decimal(32,2) DEFAULT NULL,
    "t1000" decimal(32,2) DEFAULT NULL,
    "t1015" decimal(32,2) DEFAULT NULL,
    "t1030" decimal(32,2) DEFAULT NULL,
    "t1045" decimal(32,2) DEFAULT NULL,
    "t1100" decimal(32,2) DEFAULT NULL,
    "t1115" decimal(32,2) DEFAULT NULL,
    "t1130" decimal(32,2) DEFAULT NULL,
    "t1145" decimal(32,2) DEFAULT NULL,
    "t1200" decimal(32,2) DEFAULT NULL,
    "t1215" decimal(32,2) DEFAULT NULL,
    "t1230" decimal(32,2) DEFAULT NULL,
    "t1245" decimal(32,2) DEFAULT NULL,
    "t1300" decimal(32,2) DEFAULT NULL,
    "t1315" decimal(32,2) DEFAULT NULL,
    "t1330" decimal(32,2) DEFAULT NULL,
    "t1345" decimal(32,2) DEFAULT NULL,
    "t1400" decimal(32,2) DEFAULT NULL,
    "t1415" decimal(32,2) DEFAULT NULL,
    "t1430" decimal(32,2) DEFAULT NULL,
    "t1445" decimal(32,2) DEFAULT NULL,
    "t1500" decimal(32,2) DEFAULT NULL,
    "t1515" decimal(32,2) DEFAULT NULL,
    "t1530" decimal(32,2) DEFAULT NULL,
    "t1545" decimal(32,2) DEFAULT NULL,
    "t1600" decimal(32,2) DEFAULT NULL,
    "t1615" decimal(32,2) DEFAULT NULL,
    "t1630" decimal(32,2) DEFAULT NULL,
    "t1645" decimal(32,2) DEFAULT NULL,
    "t1700" decimal(32,2) DEFAULT NULL,
    "t1715" decimal(32,2) DEFAULT NULL,
    "t1730" decimal(32,2) DEFAULT NULL,
    "t1745" decimal(32,2) DEFAULT NULL,
    "t1800" decimal(32,2) DEFAULT NULL,
    "t1815" decimal(32,2) DEFAULT NULL,
    "t1830" decimal(32,2) DEFAULT NULL,
    "t1845" decimal(32,2) DEFAULT NULL,
    "t1900" decimal(32,2) DEFAULT NULL,
    "t1915" decimal(32,2) DEFAULT NULL,
    "t1930" decimal(32,2) DEFAULT NULL,
    "t1945" decimal(32,2) DEFAULT NULL,
    "t2000" decimal(32,2) DEFAULT NULL,
    "t2015" decimal(32,2) DEFAULT NULL,
    "t2030" decimal(32,2) DEFAULT NULL,
    "t2045" decimal(32,2) DEFAULT NULL,
    "t2100" decimal(32,2) DEFAULT NULL,
    "t2115" decimal(32,2) DEFAULT NULL,
    "t2130" decimal(32,2) DEFAULT NULL,
    "t2145" decimal(32,2) DEFAULT NULL,
    "t2200" decimal(32,2) DEFAULT NULL,
    "t2215" decimal(32,2) DEFAULT NULL,
    "t2230" decimal(32,2) DEFAULT NULL,
    "t2245" decimal(32,2) DEFAULT NULL,
    "t2300" decimal(32,2) DEFAULT NULL,
    "t2315" decimal(32,2) DEFAULT NULL,
    "t2330" decimal(32,2) DEFAULT NULL,
    "t2345" decimal(32,2) DEFAULT NULL,
    "t2400" decimal(32,2) DEFAULT NULL,
    "createtime" datetime DEFAULT CURRENT_TIMESTAMP,
    "updatetime" datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY ("id") USING BTREE,
    UNIQUE KEY "weather_city_station_his_clct" ("date","city_id","type") USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='实际气象表';
